#include "NetworkError.h"
#include <QNetworkReply>

NetworkError::NetworkError()
    : m_type(Type::None)
    , m_httpCode(0)
    , m_networkError(QNetworkReply::NoError)
    , m_severity(Severity::Info)
{
}

NetworkError::NetworkError(Type type, const QString& message, int httpCode)
    : m_type(type)
    , m_message(message)
    , m_httpCode(httpCode)
    , m_networkError(QNetworkReply::NoError)
    , m_severity(Severity::Error)
{
    determineSeverity();
}

NetworkError::NetworkError(QNetworkReply::NetworkError networkError, const QString& message)
    : m_type(Type::NetworkError)
    , m_message(message)
    , m_httpCode(0)
    , m_networkError(networkError)
    , m_severity(Severity::Error)
{
    determineSeverity();
}

QString NetworkError::toString() const
{
    if (m_type == Type::None) {
        return "No error";
    }
    
    QString result = QString("[%1] %2: %3")
                        .arg(severityString())
                        .arg(typeString())
                        .arg(m_message);
    
    if (m_httpCode > 0) {
        result += QString(" (HTTP %1)").arg(m_httpCode);
    }
    
    return result;
}

QString NetworkError::typeString() const
{
    switch (m_type) {
        case Type::None: return "None";
        case Type::NetworkError: return "Network Error";
        case Type::HttpError: return "HTTP Error";
        case Type::JsonParseError: return "JSON Parse Error";
        case Type::AuthenticationError: return "Authentication Error";
        case Type::AuthorizationError: return "Authorization Error";
        case Type::ValidationError: return "Validation Error";
        case Type::ServerError: return "Server Error";
        case Type::TimeoutError: return "Timeout Error";
        case Type::UnknownError: return "Unknown Error";
    }
    return "Unknown";
}

QString NetworkError::severityString() const
{
    switch (m_severity) {
        case Severity::Info: return "INFO";
        case Severity::Warning: return "WARNING";
        case Severity::Error: return "ERROR";
        case Severity::Critical: return "CRITICAL";
    }
    return "UNKNOWN";
}

bool NetworkError::isRetryable() const
{
    switch (m_type) {
        case Type::TimeoutError:
        case Type::NetworkError:
            return true;
        case Type::HttpError:
            return m_httpCode >= 500 && m_httpCode < 600; // Server errors are retryable
        default:
            return false;
    }
}

bool NetworkError::requiresAuthentication() const
{
    return m_type == Type::AuthenticationError || m_httpCode == 401;
}

NetworkError NetworkError::fromHttpCode(int httpCode, const QString& message)
{
    Type type;
    QString defaultMessage = message;
    
    if (httpCode >= 200 && httpCode < 300) {
        return NetworkError(); // Success, no error
    } else if (httpCode == 401) {
        type = Type::AuthenticationError;
        if (defaultMessage.isEmpty()) defaultMessage = "Authentication required";
    } else if (httpCode == 403) {
        type = Type::AuthorizationError;
        if (defaultMessage.isEmpty()) defaultMessage = "Access forbidden";
    } else if (httpCode == 400) {
        type = Type::ValidationError;
        if (defaultMessage.isEmpty()) defaultMessage = "Bad request";
    } else if (httpCode >= 500) {
        type = Type::ServerError;
        if (defaultMessage.isEmpty()) defaultMessage = "Server error";
    } else {
        type = Type::HttpError;
        if (defaultMessage.isEmpty()) defaultMessage = QString("HTTP error %1").arg(httpCode);
    }
    
    return NetworkError(type, defaultMessage, httpCode);
}

NetworkError NetworkError::fromNetworkReply(QNetworkReply* reply)
{
    if (!reply) {
        return NetworkError(Type::UnknownError, "Null network reply");
    }
    
    QNetworkReply::NetworkError networkError = reply->error();
    if (networkError == QNetworkReply::NoError) {
        return NetworkError(); // No error
    }
    
    QString message = reply->errorString();
    int httpCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    
    if (httpCode > 0) {
        return fromHttpCode(httpCode, message);
    } else {
        return NetworkError(networkError, message);
    }
}

NetworkError NetworkError::authenticationRequired(const QString& message)
{
    return NetworkError(Type::AuthenticationError, 
                       message.isEmpty() ? "Authentication required" : message, 401);
}

NetworkError NetworkError::serverError(const QString& message)
{
    return NetworkError(Type::ServerError, 
                       message.isEmpty() ? "Internal server error" : message, 500);
}

NetworkError NetworkError::timeout(const QString& message)
{
    return NetworkError(Type::TimeoutError, 
                       message.isEmpty() ? "Request timeout" : message);
}

NetworkError NetworkError::jsonParseError(const QString& message)
{
    return NetworkError(Type::JsonParseError, 
                       message.isEmpty() ? "Failed to parse JSON response" : message);
}

void NetworkError::determineSeverity()
{
    switch (m_type) {
        case Type::None:
            m_severity = Severity::Info;
            break;
        case Type::ValidationError:
        case Type::AuthenticationError:
        case Type::AuthorizationError:
            m_severity = Severity::Warning;
            break;
        case Type::ServerError:
        case Type::TimeoutError:
            m_severity = Severity::Critical;
            break;
        default:
            m_severity = Severity::Error;
            break;
    }
}
