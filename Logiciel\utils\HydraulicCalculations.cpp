#include "HydraulicCalculations.h"

// Constants
const double HydraulicCalculations::ATMOSPHERIC_PRESSURE_BAR = 1.01325;
const double HydraulicCalculations::WATER_DENSITY_KG_M3 = 1000.0;
const double HydraulicCalculations::HYDRAULIC_OIL_DENSITY_KG_M3 = 850.0;
const double HydraulicCalculations::GRAVITY_MS2 = 9.81;
const double HydraulicCalculations::PI = M_PI;

// Pressure calculations
double HydraulicCalculations::pressureDifferential(double pressureA, double pressureB)
{
    return qAbs(pressureA - pressureB);
}

double HydraulicCalculations::pressureRatio(double pressure1, double pressure2)
{
    if (pressure2 == 0.0) return 0.0;
    return pressure1 / pressure2;
}

double HydraulicCalculations::pressureFromForce(double force, double area)
{
    if (area == 0.0) return 0.0;
    return (force / area) / 100000.0; // Convert Pa to bar
}

double HydraulicCalculations::forceFromPressure(double pressure, double area)
{
    return pressure * 100000.0 * area; // Convert bar to Pa, then multiply by area
}

// Flow calculations
double HydraulicCalculations::flowFromVelocity(double velocity, double area)
{
    // velocity in mm/s, area in mm², result in L/min
    double flowM3s = (velocity / 1000.0) * (area / 1000000.0); // Convert to m/s and m²
    return flowM3s * 60000.0; // Convert m³/s to L/min
}

double HydraulicCalculations::velocityFromFlow(double flow, double area)
{
    // flow in L/min, area in mm², result in mm/s
    if (area == 0.0) return 0.0;
    double flowM3s = flow / 60000.0; // Convert L/min to m³/s
    double velocityMs = flowM3s / (area / 1000000.0); // Convert area to m²
    return velocityMs * 1000.0; // Convert m/s to mm/s
}

double HydraulicCalculations::reynoldsNumber(double velocity, double diameter, double kinematicViscosity)
{
    if (kinematicViscosity == 0.0) return 0.0;
    // velocity in m/s, diameter in m, kinematic viscosity in m²/s
    return (velocity * diameter) / kinematicViscosity;
}

double HydraulicCalculations::flowCoefficient(double actualFlow, double theoreticalFlow)
{
    if (theoreticalFlow == 0.0) return 0.0;
    return actualFlow / theoreticalFlow;
}

// Power calculations
double HydraulicCalculations::hydraulicPower(double pressure, double flow)
{
    // pressure in bar, flow in L/min, result in kW
    double pressurePa = pressure * 100000.0; // Convert bar to Pa
    double flowM3s = flow / 60000.0; // Convert L/min to m³/s
    return (pressurePa * flowM3s) / 1000.0; // Convert W to kW
}

double HydraulicCalculations::mechanicalPower(double force, double velocity)
{
    // force in N, velocity in mm/s, result in kW
    double velocityMs = velocity / 1000.0; // Convert mm/s to m/s
    return (force * velocityMs) / 1000.0; // Convert W to kW
}

double HydraulicCalculations::powerLoss(double hydraulicPower, double mechanicalPower)
{
    return hydraulicPower - mechanicalPower;
}

// Efficiency calculations
double HydraulicCalculations::volumetricEfficiency(double actualFlow, double theoreticalFlow)
{
    if (theoreticalFlow == 0.0) return 0.0;
    return (actualFlow / theoreticalFlow) * 100.0;
}

double HydraulicCalculations::mechanicalEfficiency(double mechanicalPower, double hydraulicPower)
{
    if (hydraulicPower == 0.0) return 0.0;
    return (mechanicalPower / hydraulicPower) * 100.0;
}

double HydraulicCalculations::overallEfficiency(double volumetricEff, double mechanicalEff)
{
    return (volumetricEff * mechanicalEff) / 100.0;
}

// Cylinder calculations
double HydraulicCalculations::cylinderArea(double diameter)
{
    // diameter in mm, result in mm²
    return PI * qPow(diameter / 2.0, 2);
}

double HydraulicCalculations::cylinderVolume(double diameter, double stroke)
{
    // diameter and stroke in mm, result in cm³
    double areaM2 = cylinderArea(diameter) / 1000000.0; // Convert mm² to m²
    double strokeM = stroke / 1000.0; // Convert mm to m
    return (areaM2 * strokeM) * 1000000.0; // Convert m³ to cm³
}

double HydraulicCalculations::rodArea(double rodDiameter)
{
    return cylinderArea(rodDiameter);
}

double HydraulicCalculations::annularArea(double cylinderDiameter, double rodDiameter)
{
    return cylinderArea(cylinderDiameter) - cylinderArea(rodDiameter);
}

// Velocity and acceleration
double HydraulicCalculations::averageVelocity(double distance, double time)
{
    if (time == 0.0) return 0.0;
    return distance / time;
}

double HydraulicCalculations::instantaneousVelocity(double position1, double position2, double deltaTime)
{
    if (deltaTime == 0.0) return 0.0;
    return (position2 - position1) / deltaTime;
}

double HydraulicCalculations::acceleration(double velocity1, double velocity2, double deltaTime)
{
    if (deltaTime == 0.0) return 0.0;
    return (velocity2 - velocity1) / deltaTime;
}

// Temperature effects
double HydraulicCalculations::viscosityTemperatureCorrection(double viscosity, double temp1, double temp2)
{
    // Simplified viscosity-temperature relationship
    double tempRatio = (temp2 + 273.15) / (temp1 + 273.15);
    return viscosity * qPow(tempRatio, -0.8);
}

double HydraulicCalculations::densityTemperatureCorrection(double density, double temp1, double temp2)
{
    // Simplified density-temperature relationship for hydraulic oil
    double tempDiff = temp2 - temp1;
    double correctionFactor = 1.0 - (0.0007 * tempDiff); // Typical coefficient for hydraulic oil
    return density * correctionFactor;
}

double HydraulicCalculations::bulkModulusTemperatureCorrection(double bulkModulus, double temp1, double temp2)
{
    // Simplified bulk modulus-temperature relationship
    double tempDiff = temp2 - temp1;
    double correctionFactor = 1.0 - (0.005 * tempDiff); // Typical coefficient
    return bulkModulus * correctionFactor;
}

// Leakage calculations
double HydraulicCalculations::leakageRate(double pressure1, double pressure2, double time)
{
    if (time == 0.0) return 0.0;
    return (pressure1 - pressure2) / time; // Simplified leakage rate
}

double HydraulicCalculations::leakageCoefficient(double leakageRate, double pressureDiff)
{
    if (pressureDiff == 0.0) return 0.0;
    return leakageRate / pressureDiff;
}

bool HydraulicCalculations::isLeakageAcceptable(double leakageRate, double maxAllowable)
{
    return leakageRate <= maxAllowable;
}

// Unit conversions
double HydraulicCalculations::barToPsi(double bar)
{
    return bar * 14.5038;
}

double HydraulicCalculations::psiToBar(double psi)
{
    return psi / 14.5038;
}

double HydraulicCalculations::barToPascal(double bar)
{
    return bar * 100000.0;
}

double HydraulicCalculations::pascalToBar(double pascal)
{
    return pascal / 100000.0;
}

double HydraulicCalculations::lpmToGpm(double lpm)
{
    return lpm * 0.264172;
}

double HydraulicCalculations::gpmToLpm(double gpm)
{
    return gpm / 0.264172;
}

double HydraulicCalculations::lpmToCubicMeterPerSecond(double lpm)
{
    return lpm / 60000.0;
}

double HydraulicCalculations::cubicMeterPerSecondToLpm(double cms)
{
    return cms * 60000.0;
}

double HydraulicCalculations::celsiusToFahrenheit(double celsius)
{
    return (celsius * 9.0 / 5.0) + 32.0;
}

double HydraulicCalculations::fahrenheitToCelsius(double fahrenheit)
{
    return (fahrenheit - 32.0) * 5.0 / 9.0;
}

double HydraulicCalculations::mmToInch(double mm)
{
    return mm / 25.4;
}

double HydraulicCalculations::inchToMm(double inch)
{
    return inch * 25.4;
}

// HydraulicTestAnalyzer implementation
HydraulicTestAnalyzer::HydraulicTestAnalyzer(const HydraulicSystemParameters& params)
    : m_params(params)
{
}

TestData HydraulicTestAnalyzer::analyzeDataPoint(const TestData& rawData)
{
    TestData analyzed = rawData;
    
    // Calculate derived values
    double cylinderArea = HydraulicCalculations::cylinderArea(m_params.cylinderDiameter);
    double rodArea = HydraulicCalculations::rodArea(m_params.rodDiameter);
    double annularArea = HydraulicCalculations::annularArea(m_params.cylinderDiameter, m_params.rodDiameter);
    
    // Calculate theoretical flow
    double theoreticalFlow = calculateTheoreticalFlow(rawData.actuatorVelocity());
    
    // Calculate efficiencies
    double volEff = HydraulicCalculations::volumetricEfficiency(rawData.flowRate(), theoreticalFlow);
    double hydPower = HydraulicCalculations::hydraulicPower(rawData.pressureCPA(), rawData.flowRate());
    double mechPower = HydraulicCalculations::mechanicalPower(rawData.actuatorForce(), rawData.actuatorVelocity());
    double mechEff = HydraulicCalculations::mechanicalEfficiency(mechPower, hydPower);
    double overallEff = HydraulicCalculations::overallEfficiency(volEff, mechEff);
    
    // Set calculated values
    analyzed.setPowerHydraulic(hydPower);
    analyzed.setEfficiency(overallEff);
    analyzed.setParameter("volumetric_efficiency", volEff);
    analyzed.setParameter("mechanical_efficiency", mechEff);
    analyzed.setParameter("theoretical_flow", theoreticalFlow);
    
    return analyzed;
}

double HydraulicTestAnalyzer::calculateTheoreticalFlow(double velocity)
{
    double cylinderArea = HydraulicCalculations::cylinderArea(m_params.cylinderDiameter);
    return HydraulicCalculations::flowFromVelocity(velocity, cylinderArea);
}

void HydraulicTestAnalyzer::setSystemParameters(const HydraulicSystemParameters& params)
{
    m_params = params;
}
