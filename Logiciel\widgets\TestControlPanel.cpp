#include "TestControlPanel.h"
#include <QDebug>
#include <QMessageBox>
#include <QApplication>

Q_LOGGING_CATEGORY(testControlPanelLog, "hydraulic.widgets.testcontrol")

TestControlPanel::TestControlPanel(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_currentStatus(TestSessionManager::SessionStatus::Idle)
    , m_updateTimer(new QTimer(this))
{
    setupUI();
    
    // Setup update timer
    m_updateTimer->setInterval(UPDATE_INTERVAL_MS);
    connect(m_updateTimer, &QTimer::timeout, this, &TestControlPanel::onUpdateTimer);
    m_updateTimer->start();
    
    // Initialize UI state
    updateControlsState();
    
    qCDebug(testControlPanelLog) << "TestControlPanel initialized";
}

void TestControlPanel::setTestSessionManager(std::shared_ptr<TestSessionManager> sessionManager)
{
    // Disconnect previous session manager
    if (m_sessionManager) {
        disconnect(m_sessionManager.get(), nullptr, this, nullptr);
    }
    
    m_sessionManager = sessionManager;
    
    // Connect new session manager signals
    if (m_sessionManager) {
        connect(m_sessionManager.get(), &TestSessionManager::sessionStarted,
                this, &TestControlPanel::onSessionStarted);
        connect(m_sessionManager.get(), &TestSessionManager::sessionStopped,
                this, &TestControlPanel::onSessionStopped);
        connect(m_sessionManager.get(), &TestSessionManager::sessionPaused,
                this, &TestControlPanel::onSessionPaused);
        connect(m_sessionManager.get(), &TestSessionManager::sessionResumed,
                this, &TestControlPanel::onSessionResumed);
        connect(m_sessionManager.get(), &TestSessionManager::sessionCompleted,
                this, &TestControlPanel::onSessionCompleted);
        connect(m_sessionManager.get(), &TestSessionManager::sessionFailed,
                this, &TestControlPanel::onSessionFailed);
        connect(m_sessionManager.get(), &TestSessionManager::sessionCancelled,
                this, &TestControlPanel::onSessionCancelled);
        connect(m_sessionManager.get(), &TestSessionManager::sessionStatusChanged,
                this, &TestControlPanel::onSessionStatusChanged);
        connect(m_sessionManager.get(), &TestSessionManager::sessionProgressChanged,
                this, &TestControlPanel::onSessionProgressChanged);
        connect(m_sessionManager.get(), &TestSessionManager::sessionPhaseChanged,
                this, &TestControlPanel::onSessionPhaseChanged);
        connect(m_sessionManager.get(), &TestSessionManager::sessionStatisticsUpdated,
                this, &TestControlPanel::onSessionStatisticsUpdated);
        connect(m_sessionManager.get(), &TestSessionManager::sessionError,
                this, &TestControlPanel::onSessionError);
        connect(m_sessionManager.get(), &TestSessionManager::criticalSessionError,
                this, &TestControlPanel::onCriticalSessionError);
    }
    
    updateControlsState();
    qCDebug(testControlPanelLog) << "Test session manager set";
}

void TestControlPanel::setTestConfiguration(std::shared_ptr<TestConfiguration> config)
{
    m_testConfig = config;
    updateQuickConfigDisplay();
    qCDebug(testControlPanelLog) << "Test configuration set";
}

void TestControlPanel::setCurrentEssai(std::shared_ptr<Essai> essai)
{
    m_currentEssai = essai;
    updateQuickConfigDisplay();
    qCDebug(testControlPanelLog) << "Current essai set";
}

void TestControlPanel::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    
    setupControlButtons();
    setupProgressSection();
    setupStatusSection();
    setupQuickConfigSection();
    setupStatisticsSection();
}

void TestControlPanel::setupControlButtons()
{
    m_controlGroup = new QGroupBox("Test Control", this);
    QGridLayout* layout = new QGridLayout(m_controlGroup);
    
    // Main control buttons
    m_startButton = new QPushButton("Start Test", this);
    m_startButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");
    m_startButton->setMinimumHeight(50);
    connect(m_startButton, &QPushButton::clicked, this, &TestControlPanel::onStartButtonClicked);
    layout->addWidget(m_startButton, 0, 0);
    
    m_stopButton = new QPushButton("Stop Test", this);
    m_stopButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }");
    m_stopButton->setMinimumHeight(50);
    m_stopButton->setEnabled(false);
    connect(m_stopButton, &QPushButton::clicked, this, &TestControlPanel::onStopButtonClicked);
    layout->addWidget(m_stopButton, 0, 1);
    
    m_pauseResumeButton = new QPushButton("Pause", this);
    m_pauseResumeButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 10px; }");
    m_pauseResumeButton->setMinimumHeight(50);
    m_pauseResumeButton->setEnabled(false);
    connect(m_pauseResumeButton, &QPushButton::clicked, this, &TestControlPanel::onPauseResumeButtonClicked);
    layout->addWidget(m_pauseResumeButton, 0, 2);
    
    // Secondary control buttons
    m_cancelButton = new QPushButton("Cancel", this);
    m_cancelButton->setEnabled(false);
    connect(m_cancelButton, &QPushButton::clicked, this, &TestControlPanel::onCancelButtonClicked);
    layout->addWidget(m_cancelButton, 1, 0);
    
    m_configureButton = new QPushButton("Configure", this);
    connect(m_configureButton, &QPushButton::clicked, this, &TestControlPanel::onConfigureButtonClicked);
    layout->addWidget(m_configureButton, 1, 1);
    
    m_resetButton = new QPushButton("Reset", this);
    connect(m_resetButton, &QPushButton::clicked, this, &TestControlPanel::onResetButtonClicked);
    layout->addWidget(m_resetButton, 1, 2);
    
    // Emergency stop button
    m_emergencyStopButton = new QPushButton("EMERGENCY STOP", this);
    m_emergencyStopButton->setStyleSheet("QPushButton { background-color: #D32F2F; color: white; font-weight: bold; font-size: 14px; padding: 15px; }");
    m_emergencyStopButton->setMinimumHeight(60);
    connect(m_emergencyStopButton, &QPushButton::clicked, this, &TestControlPanel::onEmergencyStopClicked);
    layout->addWidget(m_emergencyStopButton, 2, 0, 1, 3);
    
    m_mainLayout->addWidget(m_controlGroup);
}

void TestControlPanel::setupProgressSection()
{
    m_progressGroup = new QGroupBox("Test Progress", this);
    QVBoxLayout* layout = new QVBoxLayout(m_progressGroup);
    
    // Progress bar
    m_progressBar = new QProgressBar(this);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setTextVisible(true);
    layout->addWidget(m_progressBar);
    
    // Progress labels
    QHBoxLayout* labelsLayout = new QHBoxLayout();
    
    m_progressLabel = new QLabel("0%", this);
    m_progressLabel->setAlignment(Qt::AlignLeft);
    labelsLayout->addWidget(m_progressLabel);
    
    m_phaseLabel = new QLabel("Idle", this);
    m_phaseLabel->setAlignment(Qt::AlignCenter);
    m_phaseLabel->setStyleSheet("font-weight: bold;");
    labelsLayout->addWidget(m_phaseLabel);
    
    labelsLayout->addStretch();
    layout->addLayout(labelsLayout);
    
    // Time labels
    QHBoxLayout* timeLayout = new QHBoxLayout();
    
    timeLayout->addWidget(new QLabel("Elapsed:", this));
    m_timeElapsedLabel = new QLabel("00:00:00", this);
    m_timeElapsedLabel->setStyleSheet("font-family: monospace;");
    timeLayout->addWidget(m_timeElapsedLabel);
    
    timeLayout->addStretch();
    
    timeLayout->addWidget(new QLabel("Remaining:", this));
    m_timeRemainingLabel = new QLabel("--:--:--", this);
    m_timeRemainingLabel->setStyleSheet("font-family: monospace;");
    timeLayout->addWidget(m_timeRemainingLabel);
    
    layout->addLayout(timeLayout);
    
    m_mainLayout->addWidget(m_progressGroup);
}

void TestControlPanel::setupStatusSection()
{
    m_statusGroup = new QGroupBox("Status", this);
    QVBoxLayout* layout = new QVBoxLayout(m_statusGroup);
    
    // Status display
    QHBoxLayout* statusLayout = new QHBoxLayout();
    
    m_statusIcon = new QLabel(this);
    m_statusIcon->setFixedSize(24, 24);
    m_statusIcon->setStyleSheet("background-color: gray; border-radius: 12px;");
    statusLayout->addWidget(m_statusIcon);
    
    m_statusLabel = new QLabel("Idle", this);
    m_statusLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
    statusLayout->addWidget(m_statusLabel);
    
    statusLayout->addStretch();
    layout->addLayout(statusLayout);
    
    // Status messages
    m_statusMessages = new QTextEdit(this);
    m_statusMessages->setMaximumHeight(100);
    m_statusMessages->setReadOnly(true);
    m_statusMessages->setFont(QFont("Courier", 9));
    layout->addWidget(m_statusMessages);
    
    // Clear messages button
    m_clearMessagesButton = new QPushButton("Clear Messages", this);
    connect(m_clearMessagesButton, &QPushButton::clicked, 
            m_statusMessages, &QTextEdit::clear);
    layout->addWidget(m_clearMessagesButton);
    
    m_mainLayout->addWidget(m_statusGroup);
}

void TestControlPanel::setupQuickConfigSection()
{
    m_quickConfigGroup = new QGroupBox("Quick Configuration", this);
    QGridLayout* layout = new QGridLayout(m_quickConfigGroup);
    
    // Test type
    layout->addWidget(new QLabel("Test Type:", this), 0, 0);
    m_testTypeComboBox = new QComboBox(this);
    m_testTypeComboBox->addItems({"Pressure Test", "Flow Test", "Leakage Test", "Cycle Test", "Performance Test"});
    connect(m_testTypeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TestControlPanel::onTestTypeChanged);
    layout->addWidget(m_testTypeComboBox, 0, 1);
    
    // Test duration
    layout->addWidget(new QLabel("Duration (s):", this), 0, 2);
    m_testDurationSpinBox = new QSpinBox(this);
    m_testDurationSpinBox->setRange(1, 3600);
    m_testDurationSpinBox->setValue(60);
    m_testDurationSpinBox->setSuffix(" s");
    connect(m_testDurationSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestControlPanel::onTestDurationChanged);
    layout->addWidget(m_testDurationSpinBox, 0, 3);
    
    // Pressure target
    layout->addWidget(new QLabel("Pressure Target:", this), 1, 0);
    m_pressureTargetSpinBox = new QDoubleSpinBox(this);
    m_pressureTargetSpinBox->setRange(0.0, 300.0);
    m_pressureTargetSpinBox->setValue(150.0);
    m_pressureTargetSpinBox->setSuffix(" bar");
    layout->addWidget(m_pressureTargetSpinBox, 1, 1);
    
    // Flow target
    layout->addWidget(new QLabel("Flow Target:", this), 1, 2);
    m_flowTargetSpinBox = new QDoubleSpinBox(this);
    m_flowTargetSpinBox->setRange(0.0, 200.0);
    m_flowTargetSpinBox->setValue(50.0);
    m_flowTargetSpinBox->setSuffix(" L/min");
    layout->addWidget(m_flowTargetSpinBox, 1, 3);
    
    // Apply button
    m_applyConfigButton = new QPushButton("Apply Configuration", this);
    connect(m_applyConfigButton, &QPushButton::clicked, this, &TestControlPanel::configurationChangeRequested);
    layout->addWidget(m_applyConfigButton, 2, 0, 1, 4);
    
    m_mainLayout->addWidget(m_quickConfigGroup);
}

void TestControlPanel::setupStatisticsSection()
{
    m_statisticsGroup = new QGroupBox("Session Statistics", this);
    QGridLayout* layout = new QGridLayout(m_statisticsGroup);
    
    // Data points
    layout->addWidget(new QLabel("Total Data Points:", this), 0, 0);
    m_totalDataPointsLabel = new QLabel("0", this);
    m_totalDataPointsLabel->setStyleSheet("font-weight: bold;");
    layout->addWidget(m_totalDataPointsLabel, 0, 1);
    
    layout->addWidget(new QLabel("Valid Data Points:", this), 0, 2);
    m_validDataPointsLabel = new QLabel("0", this);
    m_validDataPointsLabel->setStyleSheet("font-weight: bold;");
    layout->addWidget(m_validDataPointsLabel, 0, 3);
    
    // Data rate
    layout->addWidget(new QLabel("Data Rate:", this), 1, 0);
    m_dataRateLabel = new QLabel("0.0 Hz", this);
    m_dataRateLabel->setStyleSheet("font-weight: bold;");
    layout->addWidget(m_dataRateLabel, 1, 1);
    
    // Errors
    layout->addWidget(new QLabel("Errors:", this), 1, 2);
    m_errorsLabel = new QLabel("0", this);
    m_errorsLabel->setStyleSheet("font-weight: bold;");
    layout->addWidget(m_errorsLabel, 1, 3);
    
    // Session time
    layout->addWidget(new QLabel("Session Time:", this), 2, 0);
    m_sessionTimeLabel = new QLabel("00:00:00", this);
    m_sessionTimeLabel->setStyleSheet("font-weight: bold; font-family: monospace;");
    layout->addWidget(m_sessionTimeLabel, 2, 1, 1, 3);
    
    m_mainLayout->addWidget(m_statisticsGroup);
}

void TestControlPanel::onStartButtonClicked()
{
    if (!m_sessionManager) {
        QMessageBox::warning(this, "Error", "No session manager configured");
        return;
    }
    
    if (!m_testConfig) {
        QMessageBox::warning(this, "Error", "No test configuration available");
        return;
    }
    
    // Validate configuration
    if (!m_testConfig->isValid()) {
        QStringList errors = m_testConfig->validate();
        QMessageBox::warning(this, "Configuration Error", 
                           QString("Test configuration is invalid:\n%1").arg(errors.join("\n")));
        return;
    }
    
    // Start session
    bool success = m_sessionManager->startSession(m_testConfig, m_currentEssai);
    if (!success) {
        QString error = m_sessionManager->getLastError();
        QMessageBox::critical(this, "Start Error", 
                            QString("Failed to start test session:\n%1").arg(error));
    }
    
    emit testStartRequested();
}

void TestControlPanel::onStopButtonClicked()
{
    if (m_sessionManager) {
        m_sessionManager->stopSession();
    }
    emit testStopRequested();
}

void TestControlPanel::onPauseResumeButtonClicked()
{
    if (!m_sessionManager) {
        return;
    }
    
    if (m_sessionManager->isSessionPaused()) {
        m_sessionManager->resumeSession();
        emit testResumeRequested();
    } else if (m_sessionManager->isSessionRunning()) {
        m_sessionManager->pauseSession();
        emit testPauseRequested();
    }
}

void TestControlPanel::onCancelButtonClicked()
{
    if (m_sessionManager) {
        int ret = QMessageBox::question(this, "Cancel Test", 
                                      "Are you sure you want to cancel the current test?",
                                      QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            m_sessionManager->cancelSession();
            emit testCancelRequested();
        }
    }
}

void TestControlPanel::onEmergencyStopClicked()
{
    if (m_sessionManager) {
        m_sessionManager->cancelSession();
    }
    
    // Show emergency stop message
    QMessageBox::critical(this, "EMERGENCY STOP", 
                        "EMERGENCY STOP ACTIVATED!\nAll test operations have been halted.");
    
    emit emergencyStopRequested();
}

void TestControlPanel::onConfigureButtonClicked()
{
    emit configurationChangeRequested();
}

void TestControlPanel::onResetButtonClicked()
{
    resetControls();
}

void TestControlPanel::onSessionStarted()
{
    m_sessionStartTime = QDateTime::currentDateTime();
    updateControlsState();
    
    QString message = QString("[%1] Test session started")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
    
    qCInfo(testControlPanelLog) << "Session started";
}

void TestControlPanel::onSessionStopped()
{
    updateControlsState();
    
    QString message = QString("[%1] Test session stopped")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
    
    qCInfo(testControlPanelLog) << "Session stopped";
}

void TestControlPanel::onSessionPaused()
{
    updateControlsState();
    
    QString message = QString("[%1] Test session paused")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
}

void TestControlPanel::onSessionResumed()
{
    updateControlsState();
    
    QString message = QString("[%1] Test session resumed")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
}

void TestControlPanel::onSessionCompleted()
{
    updateControlsState();
    
    QString message = QString("[%1] Test session completed successfully")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
    
    QMessageBox::information(this, "Test Complete", "Test session completed successfully!");
}

void TestControlPanel::onSessionFailed(const QString& error)
{
    updateControlsState();
    
    QString message = QString("[%1] Test session failed: %2")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"), error);
    m_statusMessages->append(message);
    
    QMessageBox::critical(this, "Test Failed", QString("Test session failed:\n%1").arg(error));
}

void TestControlPanel::onSessionCancelled()
{
    updateControlsState();
    
    QString message = QString("[%1] Test session cancelled")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_statusMessages->append(message);
}

void TestControlPanel::onSessionStatusChanged(TestSessionManager::SessionStatus status)
{
    m_currentStatus = status;
    updateStatusDisplay();
    updateButtonStates();
}

void TestControlPanel::onSessionProgressChanged(double percentage)
{
    m_progressBar->setValue(static_cast<int>(percentage));
    m_progressLabel->setText(QString("%1%").arg(percentage, 0, 'f', 1));
    
    updateProgressDisplay();
}

void TestControlPanel::onSessionPhaseChanged(const QString& phase)
{
    m_phaseLabel->setText(phase);
}

void TestControlPanel::onSessionStatisticsUpdated(const TestSessionManager::SessionStatistics& stats)
{
    m_currentStats = stats;
    updateStatisticsDisplay();
}

void TestControlPanel::onSessionError(const QString& error)
{
    QString message = QString("[%1] ERROR: %2")
                         .arg(QDateTime::currentDateTime().toString("hh:mm:ss"), error);
    m_statusMessages->append(message);
    
    // Limit message count
    QTextDocument* doc = m_statusMessages->document();
    if (doc->blockCount() > MAX_STATUS_MESSAGES) {
        QTextCursor cursor = m_statusMessages->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.select(QTextCursor::BlockUnderCursor);
        cursor.removeSelectedText();
    }
}

void TestControlPanel::onCriticalSessionError(const QString& error)
{
    onSessionError(QString("CRITICAL: %1").arg(error));
    QMessageBox::critical(this, "Critical Error", QString("Critical test error:\n%1").arg(error));
}

void TestControlPanel::onUpdateTimer()
{
    updateProgressDisplay();
    updateStatisticsDisplay();
}

void TestControlPanel::updateControlsState()
{
    updateButtonStates();
    updateStatusDisplay();
    updateProgressDisplay();
    updateStatisticsDisplay();
}

void TestControlPanel::updateButtonStates()
{
    bool sessionActive = m_sessionManager && m_sessionManager->isSessionActive();
    bool sessionRunning = m_sessionManager && m_sessionManager->isSessionRunning();
    bool sessionPaused = m_sessionManager && m_sessionManager->isSessionPaused();
    
    m_startButton->setEnabled(!sessionActive && m_testConfig);
    m_stopButton->setEnabled(sessionActive);
    m_pauseResumeButton->setEnabled(sessionActive);
    m_cancelButton->setEnabled(sessionActive);
    m_configureButton->setEnabled(!sessionActive);
    m_resetButton->setEnabled(!sessionActive);
    
    // Update pause/resume button text
    if (sessionPaused) {
        m_pauseResumeButton->setText("Resume");
        m_pauseResumeButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");
    } else {
        m_pauseResumeButton->setText("Pause");
        m_pauseResumeButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 10px; }");
    }
}

void TestControlPanel::updateStatusDisplay()
{
    QString statusText = formatSessionStatus(m_currentStatus);
    m_statusLabel->setText(statusText);
    
    QColor statusColor = getStatusColor(m_currentStatus);
    m_statusIcon->setStyleSheet(QString("background-color: %1; border-radius: 12px;").arg(statusColor.name()));
}

void TestControlPanel::updateProgressDisplay()
{
    if (!m_sessionManager || !m_sessionStartTime.isValid()) {
        return;
    }
    
    // Update elapsed time
    qint64 elapsedMs = m_sessionStartTime.msecsTo(QDateTime::currentDateTime());
    m_timeElapsedLabel->setText(formatDuration(elapsedMs / 1000.0));
    
    // Update remaining time
    if (m_testConfig && m_progressBar->value() > 0) {
        double totalDurationS = m_testConfig->testDuration() / 1000.0;
        double progress = m_progressBar->value() / 100.0;
        double remainingS = totalDurationS * (1.0 - progress);
        m_timeRemainingLabel->setText(formatDuration(remainingS));
    } else {
        m_timeRemainingLabel->setText("--:--:--");
    }
}

void TestControlPanel::updateStatisticsDisplay()
{
    m_totalDataPointsLabel->setText(QString::number(m_currentStats.totalDataPoints));
    m_validDataPointsLabel->setText(QString::number(m_currentStats.validDataPoints));
    m_dataRateLabel->setText(formatDataRate(m_currentStats.averageDataRate));
    m_errorsLabel->setText(QString::number(m_currentStats.validationErrors + m_currentStats.criticalErrors));
    m_sessionTimeLabel->setText(formatDuration(m_currentStats.sessionDuration));
}

void TestControlPanel::updateQuickConfigDisplay()
{
    if (m_testConfig) {
        m_testDurationSpinBox->setValue(m_testConfig->testDuration() / 1000);
        m_pressureTargetSpinBox->setValue(m_testConfig->pressureMax());
        m_flowTargetSpinBox->setValue(m_testConfig->flowMax());
    }
    
    if (m_currentEssai) {
        QString testType = m_currentEssai->testTypeDisplayName();
        int index = m_testTypeComboBox->findText(testType);
        if (index >= 0) {
            m_testTypeComboBox->setCurrentIndex(index);
        }
    }
}

void TestControlPanel::resetControls()
{
    m_progressBar->setValue(0);
    m_progressLabel->setText("0%");
    m_phaseLabel->setText("Idle");
    m_timeElapsedLabel->setText("00:00:00");
    m_timeRemainingLabel->setText("--:--:--");
    m_statusMessages->clear();
    
    // Reset statistics
    m_currentStats = TestSessionManager::SessionStatistics();
    updateStatisticsDisplay();
    
    m_sessionStartTime = QDateTime();
    updateControlsState();
}

QString TestControlPanel::formatDuration(double seconds) const
{
    int hours = static_cast<int>(seconds) / 3600;
    int minutes = (static_cast<int>(seconds) % 3600) / 60;
    int secs = static_cast<int>(seconds) % 60;
    
    return QString("%1:%2:%3")
               .arg(hours, 2, 10, QChar('0'))
               .arg(minutes, 2, 10, QChar('0'))
               .arg(secs, 2, 10, QChar('0'));
}

QString TestControlPanel::formatDataRate(double rate) const
{
    return QString("%1 Hz").arg(rate, 0, 'f', 1);
}

QString TestControlPanel::formatSessionStatus(TestSessionManager::SessionStatus status) const
{
    switch (status) {
        case TestSessionManager::SessionStatus::Idle: return "Idle";
        case TestSessionManager::SessionStatus::Preparing: return "Preparing";
        case TestSessionManager::SessionStatus::Running: return "Running";
        case TestSessionManager::SessionStatus::Paused: return "Paused";
        case TestSessionManager::SessionStatus::Stopping: return "Stopping";
        case TestSessionManager::SessionStatus::Completed: return "Completed";
        case TestSessionManager::SessionStatus::Failed: return "Failed";
        case TestSessionManager::SessionStatus::Cancelled: return "Cancelled";
    }
    return "Unknown";
}

QColor TestControlPanel::getStatusColor(TestSessionManager::SessionStatus status) const
{
    switch (status) {
        case TestSessionManager::SessionStatus::Idle: return QColor("#9E9E9E");
        case TestSessionManager::SessionStatus::Preparing: return QColor("#FF9800");
        case TestSessionManager::SessionStatus::Running: return QColor("#4CAF50");
        case TestSessionManager::SessionStatus::Paused: return QColor("#FF9800");
        case TestSessionManager::SessionStatus::Stopping: return QColor("#FF5722");
        case TestSessionManager::SessionStatus::Completed: return QColor("#2196F3");
        case TestSessionManager::SessionStatus::Failed: return QColor("#F44336");
        case TestSessionManager::SessionStatus::Cancelled: return QColor("#9C27B0");
    }
    return QColor("#9E9E9E");
}

void TestControlPanel::onTestTypeChanged()
{
    // Update test configuration based on selected type
    // This would typically create a new configuration or modify the existing one
    emit configurationChangeRequested();
}

void TestControlPanel::onTestDurationChanged()
{
    if (m_testConfig) {
        m_testConfig->setTestDuration(m_testDurationSpinBox->value() * 1000); // Convert to ms
    }
}
