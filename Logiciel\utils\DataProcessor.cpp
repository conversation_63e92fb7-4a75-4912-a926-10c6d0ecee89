#include "DataProcessor.h"
#include <QDebug>
#include <QMutexLocker>
#include <QElapsedTimer>
#include <QtMath>
#include <algorithm>

Q_LOGGING_CATEGORY(dataProcessorLog, "hydraulic.utils.dataprocessor")

DataProcessor::DataProcessor(QObject *parent)
    : QObject(parent)
    , m_processedDataCount(0)
{
    // Initialize default configuration
    m_config.enableFiltering = true;
    m_config.enableValidation = true;
    m_config.enableCalculations = true;
    m_config.enableStatistics = true;
    m_config.filterCutoff = 10.0;
    m_config.filterOrder = 2;
    m_config.validationTolerance = 0.05;
    m_config.statisticsWindow = 100;
    m_config.enableRealTimeProcessing = true;
    
    // Initialize hydraulic analyzer
    m_analyzer = std::make_unique<HydraulicTestAnalyzer>(m_hydraulicParams);
    
    // Initialize filters
    initializeFilters();
    
    qCDebug(dataProcessorLog) << "DataProcessor initialized";
}

void DataProcessor::setProcessingConfig(const ProcessingConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_config = config;
    
    // Reinitialize filters if configuration changed
    initializeFilters();
    
    emit configurationChanged();
    qCDebug(dataProcessorLog) << "Processing configuration updated";
}

DataProcessor::ProcessingConfig DataProcessor::processingConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void DataProcessor::setTestConfiguration(std::shared_ptr<TestConfiguration> config)
{
    QMutexLocker locker(&m_mutex);
    m_testConfig = config;
    
    if (m_testConfig) {
        connect(m_testConfig.get(), &TestConfiguration::configurationChanged,
                this, &DataProcessor::onTestConfigurationChanged);
    }
    
    qCDebug(dataProcessorLog) << "Test configuration set";
}

std::shared_ptr<TestConfiguration> DataProcessor::testConfiguration() const
{
    QMutexLocker locker(&m_mutex);
    return m_testConfig;
}

void DataProcessor::setHydraulicParameters(const HydraulicSystemParameters& params)
{
    QMutexLocker locker(&m_mutex);
    m_hydraulicParams = params;
    m_analyzer->setSystemParameters(params);
    
    qCDebug(dataProcessorLog) << "Hydraulic parameters updated";
}

HydraulicSystemParameters DataProcessor::hydraulicParameters() const
{
    QMutexLocker locker(&m_mutex);
    return m_hydraulicParams;
}

TestData DataProcessor::processRawData(const TestData& rawData)
{
    QElapsedTimer timer;
    timer.start();
    
    TestData processedData = rawData;
    
    if (!processRawDataInPlace(processedData)) {
        emit processingError("Failed to process raw data");
        return rawData; // Return original data on error
    }
    
    // Update performance metrics
    double processingTime = timer.nsecsElapsed() / 1000000.0; // Convert to milliseconds
    updatePerformanceMetrics(processingTime);
    
    emit dataProcessed(processedData);
    return processedData;
}

bool DataProcessor::processRawDataInPlace(TestData& data)
{
    QMutexLocker locker(&m_mutex);
    
    try {
        // Step 1: Validate data integrity
        if (m_config.enableValidation) {
            QStringList errors;
            if (!validateDataIntegrity(data, errors)) {
                emit validationFailed(data, errors);
                if (!errors.isEmpty()) {
                    qCWarning(dataProcessorLog) << "Data validation failed:" << errors;
                    return false;
                }
            }
        }
        
        // Step 2: Apply filters
        if (m_config.enableFiltering) {
            if (!applyFilters(data)) {
                qCWarning(dataProcessorLog) << "Failed to apply filters";
                return false;
            }
        }
        
        // Step 3: Calculate derived values
        if (m_config.enableCalculations) {
            if (!calculateDerivedValues(data)) {
                qCWarning(dataProcessorLog) << "Failed to calculate derived values";
                return false;
            }
        }
        
        // Step 4: Update statistics
        if (m_config.enableStatistics) {
            updateStatistics(data);
        }
        
        // Step 5: Check safety limits
        QStringList violations;
        if (checkSafetyLimits(data, violations)) {
            if (!violations.isEmpty()) {
                emit safetyViolation(data, violations);
                qCWarning(dataProcessorLog) << "Safety violations detected:" << violations;
            }
        }
        
        // Step 6: Detect outliers
        QStringList outliers;
        if (detectOutliers(data, outliers)) {
            if (!outliers.isEmpty()) {
                emit outlierDetected(data, outliers);
                qCDebug(dataProcessorLog) << "Outliers detected:" << outliers;
            }
        }
        
        // Update quality metrics
        updateQualityMetrics(data);
        
        m_processedDataCount++;
        return true;
        
    } catch (const std::exception& e) {
        qCCritical(dataProcessorLog) << "Exception in data processing:" << e.what();
        emit processingError(QString("Processing exception: %1").arg(e.what()));
        return false;
    }
}

QList<TestData> DataProcessor::processBatch(const QList<TestData>& rawDataList)
{
    QList<TestData> processedList;
    processedList.reserve(rawDataList.size());
    
    for (const TestData& rawData : rawDataList) {
        TestData processed = processRawData(rawData);
        processedList.append(processed);
    }
    
    return processedList;
}

bool DataProcessor::applyFilters(TestData& data)
{
    if (!m_config.enableFiltering) {
        return true;
    }
    
    try {
        // Apply low-pass filter to pressure readings
        double filteredCPA = applyLowPassFilter(
            data.pressureCPA(),
            m_filterStates.value("pressure_cpa", data.pressureCPA()),
            m_config.filterCutoff,
            DEFAULT_SAMPLE_RATE
        );
        data.setPressureCPA(filteredCPA);
        m_filterStates["pressure_cpa"] = filteredCPA;
        
        double filteredCPB = applyLowPassFilter(
            data.pressureCPB(),
            m_filterStates.value("pressure_cpb", data.pressureCPB()),
            m_config.filterCutoff,
            DEFAULT_SAMPLE_RATE
        );
        data.setPressureCPB(filteredCPB);
        m_filterStates["pressure_cpb"] = filteredCPB;
        
        // Apply filter to flow rate
        double filteredFlow = applyLowPassFilter(
            data.flowRate(),
            m_filterStates.value("flow_rate", data.flowRate()),
            m_config.filterCutoff,
            DEFAULT_SAMPLE_RATE
        );
        data.setFlowRate(filteredFlow);
        m_filterStates["flow_rate"] = filteredFlow;
        
        // Apply filter to actuator velocity (more aggressive filtering)
        double filteredVelocity = applyLowPassFilter(
            data.actuatorVelocity(),
            m_filterStates.value("actuator_velocity", data.actuatorVelocity()),
            m_config.filterCutoff * 0.5, // Lower cutoff for velocity
            DEFAULT_SAMPLE_RATE
        );
        data.setActuatorVelocity(filteredVelocity);
        m_filterStates["actuator_velocity"] = filteredVelocity;
        
        // Update filter buffers for moving average filters
        updateFilterBuffers(data);
        
        return true;
        
    } catch (const std::exception& e) {
        qCWarning(dataProcessorLog) << "Filter application failed:" << e.what();
        return false;
    }
}

bool DataProcessor::validateDataIntegrity(const TestData& data, QStringList& errors)
{
    errors.clear();
    
    if (!data.isValid()) {
        errors.append("Basic data validation failed");
        return false;
    }
    
    // Check timestamp validity
    if (!data.timestamp().isValid()) {
        errors.append("Invalid timestamp");
    }
    
    // Validate pressure readings
    QStringList pressureErrors = validateSensorReading("pressure_cpa", data.pressureCPA());
    errors.append(pressureErrors);
    
    pressureErrors = validateSensorReading("pressure_cpb", data.pressureCPB());
    errors.append(pressureErrors);
    
    // Validate flow rate
    QStringList flowErrors = validateSensorReading("flow_rate", data.flowRate());
    errors.append(flowErrors);
    
    // Validate temperature readings
    QStringList tempErrors = validateSensorReading("temperature", data.temperatureFluid());
    errors.append(tempErrors);
    
    // Check sensor correlation
    if (!isSensorCorrelationValid(data)) {
        errors.append("Sensor correlation validation failed");
    }
    
    // Remove empty error strings
    errors.removeAll("");
    
    return errors.isEmpty();
}

bool DataProcessor::calculateDerivedValues(TestData& data)
{
    if (!m_analyzer) {
        qCWarning(dataProcessorLog) << "Hydraulic analyzer not initialized";
        return false;
    }
    
    try {
        // Use the hydraulic analyzer to calculate derived values
        TestData analyzed = m_analyzer->analyzeDataPoint(data);
        
        // Copy calculated values back to original data
        data.setPowerHydraulic(analyzed.powerHydraulic());
        data.setEfficiency(analyzed.efficiency());
        
        // Copy additional calculated parameters
        QStringList paramNames = analyzed.parameterNames();
        for (const QString& paramName : paramNames) {
            if (paramName.startsWith("calculated_") || 
                paramName.contains("efficiency") || 
                paramName.contains("theoretical")) {
                data.setParameter(paramName, analyzed.parameter(paramName));
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        qCWarning(dataProcessorLog) << "Derived value calculation failed:" << e.what();
        return false;
    }
}

bool DataProcessor::updateStatistics(const TestData& data)
{
    if (!m_config.enableStatistics) {
        return true;
    }
    
    // Add data to recent data buffer
    m_recentData.append(data);
    
    // Limit buffer size
    while (m_recentData.size() > m_config.statisticsWindow) {
        m_recentData.removeFirst();
    }
    
    // Update statistics buffers for individual parameters
    m_statisticsBuffers["pressure_cpa"].append(data.pressureCPA());
    m_statisticsBuffers["pressure_cpb"].append(data.pressureCPB());
    m_statisticsBuffers["flow_rate"].append(data.flowRate());
    m_statisticsBuffers["temperature"].append(data.temperatureFluid());
    
    // Limit buffer sizes
    for (auto it = m_statisticsBuffers.begin(); it != m_statisticsBuffers.end(); ++it) {
        while (it.value().size() > MAX_STATISTICS_BUFFER_SIZE) {
            it.value().removeFirst();
        }
    }
    
    return true;
}

QStringList DataProcessor::validateSensorReading(const QString& sensor, double value) const
{
    QStringList errors;
    
    if (qIsNaN(value) || qIsInf(value)) {
        errors.append(QString("Invalid %1 reading: %2").arg(sensor).arg(value));
        return errors;
    }
    
    if (!m_testConfig) {
        return errors; // No configuration to validate against
    }
    
    // Validate against test configuration limits
    if (sensor.contains("pressure")) {
        auto range = m_testConfig->pressureRange();
        if (!isValueInRange(value, range.first, range.second)) {
            errors.append(QString("Pressure %1 out of range [%2, %3]: %4")
                             .arg(sensor).arg(range.first).arg(range.second).arg(value));
        }
    } else if (sensor.contains("flow")) {
        auto range = m_testConfig->flowRange();
        if (!isValueInRange(value, range.first, range.second)) {
            errors.append(QString("Flow %1 out of range [%2, %3]: %4")
                             .arg(sensor).arg(range.first).arg(range.second).arg(value));
        }
    } else if (sensor.contains("temperature")) {
        if (value > m_testConfig->temperatureMax()) {
            errors.append(QString("Temperature %1 exceeds maximum %2: %3")
                             .arg(sensor).arg(m_testConfig->temperatureMax()).arg(value));
        }
    }
    
    return errors;
}

bool DataProcessor::checkSafetyLimits(const TestData& data, QStringList& violations) const
{
    violations.clear();
    
    if (!m_testConfig) {
        return true; // No configuration to check against
    }
    
    auto safetyThresholds = m_testConfig->safetyThresholds();
    
    // Check pressure limits
    if (!m_testConfig->isPressureSafe(data.pressureCPA())) {
        violations.append(QString("CPA pressure unsafe: %1 bar").arg(data.pressureCPA()));
    }
    
    if (!m_testConfig->isPressureSafe(data.pressureCPB())) {
        violations.append(QString("CPB pressure unsafe: %1 bar").arg(data.pressureCPB()));
    }
    
    // Check temperature limits
    if (!m_testConfig->isTemperatureSafe(data.temperatureFluid())) {
        violations.append(QString("Fluid temperature unsafe: %1 °C").arg(data.temperatureFluid()));
    }
    
    // Check flow limits
    if (!m_testConfig->isFlowSafe(data.flowRate())) {
        violations.append(QString("Flow rate unsafe: %1 L/min").arg(data.flowRate()));
    }
    
    // Check velocity limits
    if (!m_testConfig->isVelocitySafe(data.actuatorVelocity())) {
        violations.append(QString("Actuator velocity unsafe: %1 mm/s").arg(data.actuatorVelocity()));
    }
    
    // Check force limits
    if (!m_testConfig->isForceSafe(data.actuatorForce())) {
        violations.append(QString("Actuator force unsafe: %1 N").arg(data.actuatorForce()));
    }
    
    return true;
}

bool DataProcessor::detectOutliers(const TestData& data, QStringList& outliers) const
{
    outliers.clear();
    
    // Need sufficient data for outlier detection
    if (m_recentData.size() < MIN_STATISTICS_SAMPLES) {
        return true;
    }
    
    // Check each parameter for outliers using Z-score method
    QStringList parameters = {"pressure_cpa", "pressure_cpb", "flow_rate", "temperature"};
    
    for (const QString& param : parameters) {
        if (!m_statisticsBuffers.contains(param)) {
            continue;
        }
        
        const QList<double>& values = m_statisticsBuffers.value(param);
        if (values.size() < MIN_STATISTICS_SAMPLES) {
            continue;
        }
        
        double mean = calculateMean(values);
        double stdDev = calculateStandardDeviation(values);
        
        if (stdDev == 0.0) {
            continue; // No variation, can't detect outliers
        }
        
        double currentValue = 0.0;
        if (param == "pressure_cpa") currentValue = data.pressureCPA();
        else if (param == "pressure_cpb") currentValue = data.pressureCPB();
        else if (param == "flow_rate") currentValue = data.flowRate();
        else if (param == "temperature") currentValue = data.temperatureFluid();
        
        double zScore = qAbs(currentValue - mean) / stdDev;
        
        if (zScore > OUTLIER_THRESHOLD) {
            outliers.append(QString("%1 outlier detected: %2 (Z-score: %3)")
                               .arg(param).arg(currentValue).arg(zScore));
        }
    }
    
    return true;
}

double DataProcessor::applyLowPassFilter(double newValue, double previousValue, double cutoffFreq, double sampleRate)
{
    // Simple first-order low-pass filter
    double alpha = 2.0 * M_PI * cutoffFreq / sampleRate;
    double filterConstant = alpha / (1.0 + alpha);
    
    return previousValue + filterConstant * (newValue - previousValue);
}

void DataProcessor::resetStatistics()
{
    QMutexLocker locker(&m_mutex);
    
    m_recentData.clear();
    m_statisticsBuffers.clear();
    m_qualityMetrics = QualityMetrics();
    
    qCDebug(dataProcessorLog) << "Statistics reset";
}

DataProcessor::QualityMetrics DataProcessor::getQualityMetrics() const
{
    QMutexLocker locker(&m_mutex);
    return m_qualityMetrics;
}

void DataProcessor::enableRealTimeProcessing(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_config.enableRealTimeProcessing = enabled;
    
    qCDebug(dataProcessorLog) << "Real-time processing" << (enabled ? "enabled" : "disabled");
}

bool DataProcessor::isRealTimeProcessingEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_config.enableRealTimeProcessing;
}

int DataProcessor::getProcessedDataCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_processedDataCount;
}

double DataProcessor::getAverageProcessingTime() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_processingTimes.isEmpty()) {
        return 0.0;
    }
    
    return calculateMean(m_processingTimes);
}

void DataProcessor::resetPerformanceCounters()
{
    QMutexLocker locker(&m_mutex);
    
    m_processedDataCount = 0;
    m_processingTimes.clear();
    
    qCDebug(dataProcessorLog) << "Performance counters reset";
}

void DataProcessor::onTestConfigurationChanged()
{
    qCDebug(dataProcessorLog) << "Test configuration changed, updating processor";
    emit configurationChanged();
}

bool DataProcessor::initializeFilters()
{
    m_filterStates.clear();
    m_filterBuffers.clear();
    
    // Initialize filter states with default values
    m_filterStates["pressure_cpa"] = 0.0;
    m_filterStates["pressure_cpb"] = 0.0;
    m_filterStates["flow_rate"] = 0.0;
    m_filterStates["actuator_velocity"] = 0.0;
    
    return true;
}

void DataProcessor::updateFilterBuffers(const TestData& data)
{
    // Update moving average buffers
    m_filterBuffers["pressure_cpa"].append(data.pressureCPA());
    m_filterBuffers["pressure_cpb"].append(data.pressureCPB());
    m_filterBuffers["flow_rate"].append(data.flowRate());
    
    // Limit buffer sizes
    for (auto it = m_filterBuffers.begin(); it != m_filterBuffers.end(); ++it) {
        while (it.value().size() > MAX_FILTER_BUFFER_SIZE) {
            it.value().removeFirst();
        }
    }
}

void DataProcessor::updateQualityMetrics(const TestData& data)
{
    m_qualityMetrics.lastUpdate = QDateTime::currentDateTime();
    
    // Update data completeness
    int validFields = 0;
    int totalFields = 0;
    
    QStringList paramNames = data.parameterNames();
    for (const QString& param : paramNames) {
        totalFields++;
        QVariant value = data.parameter(param);
        if (value.isValid() && !value.isNull()) {
            validFields++;
        }
    }
    
    if (totalFields > 0) {
        m_qualityMetrics.dataCompleteness = (double(validFields) / totalFields) * 100.0;
    }
    
    // Calculate average update rate
    if (m_lastProcessingTime.isValid()) {
        qint64 deltaMs = m_lastProcessingTime.msecsTo(m_qualityMetrics.lastUpdate);
        if (deltaMs > 0) {
            double currentRate = 1000.0 / deltaMs; // Hz
            m_qualityMetrics.averageUpdateRate = 
                (m_qualityMetrics.averageUpdateRate * 0.9) + (currentRate * 0.1); // Exponential smoothing
        }
    }
    
    m_lastProcessingTime = m_qualityMetrics.lastUpdate;
    
    // Emit updated metrics periodically
    static int updateCounter = 0;
    if (++updateCounter % 100 == 0) { // Every 100 updates
        emit qualityMetricsUpdated(m_qualityMetrics);
    }
}

void DataProcessor::updatePerformanceMetrics(double processingTimeMs)
{
    m_processingTimes.append(processingTimeMs);
    
    // Limit processing time history
    while (m_processingTimes.size() > 1000) {
        m_processingTimes.removeFirst();
    }
}

bool DataProcessor::isValueInRange(double value, double min, double max) const
{
    return value >= min && value <= max;
}

bool DataProcessor::isSensorCorrelationValid(const TestData& data) const
{
    // Check if sensor readings are correlated as expected
    // For example, high pressure should correlate with high force
    
    double pressureDiff = data.pressureCPA() - data.pressureCPB();
    double force = data.actuatorForce();
    
    // Simple correlation check: pressure difference should relate to force direction
    if (qAbs(pressureDiff) > 10.0) { // Significant pressure difference
        if ((pressureDiff > 0 && force < -1000.0) || (pressureDiff < 0 && force > 1000.0)) {
            return false; // Unexpected correlation
        }
    }
    
    return true;
}

double DataProcessor::calculateMean(const QList<double>& values) const
{
    if (values.isEmpty()) return 0.0;
    
    double sum = 0.0;
    for (double value : values) {
        sum += value;
    }
    return sum / values.size();
}

double DataProcessor::calculateStandardDeviation(const QList<double>& values) const
{
    if (values.size() < 2) return 0.0;
    
    double mean = calculateMean(values);
    double sumSquaredDiffs = 0.0;
    
    for (double value : values) {
        double diff = value - mean;
        sumSquaredDiffs += diff * diff;
    }
    
    double variance = sumSquaredDiffs / (values.size() - 1);
    return qSqrt(variance);
}
