#ifndef PRESSURESENSOR_H
#define PRESSURESENSOR_H

#include "SimulatedSensor.h"

/**
 * @brief Simulated hydraulic pressure sensor
 * 
 * This class simulates a hydraulic pressure sensor with realistic
 * pressure readings for hydraulic cylinder testing.
 */
class PressureSensor : public SimulatedSensor
{
    Q_OBJECT
    
public:
    enum class PressureType {
        CPA,  // Chamber A pressure
        CPB,  // Chamber B pressure
        Supply,  // Supply pressure
        Return   // Return pressure
    };
    
    explicit PressureSensor(PressureType type, QObject *parent = nullptr);
    
    // HardwareInterface implementation
    QString deviceName() const override;
    QString deviceVersion() const override;
    DeviceType deviceType() const override;
    
    // SimulatedSensor implementation
    double generateRealisticValue() override;
    QString getPrimaryParameter() const override;
    QStringList getAllParameters() const override;
    
    // Pressure-specific methods
    PressureType pressureType() const { return m_pressureType; }
    void setPressureType(PressureType type);
    
    // Hydraulic test scenarios
    void simulatePressureTest(double targetPressure, int durationMs = 5000);
    void simulateLeakTest(double initialPressure, double leakRate = 0.1);
    void simulateCycleTest(double minPressure, double maxPressure, int cycleTimeMs = 2000);
    void simulateStaticTest(double pressure);
    
    // Pressure unit conversion
    static double barToPsi(double bar);
    static double psiToBar(double psi);
    static double barToPascal(double bar);
    static double pascalToBar(double pascal);
    
signals:
    void pressureTestCompleted();
    void leakDetected(double leakRate);
    void cycleCompleted(int cycleNumber);
    
private slots:
    void onTestTimer();
    void onCycleTimer();
    
private:
    PressureType m_pressureType;
    QString m_pressureTypeString;
    
    // Test simulation
    QTimer* m_testTimer;
    QTimer* m_cycleTimer;
    
    enum class TestMode {
        Normal,
        PressureTest,
        LeakTest,
        CycleTest,
        StaticTest
    };
    
    TestMode m_currentTestMode;
    double m_testTargetPressure;
    double m_testInitialPressure;
    double m_testLeakRate;
    double m_testMinPressure;
    double m_testMaxPressure;
    int m_testDuration;
    int m_cycleTime;
    int m_currentCycle;
    bool m_cycleDirection; // true = increasing, false = decreasing
    QDateTime m_testStartTime;
    
    void setupPressureType();
    void updateTestMode();
    double generatePressureTestValue();
    double generateLeakTestValue();
    double generateCycleTestValue();
    double generateStaticTestValue();
    double generateNormalPressureValue();
    
    // Realistic pressure patterns
    double generateSupplyPressure();
    double generateChamberPressure();
    double generateReturnPressure();
    
    static const double DEFAULT_SUPPLY_PRESSURE; // bar
    static const double DEFAULT_MAX_CHAMBER_PRESSURE; // bar
    static const double DEFAULT_RETURN_PRESSURE; // bar
};

#endif // PRESSURESENSOR_H
