#ifndef HARDWAREINTERFACE_H
#define HARDWAREINTERFACE_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QMap>
#include <QDateTime>

/**
 * @brief Abstract hardware interface for hydraulic test equipment
 * 
 * This class provides an abstraction layer that can work with both
 * simulated hardware (for development/testing) and real hardware
 * (for production use) without requiring code changes.
 */
class HardwareInterface : public QObject
{
    Q_OBJECT
    
public:
    enum class ConnectionStatus {
        Disconnected,
        Connecting,
        Connected,
        Error
    };
    
    enum class DeviceType {
        PressureSensor,
        FlowMeter,
        TemperatureSensor,
        ActuatorControl,
        DataAcquisitionUnit
    };
    
    explicit HardwareInterface(QObject *parent = nullptr);
    virtual ~HardwareInterface() = default;
    
    // Connection management
    virtual bool connect() = 0;
    virtual void disconnect() = 0;
    virtual bool isConnected() const = 0;
    virtual ConnectionStatus connectionStatus() const = 0;
    
    // Device information
    virtual QString deviceName() const = 0;
    virtual QString deviceVersion() const = 0;
    virtual DeviceType deviceType() const = 0;
    virtual bool isSimulated() const = 0;
    
    // Data acquisition
    virtual QVariant readValue(const QString& parameter) = 0;
    virtual bool writeValue(const QString& parameter, const QVariant& value) = 0;
    virtual QMap<QString, QVariant> readAllValues() = 0;
    
    // Configuration
    virtual bool configure(const QMap<QString, QVariant>& config) = 0;
    virtual QMap<QString, QVariant> getConfiguration() const = 0;
    
    // Calibration
    virtual bool calibrate() = 0;
    virtual bool isCalibrated() const = 0;
    virtual QDateTime lastCalibrationDate() const = 0;
    
    // Status and diagnostics
    virtual bool performSelfTest() = 0;
    virtual QString getLastError() const = 0;
    virtual QMap<QString, QVariant> getDiagnosticInfo() const = 0;
    
signals:
    void connectionStatusChanged(ConnectionStatus status);
    void dataReceived(const QString& parameter, const QVariant& value);
    void errorOccurred(const QString& error);
    void calibrationRequired();
    void selfTestCompleted(bool success);
    
protected:
    ConnectionStatus m_connectionStatus;
    QString m_lastError;
    QMap<QString, QVariant> m_configuration;
    QDateTime m_lastCalibration;
    bool m_isCalibrated;
    
    void setConnectionStatus(ConnectionStatus status);
    void setLastError(const QString& error);
    void emitDataReceived(const QString& parameter, const QVariant& value);
};

#endif // HARDWAREINTERFACE_H
