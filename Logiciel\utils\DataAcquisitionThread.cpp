#include "DataAcquisitionThread.h"
#include <QDebug>
#include <QMutexLocker>

DataAcquisitionThread::DataAcquisitionThread(QObject *parent)
    : QThread(parent)
    , m_isRunning(false)
    , m_isPaused(false)
    , m_stopRequested(false)
    , m_acquisitionRateHz(100)
    , m_maxBufferSize(10000)
    , m_acquisitionIntervalMs(10)
    , m_totalSamples(0)
    , m_droppedSamples(0)
    , m_actualRate(0.0)
    , m_lastSampleTime(0)
{
    updateAcquisitionInterval();
    m_dataQueue.setMaxSize(m_maxBufferSize);
}

DataAcquisitionThread::~DataAcquisitionThread()
{
    stopAcquisition();
    wait(); // Wait for thread to finish
}

void DataAcquisitionThread::startAcquisition()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isRunning) {
        return; // Already running
    }
    
    if (m_hardwareDevices.isEmpty()) {
        setError("No hardware devices configured for data acquisition");
        return;
    }
    
    // Validate hardware devices
    validateHardwareDevices();
    if (hasErrors()) {
        return;
    }
    
    // Reset statistics
    m_totalSamples = 0;
    m_droppedSamples = 0;
    m_actualRate = 0.0;
    m_sampleTimes.clear();
    clearErrors();
    
    // Start the thread
    m_isRunning = true;
    m_isPaused = false;
    m_stopRequested = false;
    
    start();
    
    emit acquisitionStarted();
    qDebug() << "Data acquisition started at" << m_acquisitionRateHz << "Hz";
}

void DataAcquisitionThread::stopAcquisition()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning) {
        return; // Not running
    }
    
    m_stopRequested = true;
    m_pauseCondition.wakeAll(); // Wake up if paused
    
    locker.unlock();
    
    // Wait for thread to finish
    if (!wait(5000)) { // 5 second timeout
        terminate(); // Force termination if needed
        wait(1000);
    }
    
    locker.relock();
    m_isRunning = false;
    m_isPaused = false;
    
    emit acquisitionStopped();
    qDebug() << "Data acquisition stopped";
}

void DataAcquisitionThread::pauseAcquisition()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning || m_isPaused) {
        return;
    }
    
    m_isPaused = true;
    emit acquisitionPaused();
    qDebug() << "Data acquisition paused";
}

void DataAcquisitionThread::resumeAcquisition()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isRunning || !m_isPaused) {
        return;
    }
    
    m_isPaused = false;
    m_pauseCondition.wakeAll();
    emit acquisitionResumed();
    qDebug() << "Data acquisition resumed";
}

void DataAcquisitionThread::setAcquisitionRate(int rateHz)
{
    QMutexLocker locker(&m_mutex);
    
    if (rateHz < 1 || rateHz > 10000) {
        setError(QString("Invalid acquisition rate: %1 Hz (valid range: 1-10000)").arg(rateHz));
        return;
    }
    
    m_acquisitionRateHz = rateHz;
    updateAcquisitionInterval();
    
    qDebug() << "Acquisition rate set to" << rateHz << "Hz";
}

void DataAcquisitionThread::setBufferSize(int maxSamples)
{
    QMutexLocker locker(&m_mutex);
    
    m_maxBufferSize = qMax(100, maxSamples); // Minimum 100 samples
    m_dataQueue.setMaxSize(m_maxBufferSize);
    
    // Trim buffer if necessary
    while (m_dataBuffer.size() > m_maxBufferSize) {
        m_dataBuffer.removeFirst();
    }
}

void DataAcquisitionThread::addHardwareDevice(std::shared_ptr<HardwareInterface> device)
{
    QMutexLocker locker(&m_mutex);
    
    if (!device) {
        setError("Cannot add null hardware device");
        return;
    }
    
    if (m_hardwareDevices.contains(device)) {
        return; // Already added
    }
    
    m_hardwareDevices.append(device);
    connectHardwareSignals(device);
    
    qDebug() << "Added hardware device:" << device->deviceName();
}

void DataAcquisitionThread::removeHardwareDevice(std::shared_ptr<HardwareInterface> device)
{
    QMutexLocker locker(&m_mutex);
    
    if (!device) {
        return;
    }
    
    int index = m_hardwareDevices.indexOf(device);
    if (index >= 0) {
        disconnectHardwareSignals(device);
        m_hardwareDevices.removeAt(index);
        qDebug() << "Removed hardware device:" << device->deviceName();
    }
}

void DataAcquisitionThread::clearHardwareDevices()
{
    QMutexLocker locker(&m_mutex);
    
    for (auto device : m_hardwareDevices) {
        disconnectHardwareSignals(device);
    }
    
    m_hardwareDevices.clear();
    qDebug() << "Cleared all hardware devices";
}

QList<std::shared_ptr<HardwareInterface>> DataAcquisitionThread::hardwareDevices() const
{
    QMutexLocker locker(&m_mutex);
    return m_hardwareDevices;
}

bool DataAcquisitionThread::getLatestData(TestData& data)
{
    return m_dataQueue.dequeue(data);
}

QList<TestData> DataAcquisitionThread::getBufferedData(int maxSamples)
{
    QMutexLocker locker(&m_mutex);
    
    if (maxSamples < 0 || maxSamples >= m_dataBuffer.size()) {
        return m_dataBuffer;
    }
    
    // Return the most recent samples
    int startIndex = m_dataBuffer.size() - maxSamples;
    return m_dataBuffer.mid(startIndex);
}

void DataAcquisitionThread::clearDataBuffer()
{
    QMutexLocker locker(&m_mutex);
    m_dataBuffer.clear();
    m_dataQueue.clear();
}

void DataAcquisitionThread::clearErrors()
{
    QMutexLocker locker(&m_mutex);
    m_lastError.clear();
}

void DataAcquisitionThread::run()
{
    qDebug() << "Data acquisition thread started";
    
    m_rateTimer.start();
    m_acquisitionTimer.start();
    
    while (!m_stopRequested) {
        // Check if paused
        {
            QMutexLocker locker(&m_mutex);
            while (m_isPaused && !m_stopRequested) {
                m_pauseCondition.wait(&m_mutex);
            }
        }
        
        if (m_stopRequested) {
            break;
        }
        
        // Collect data from hardware
        try {
            TestData data = collectDataFromHardware();
            
            if (data.isValid()) {
                // Add to queue and buffer
                m_dataQueue.enqueue(data);
                addToBuffer(data);
                
                // Update statistics
                updateStatistics();
                
                // Emit signal
                emit dataReady(data);
            } else {
                m_droppedSamples++;
            }
        } catch (const std::exception& e) {
            setError(QString("Data collection error: %1").arg(e.what()));
            m_droppedSamples++;
        }
        
        // Wait for next sample
        if (!waitForNextSample()) {
            break; // Stop requested during wait
        }
    }
    
    qDebug() << "Data acquisition thread finished";
}

TestData DataAcquisitionThread::collectDataFromHardware()
{
    TestData data(QDateTime::currentDateTime());
    
    QMutexLocker locker(&m_mutex);
    
    // Collect data from all hardware devices
    for (auto device : m_hardwareDevices) {
        if (!device->isConnected()) {
            continue; // Skip disconnected devices
        }
        
        try {
            QMap<QString, QVariant> deviceData = device->readAllValues();
            
            // Map device data to TestData fields
            if (device->deviceType() == HardwareInterface::DeviceType::PressureSensor) {
                auto pressureSensor = std::dynamic_pointer_cast<PressureSensor>(device);
                if (pressureSensor) {
                    double pressure = deviceData.value("pressure", 0.0).toDouble();
                    
                    switch (pressureSensor->pressureType()) {
                        case PressureSensor::PressureType::CPA:
                            data.setPressureCPA(pressure);
                            break;
                        case PressureSensor::PressureType::CPB:
                            data.setPressureCPB(pressure);
                            break;
                        case PressureSensor::PressureType::Supply:
                            data.setPressureSupply(pressure);
                            break;
                        case PressureSensor::PressureType::Return:
                            data.setPressureReturn(pressure);
                            break;
                    }
                }
            } else if (device->deviceType() == HardwareInterface::DeviceType::FlowMeter) {
                double flow = deviceData.value("flow", 0.0).toDouble();
                data.setFlowRate(flow);
            } else if (device->deviceType() == HardwareInterface::DeviceType::TemperatureSensor) {
                auto tempSensor = std::dynamic_pointer_cast<TemperatureSensor>(device);
                if (tempSensor) {
                    double temperature = deviceData.value("temperature", 20.0).toDouble();
                    
                    switch (tempSensor->temperatureLocation()) {
                        case TemperatureSensor::TemperatureLocation::FluidTank:
                        case TemperatureSensor::TemperatureLocation::SystemReturn:
                            data.setTemperatureFluid(temperature);
                            break;
                        case TemperatureSensor::TemperatureLocation::Ambient:
                            data.setTemperatureAmbient(temperature);
                            break;
                        default:
                            data.setTemperatureFluid(temperature);
                            break;
                    }
                }
            } else if (device->deviceType() == HardwareInterface::DeviceType::ActuatorControl) {
                data.setActuatorPosition(deviceData.value("position", 0.0).toDouble());
                data.setActuatorVelocity(deviceData.value("velocity", 0.0).toDouble());
                data.setActuatorForce(deviceData.value("force", 0.0).toDouble());
            }
            
        } catch (const std::exception& e) {
            handleHardwareError(device, QString("Read error: %1").arg(e.what()));
        }
    }
    
    return data;
}

void DataAcquisitionThread::updateStatistics()
{
    m_totalSamples++;
    
    // Calculate actual acquisition rate
    if (m_totalSamples % 10 == 0) { // Update every 10 samples
        calculateActualRate();
        emit statisticsUpdated(m_totalSamples, m_actualRate, m_droppedSamples);
    }
}

void DataAcquisitionThread::calculateActualRate()
{
    qint64 currentTime = m_acquisitionTimer.elapsed();
    m_sampleTimes.append(currentTime);
    
    // Keep only recent samples for rate calculation
    while (m_sampleTimes.size() > RATE_CALCULATION_SAMPLES) {
        m_sampleTimes.removeFirst();
    }
    
    if (m_sampleTimes.size() >= 2) {
        qint64 timeSpan = m_sampleTimes.last() - m_sampleTimes.first();
        if (timeSpan > 0) {
            m_actualRate = (m_sampleTimes.size() - 1) * 1000.0 / timeSpan;
        }
    }
}

void DataAcquisitionThread::addToBuffer(const TestData& data)
{
    m_dataBuffer.append(data);
    
    // Remove old data if buffer is full
    while (m_dataBuffer.size() > m_maxBufferSize) {
        m_dataBuffer.removeFirst();
    }
}

void DataAcquisitionThread::validateHardwareDevices()
{
    for (auto device : m_hardwareDevices) {
        if (!device->isConnected()) {
            setError(QString("Hardware device not connected: %1").arg(device->deviceName()));
            return;
        }
    }
}

void DataAcquisitionThread::setError(const QString& error)
{
    m_lastError = error;
    emit errorOccurred(error);
    qWarning() << "Data acquisition error:" << error;
}

void DataAcquisitionThread::handleHardwareError(std::shared_ptr<HardwareInterface> device, const QString& error)
{
    QString fullError = QString("Hardware error from %1: %2").arg(device->deviceName(), error);
    setError(fullError);
}

void DataAcquisitionThread::updateAcquisitionInterval()
{
    m_acquisitionIntervalMs = 1000 / m_acquisitionRateHz;
}

bool DataAcquisitionThread::waitForNextSample()
{
    // Simple sleep-based timing (could be improved with high-resolution timers)
    msleep(m_acquisitionIntervalMs);
    return !m_stopRequested;
}

void DataAcquisitionThread::connectHardwareSignals(std::shared_ptr<HardwareInterface> device)
{
    connect(device.get(), &HardwareInterface::errorOccurred,
            this, &DataAcquisitionThread::onHardwareError);
    connect(device.get(), &HardwareInterface::dataReceived,
            this, &DataAcquisitionThread::onHardwareDataReceived);
}

void DataAcquisitionThread::disconnectHardwareSignals(std::shared_ptr<HardwareInterface> device)
{
    disconnect(device.get(), nullptr, this, nullptr);
}

void DataAcquisitionThread::onHardwareError(const QString& error)
{
    HardwareInterface* device = qobject_cast<HardwareInterface*>(sender());
    if (device) {
        handleHardwareError(std::shared_ptr<HardwareInterface>(device), error);
    }
}

void DataAcquisitionThread::onHardwareDataReceived(const QString& parameter, const QVariant& value)
{
    QMutexLocker locker(&m_mutex);
    m_latestReadings[parameter] = value;
}
