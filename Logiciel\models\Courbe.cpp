#include "Courbe.h"
#include <QDebug>
#include <QtMath>
#include <algorithm>

Q_LOGGING_CATEGORY(courbeModelLog, "hydraulic.models.courbe")

Courbe::Courbe(QObject *parent)
    : QObject(parent)
    , m_id(0)
    , m_essaiId(0)
    , m_typeCourbe("pressure_vs_time")
    , m_uniteX("s")
    , m_uniteY("bar")
{
    connectSignals();
}

Courbe::Courbe(const Courbe& other)
    : QObject(other.parent())
    , m_id(other.m_id)
    , m_essaiId(other.m_essaiId)
    , m_typeCourbe(other.m_typeCourbe)
    , m_pointsDonnees(other.m_pointsDonnees)
    , m_uniteX(other.m_uniteX)
    , m_uniteY(other.m_uniteY)
{
    connectSignals();
}

Courbe& Courbe::operator=(const Courbe& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_essaiId = other.m_essaiId;
        m_typeCourbe = other.m_typeCourbe;
        m_pointsDonnees = other.m_pointsDonnees;
        m_uniteX = other.m_uniteX;
        m_uniteY = other.m_uniteY;
        
        emit courbeDataChanged();
        validateAndEmitSignals();
    }
    return *this;
}

void Courbe::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged(id);
        emit courbeDataChanged();
    }
}

void Courbe::setEssaiId(int essaiId)
{
    if (m_essaiId != essaiId) {
        m_essaiId = essaiId;
        emit essaiIdChanged(essaiId);
        emit courbeDataChanged();
        validateAndEmitSignals();
    }
}

void Courbe::setTypeCourbe(const QString& typeCourbe)
{
    if (m_typeCourbe != typeCourbe) {
        m_typeCourbe = typeCourbe.toLower();
        emit typeCourbeChanged(m_typeCourbe);
        emit courbeDataChanged();
        validateAndEmitSignals();
        
        // Update default units for the new curve type
        auto defaultUnits = getDefaultUnits(curveType());
        setUniteX(defaultUnits.first);
        setUniteY(defaultUnits.second);
    }
}

void Courbe::setPointsDonnees(const QJsonArray& pointsDonnees)
{
    if (m_pointsDonnees != pointsDonnees) {
        m_pointsDonnees = pointsDonnees;
        emit pointsDonneesChanged(pointsDonnees);
        emit courbeDataChanged();
        validateAndEmitSignals();
    }
}

void Courbe::setUniteX(const QString& uniteX)
{
    if (m_uniteX != uniteX) {
        m_uniteX = uniteX;
        emit uniteXChanged(uniteX);
        emit courbeDataChanged();
    }
}

void Courbe::setUniteY(const QString& uniteY)
{
    if (m_uniteY != uniteY) {
        m_uniteY = uniteY;
        emit uniteYChanged(uniteY);
        emit courbeDataChanged();
    }
}

Courbe::CurveType Courbe::curveType() const
{
    return curveTypeFromString(m_typeCourbe);
}

void Courbe::setCurveType(CurveType type)
{
    setTypeCourbe(curveTypeToString(type));
}

QString Courbe::curveTypeDisplayName() const
{
    switch (curveType()) {
        case CurveType::PressureVsTime: return "Pressure vs Time";
        case CurveType::FlowVsTime: return "Flow vs Time";
        case CurveType::PositionVsTime: return "Position vs Time";
        case CurveType::VelocityVsTime: return "Velocity vs Time";
        case CurveType::ForceVsTime: return "Force vs Time";
        case CurveType::TemperatureVsTime: return "Temperature vs Time";
        case CurveType::PressureVsPosition: return "Pressure vs Position";
        case CurveType::FlowVsPosition: return "Flow vs Position";
        case CurveType::ForceVsPosition: return "Force vs Position";
        case CurveType::EfficiencyVsTime: return "Efficiency vs Time";
        case CurveType::PowerVsTime: return "Power vs Time";
        case CurveType::PressureVsFlow: return "Pressure vs Flow";
        case CurveType::Custom: return "Custom Curve";
    }
    return "Unknown Curve";
}

QString Courbe::curveTypeToString(CurveType type)
{
    switch (type) {
        case CurveType::PressureVsTime: return "pressure_vs_time";
        case CurveType::FlowVsTime: return "flow_vs_time";
        case CurveType::PositionVsTime: return "position_vs_time";
        case CurveType::VelocityVsTime: return "velocity_vs_time";
        case CurveType::ForceVsTime: return "force_vs_time";
        case CurveType::TemperatureVsTime: return "temperature_vs_time";
        case CurveType::PressureVsPosition: return "pressure_vs_position";
        case CurveType::FlowVsPosition: return "flow_vs_position";
        case CurveType::ForceVsPosition: return "force_vs_position";
        case CurveType::EfficiencyVsTime: return "efficiency_vs_time";
        case CurveType::PowerVsTime: return "power_vs_time";
        case CurveType::PressureVsFlow: return "pressure_vs_flow";
        case CurveType::Custom: return "custom";
    }
    return "pressure_vs_time";
}

Courbe::CurveType Courbe::curveTypeFromString(const QString& typeStr)
{
    QString type = typeStr.toLower();
    if (type == "flow_vs_time") return CurveType::FlowVsTime;
    if (type == "position_vs_time") return CurveType::PositionVsTime;
    if (type == "velocity_vs_time") return CurveType::VelocityVsTime;
    if (type == "force_vs_time") return CurveType::ForceVsTime;
    if (type == "temperature_vs_time") return CurveType::TemperatureVsTime;
    if (type == "pressure_vs_position") return CurveType::PressureVsPosition;
    if (type == "flow_vs_position") return CurveType::FlowVsPosition;
    if (type == "force_vs_position") return CurveType::ForceVsPosition;
    if (type == "efficiency_vs_time") return CurveType::EfficiencyVsTime;
    if (type == "power_vs_time") return CurveType::PowerVsTime;
    if (type == "pressure_vs_flow") return CurveType::PressureVsFlow;
    if (type == "custom") return CurveType::Custom;
    return CurveType::PressureVsTime;
}

void Courbe::addDataPoint(double x, double y)
{
    addDataPoint(QPointF(x, y));
}

void Courbe::addDataPoint(const QPointF& point)
{
    if (dataPointCount() >= MAX_DATA_POINTS) {
        qCWarning(courbeModelLog) << "Maximum data points reached, cannot add more";
        return;
    }
    
    QJsonArray points = m_pointsDonnees;
    QJsonObject pointObj;
    pointObj["x"] = point.x();
    pointObj["y"] = point.y();
    points.append(pointObj);
    
    setPointsDonnees(points);
    emit dataPointAdded(point);
}

void Courbe::addDataPoints(const QList<QPointF>& points)
{
    if (dataPointCount() + points.size() > MAX_DATA_POINTS) {
        qCWarning(courbeModelLog) << "Adding points would exceed maximum, truncating";
    }
    
    QJsonArray jsonPoints = m_pointsDonnees;
    int remainingSpace = MAX_DATA_POINTS - dataPointCount();
    
    for (int i = 0; i < qMin(points.size(), remainingSpace); ++i) {
        const QPointF& point = points[i];
        QJsonObject pointObj;
        pointObj["x"] = point.x();
        pointObj["y"] = point.y();
        jsonPoints.append(pointObj);
    }
    
    setPointsDonnees(jsonPoints);
}

void Courbe::removeDataPoint(int index)
{
    if (index < 0 || index >= dataPointCount()) {
        return;
    }
    
    QJsonArray points = m_pointsDonnees;
    points.removeAt(index);
    setPointsDonnees(points);
    emit dataPointRemoved(index);
}

void Courbe::clearDataPoints()
{
    setPointsDonnees(QJsonArray());
    emit dataPointsCleared();
}

QList<QPointF> Courbe::dataPoints() const
{
    return jsonArrayToPoints(m_pointsDonnees);
}

QPointF Courbe::dataPoint(int index) const
{
    if (index < 0 || index >= dataPointCount()) {
        return QPointF();
    }
    
    QJsonObject pointObj = m_pointsDonnees[index].toObject();
    return QPointF(pointObj["x"].toDouble(), pointObj["y"].toDouble());
}

int Courbe::dataPointCount() const
{
    return m_pointsDonnees.size();
}

bool Courbe::hasDataPoints() const
{
    return dataPointCount() > 0;
}

double Courbe::minX() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return 0.0;
    
    auto minIt = std::min_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.x() < b.x(); });
    return minIt->x();
}

double Courbe::maxX() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return 0.0;
    
    auto maxIt = std::max_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.x() < b.x(); });
    return maxIt->x();
}

double Courbe::minY() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return 0.0;
    
    auto minIt = std::min_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.y() < b.y(); });
    return minIt->y();
}

double Courbe::maxY() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return 0.0;
    
    auto maxIt = std::max_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.y() < b.y(); });
    return maxIt->y();
}

QPointF Courbe::minPoint() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return QPointF();
    
    auto minIt = std::min_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.y() < b.y(); });
    return *minIt;
}

QPointF Courbe::maxPoint() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return QPointF();
    
    auto maxIt = std::max_element(points.begin(), points.end(),
                                  [](const QPointF& a, const QPointF& b) { return a.y() < b.y(); });
    return *maxIt;
}

double Courbe::averageY() const
{
    QList<QPointF> points = dataPoints();
    if (points.isEmpty()) return 0.0;
    
    double sum = 0.0;
    for (const QPointF& point : points) {
        sum += point.y();
    }
    return sum / points.size();
}

double Courbe::standardDeviationY() const
{
    QList<QPointF> points = dataPoints();
    if (points.size() < 2) return 0.0;
    
    double mean = averageY();
    QList<double> yValues;
    for (const QPointF& point : points) {
        yValues.append(point.y());
    }
    
    double variance = calculateVariance(yValues, mean);
    return qSqrt(variance);
}

QList<QPointF> Courbe::getDataInRange(double minX, double maxX) const
{
    QList<QPointF> points = dataPoints();
    QList<QPointF> filteredPoints;
    
    for (const QPointF& point : points) {
        if (point.x() >= minX && point.x() <= maxX) {
            filteredPoints.append(point);
        }
    }
    
    return filteredPoints;
}

QList<QPointF> Courbe::downsample(int maxPoints) const
{
    QList<QPointF> points = dataPoints();
    if (points.size() <= maxPoints) {
        return points;
    }
    
    QList<QPointF> downsampled;
    double step = static_cast<double>(points.size() - 1) / (maxPoints - 1);
    
    for (int i = 0; i < maxPoints; ++i) {
        int index = qRound(i * step);
        if (index < points.size()) {
            downsampled.append(points[index]);
        }
    }
    
    return downsampled;
}

double Courbe::interpolateY(double x) const
{
    QList<QPointF> points = dataPoints();
    if (points.size() < 2) {
        return points.isEmpty() ? 0.0 : points.first().y();
    }
    
    // Sort points by x coordinate
    std::sort(points.begin(), points.end(),
              [](const QPointF& a, const QPointF& b) { return a.x() < b.x(); });
    
    // Find surrounding points
    for (int i = 0; i < points.size() - 1; ++i) {
        if (x >= points[i].x() && x <= points[i + 1].x()) {
            // Linear interpolation
            double x1 = points[i].x();
            double y1 = points[i].y();
            double x2 = points[i + 1].x();
            double y2 = points[i + 1].y();
            
            if (qAbs(x2 - x1) < INTERPOLATION_TOLERANCE) {
                return y1; // Avoid division by zero
            }
            
            return y1 + (y2 - y1) * (x - x1) / (x2 - x1);
        }
    }
    
    // Extrapolation
    if (x < points.first().x()) {
        return points.first().y();
    } else {
        return points.last().y();
    }
}

bool Courbe::isValid() const
{
    return validate().isEmpty();
}

QStringList Courbe::validate() const
{
    QStringList errors;
    
    if (m_id <= 0) {
        errors << "Invalid courbe ID";
    }
    
    if (m_essaiId <= 0) {
        errors << "Invalid essai ID";
    }
    
    if (!isValidCurveType(m_typeCourbe)) {
        errors << "Invalid curve type";
    }
    
    if (!areDataPointsValid()) {
        errors << "Invalid data points";
    }
    
    if (!areUnitsValid()) {
        errors << "Invalid units";
    }
    
    return errors;
}

bool Courbe::areDataPointsValid() const
{
    QList<QPointF> points = dataPoints();
    
    for (const QPointF& point : points) {
        if (qIsNaN(point.x()) || qIsNaN(point.y()) || 
            qIsInf(point.x()) || qIsInf(point.y())) {
            return false;
        }
    }
    
    return true;
}

bool Courbe::areUnitsValid() const
{
    return !m_uniteX.isEmpty() && !m_uniteY.isEmpty();
}

QJsonObject Courbe::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["essai_id"] = m_essaiId;
    json["type_courbe"] = m_typeCourbe;
    json["points_donnees"] = m_pointsDonnees;
    json["unite_x"] = m_uniteX;
    json["unite_y"] = m_uniteY;
    json["display_name"] = displayName();
    json["curve_type_display"] = curveTypeDisplayName();
    json["data_point_count"] = dataPointCount();
    json["has_data_points"] = hasDataPoints();
    json["min_x"] = minX();
    json["max_x"] = maxX();
    json["min_y"] = minY();
    json["max_y"] = maxY();
    json["average_y"] = averageY();
    return json;
}

void Courbe::fromJson(const QJsonObject& json)
{
    setId(json["id"].toInt());
    setEssaiId(json["essai_id"].toInt());
    setTypeCourbe(json["type_courbe"].toString());
    setPointsDonnees(json["points_donnees"].toArray());
    setUniteX(json["unite_x"].toString());
    setUniteY(json["unite_y"].toString());
}

QJsonObject Courbe::toJsonPartial() const
{
    QJsonObject json;
    if (m_id > 0) json["id"] = m_id;
    if (m_essaiId > 0) json["essai_id"] = m_essaiId;
    if (!m_typeCourbe.isEmpty()) json["type_courbe"] = m_typeCourbe;
    if (!m_pointsDonnees.isEmpty()) json["points_donnees"] = m_pointsDonnees;
    if (!m_uniteX.isEmpty()) json["unite_x"] = m_uniteX;
    if (!m_uniteY.isEmpty()) json["unite_y"] = m_uniteY;
    return json;
}

QString Courbe::displayName() const
{
    return QString("%1 #%2").arg(curveTypeDisplayName()).arg(m_id);
}

QString Courbe::description() const
{
    return QString("%1 (%2 points)").arg(curveTypeDisplayName()).arg(dataPointCount());
}

QString Courbe::formattedRange() const
{
    if (!hasDataPoints()) {
        return "No data";
    }
    
    return QString("X: %1 to %2 %3, Y: %4 to %5 %6")
               .arg(minX()).arg(maxX()).arg(m_uniteX)
               .arg(minY()).arg(maxY()).arg(m_uniteY);
}

bool Courbe::operator==(const Courbe& other) const
{
    return m_id == other.m_id &&
           m_essaiId == other.m_essaiId &&
           m_typeCourbe == other.m_typeCourbe &&
           m_pointsDonnees == other.m_pointsDonnees;
}

Courbe Courbe::createFromJson(const QJsonObject& json)
{
    Courbe courbe;
    courbe.fromJson(json);
    return courbe;
}

Courbe Courbe::createNew(int essaiId, CurveType type)
{
    Courbe courbe;
    courbe.setEssaiId(essaiId);
    courbe.setCurveType(type);
    
    auto defaultUnits = getDefaultUnits(type);
    courbe.setUniteX(defaultUnits.first);
    courbe.setUniteY(defaultUnits.second);
    
    return courbe;
}

QStringList Courbe::getAllCurveTypes()
{
    return QStringList() << "pressure_vs_time" << "flow_vs_time" << "position_vs_time" 
                         << "velocity_vs_time" << "force_vs_time" << "temperature_vs_time"
                         << "pressure_vs_position" << "flow_vs_position" << "force_vs_position"
                         << "efficiency_vs_time" << "power_vs_time" << "pressure_vs_flow" << "custom";
}

QPair<QString, QString> Courbe::getDefaultUnits(CurveType type)
{
    switch (type) {
        case CurveType::PressureVsTime: return {"s", "bar"};
        case CurveType::FlowVsTime: return {"s", "L/min"};
        case CurveType::PositionVsTime: return {"s", "mm"};
        case CurveType::VelocityVsTime: return {"s", "mm/s"};
        case CurveType::ForceVsTime: return {"s", "N"};
        case CurveType::TemperatureVsTime: return {"s", "°C"};
        case CurveType::PressureVsPosition: return {"mm", "bar"};
        case CurveType::FlowVsPosition: return {"mm", "L/min"};
        case CurveType::ForceVsPosition: return {"mm", "N"};
        case CurveType::EfficiencyVsTime: return {"s", "%"};
        case CurveType::PowerVsTime: return {"s", "kW"};
        case CurveType::PressureVsFlow: return {"L/min", "bar"};
        case CurveType::Custom: return {"", ""};
    }
    return {"", ""};
}

void Courbe::connectSignals()
{
    // Connect property change signals to validation
    connect(this, &Courbe::essaiIdChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Courbe::typeCourbeChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Courbe::pointsDonneesChanged, this, [this]() { validateAndEmitSignals(); });
}

void Courbe::validateAndEmitSignals()
{
    bool valid = isValid();
    emit validationChanged(valid);
    
    if (!valid) {
        QStringList errors = validate();
        qCWarning(courbeModelLog) << "Courbe validation failed:" << errors;
    }
}

bool Courbe::isValidCurveType(const QString& type) const
{
    QStringList validTypes = getAllCurveTypes();
    return validTypes.contains(type);
}

QJsonArray Courbe::pointsToJsonArray(const QList<QPointF>& points) const
{
    QJsonArray array;
    for (const QPointF& point : points) {
        QJsonObject pointObj;
        pointObj["x"] = point.x();
        pointObj["y"] = point.y();
        array.append(pointObj);
    }
    return array;
}

QList<QPointF> Courbe::jsonArrayToPoints(const QJsonArray& array) const
{
    QList<QPointF> points;
    for (const QJsonValue& value : array) {
        QJsonObject pointObj = value.toObject();
        double x = pointObj["x"].toDouble();
        double y = pointObj["y"].toDouble();
        points.append(QPointF(x, y));
    }
    return points;
}

double Courbe::calculateVariance(const QList<double>& values, double mean) const
{
    if (values.size() < 2) return 0.0;
    
    double sumSquaredDiffs = 0.0;
    for (double value : values) {
        double diff = value - mean;
        sumSquaredDiffs += diff * diff;
    }
    
    return sumSquaredDiffs / (values.size() - 1);
}
