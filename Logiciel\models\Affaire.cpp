#include "Affaire.h"
#include <QRegularExpression>
#include <QDebug>
#include <QUuid>

Q_LOGGING_CATEGORY(affaireModelLog, "hydraulic.models.affaire")

Affaire::Affaire(QObject *parent)
    : QObject(parent)
    , m_id(0)
    , m_statut("draft")
    , m_dateCreation(QDateTime::currentDateTime())
{
    connectSignals();
}

Affaire::Affaire(const Affaire& other)
    : QObject(other.parent())
    , m_id(other.m_id)
    , m_numero(other.m_numero)
    , m_client(other.m_client)
    , m_description(other.m_description)
    , m_statut(other.m_statut)
    , m_dateCreation(other.m_dateCreation)
{
    connectSignals();
}

Affaire& Affaire::operator=(const Affaire& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_numero = other.m_numero;
        m_client = other.m_client;
        m_description = other.m_description;
        m_statut = other.m_statut;
        m_dateCreation = other.m_dateCreation;
        
        emit affaireDataChanged();
        validateAndEmitSignals();
    }
    return *this;
}

void Affaire::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged(id);
        emit affaireDataChanged();
    }
}

void Affaire::setNumero(const QString& numero)
{
    if (m_numero != numero) {
        m_numero = numero.trimmed().toUpper();
        emit numeroChanged(m_numero);
        emit affaireDataChanged();
        validateAndEmitSignals();
    }
}

void Affaire::setClient(const QString& client)
{
    if (m_client != client) {
        m_client = client.trimmed();
        emit clientChanged(m_client);
        emit affaireDataChanged();
        validateAndEmitSignals();
    }
}

void Affaire::setDescription(const QString& description)
{
    if (m_description != description) {
        m_description = description.trimmed();
        emit descriptionChanged(m_description);
        emit affaireDataChanged();
        validateAndEmitSignals();
    }
}

void Affaire::setStatut(const QString& statut)
{
    if (m_statut != statut) {
        Status oldStatus = affaireStatus();
        m_statut = statut.toLower();
        Status newStatus = affaireStatus();
        
        emit statutChanged(m_statut);
        emit affaireDataChanged();
        emit statusChanged(oldStatus, newStatus);
        validateAndEmitSignals();
    }
}

void Affaire::setDateCreation(const QDateTime& dateCreation)
{
    if (m_dateCreation != dateCreation) {
        m_dateCreation = dateCreation;
        emit dateCreationChanged(dateCreation);
        emit affaireDataChanged();
    }
}

Affaire::Status Affaire::affaireStatus() const
{
    return statusFromString(m_statut);
}

void Affaire::setAffaireStatus(Status status)
{
    setStatut(statusToString(status));
}

QString Affaire::statusDisplayName() const
{
    switch (affaireStatus()) {
        case Status::Draft: return "Draft";
        case Status::InProgress: return "In Progress";
        case Status::OnHold: return "On Hold";
        case Status::Completed: return "Completed";
        case Status::Cancelled: return "Cancelled";
        case Status::Archived: return "Archived";
    }
    return "Unknown";
}

QString Affaire::statusToString(Status status)
{
    switch (status) {
        case Status::Draft: return "draft";
        case Status::InProgress: return "in_progress";
        case Status::OnHold: return "on_hold";
        case Status::Completed: return "completed";
        case Status::Cancelled: return "cancelled";
        case Status::Archived: return "archived";
    }
    return "draft";
}

Affaire::Status Affaire::statusFromString(const QString& statusStr)
{
    QString status = statusStr.toLower();
    if (status == "in_progress") return Status::InProgress;
    if (status == "on_hold") return Status::OnHold;
    if (status == "completed") return Status::Completed;
    if (status == "cancelled") return Status::Cancelled;
    if (status == "archived") return Status::Archived;
    return Status::Draft;
}

bool Affaire::canBeModified() const
{
    Status status = affaireStatus();
    return status == Status::Draft || status == Status::InProgress || status == Status::OnHold;
}

bool Affaire::canBeDeleted() const
{
    Status status = affaireStatus();
    return status == Status::Draft || status == Status::Cancelled;
}

bool Affaire::canAddTests() const
{
    Status status = affaireStatus();
    return status == Status::Draft || status == Status::InProgress;
}

bool Affaire::isActive() const
{
    Status status = affaireStatus();
    return status == Status::InProgress;
}

bool Affaire::isCompleted() const
{
    Status status = affaireStatus();
    return status == Status::Completed || status == Status::Archived;
}

bool Affaire::isValid() const
{
    return validate().isEmpty();
}

QStringList Affaire::validate() const
{
    QStringList errors;
    
    if (m_id <= 0) {
        errors << "Invalid affaire ID";
    }
    
    if (!isNumeroValid()) {
        errors << QString("Numero must be %1-%2 characters and contain only letters, numbers, and hyphens")
                     .arg(MIN_NUMERO_LENGTH).arg(MAX_NUMERO_LENGTH);
    }
    
    if (!isClientValid()) {
        errors << QString("Client name must be %1-%2 characters")
                     .arg(MIN_CLIENT_LENGTH).arg(MAX_CLIENT_LENGTH);
    }
    
    if (!isDescriptionValid()) {
        errors << QString("Description must be %1-%2 characters")
                     .arg(MIN_DESCRIPTION_LENGTH).arg(MAX_DESCRIPTION_LENGTH);
    }
    
    if (!isStatutValid()) {
        errors << "Invalid status";
    }
    
    if (!m_dateCreation.isValid()) {
        errors << "Invalid creation date";
    }
    
    return errors;
}

bool Affaire::isNumeroValid() const
{
    return isValidNumero(m_numero);
}

bool Affaire::isClientValid() const
{
    return isValidClient(m_client);
}

bool Affaire::isDescriptionValid() const
{
    return isValidDescription(m_description);
}

bool Affaire::isStatutValid() const
{
    QStringList validStatuses = getAllStatuses();
    return validStatuses.contains(m_statut);
}

QJsonObject Affaire::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["numero"] = m_numero;
    json["client"] = m_client;
    json["description"] = m_description;
    json["statut"] = m_statut;
    json["date_creation"] = m_dateCreation.toString(Qt::ISODate);
    json["display_name"] = displayName();
    json["status_display"] = statusDisplayName();
    json["can_be_modified"] = canBeModified();
    json["can_be_deleted"] = canBeDeleted();
    json["can_add_tests"] = canAddTests();
    json["is_active"] = isActive();
    json["is_completed"] = isCompleted();
    json["days_since_creation"] = daysSinceCreation();
    json["formatted_creation_date"] = formattedCreationDate();
    return json;
}

void Affaire::fromJson(const QJsonObject& json)
{
    setId(json["id"].toInt());
    setNumero(json["numero"].toString());
    setClient(json["client"].toString());
    setDescription(json["description"].toString());
    setStatut(json["statut"].toString());
    
    QString dateCreationStr = json["date_creation"].toString();
    if (!dateCreationStr.isEmpty()) {
        setDateCreation(QDateTime::fromString(dateCreationStr, Qt::ISODate));
    }
}

QJsonObject Affaire::toJsonPartial() const
{
    QJsonObject json;
    if (m_id > 0) json["id"] = m_id;
    if (!m_numero.isEmpty()) json["numero"] = m_numero;
    if (!m_client.isEmpty()) json["client"] = m_client;
    if (!m_description.isEmpty()) json["description"] = m_description;
    if (!m_statut.isEmpty()) json["statut"] = m_statut;
    return json;
}

QString Affaire::displayName() const
{
    if (!m_numero.isEmpty() && !m_client.isEmpty()) {
        return QString("%1 - %2").arg(m_numero, m_client);
    }
    if (!m_numero.isEmpty()) {
        return m_numero;
    }
    if (!m_client.isEmpty()) {
        return m_client;
    }
    return QString("Affaire %1").arg(m_id);
}

QString Affaire::shortDescription() const
{
    if (m_description.length() <= 50) {
        return m_description;
    }
    return m_description.left(47) + "...";
}

int Affaire::daysSinceCreation() const
{
    if (!m_dateCreation.isValid()) {
        return 0;
    }
    return m_dateCreation.daysTo(QDateTime::currentDateTime());
}

QString Affaire::formattedCreationDate() const
{
    if (!m_dateCreation.isValid()) {
        return "Invalid Date";
    }
    return m_dateCreation.toString("dd/MM/yyyy hh:mm");
}

bool Affaire::operator==(const Affaire& other) const
{
    return m_id == other.m_id &&
           m_numero == other.m_numero &&
           m_client == other.m_client &&
           m_statut == other.m_statut;
}

Affaire Affaire::createFromJson(const QJsonObject& json)
{
    Affaire affaire;
    affaire.fromJson(json);
    return affaire;
}

Affaire Affaire::createNew(const QString& numero, const QString& client, const QString& description)
{
    Affaire affaire;
    affaire.setNumero(numero.isEmpty() ? generateNumero() : numero);
    affaire.setClient(client);
    affaire.setDescription(description);
    affaire.setAffaireStatus(Status::Draft);
    return affaire;
}

QStringList Affaire::getAllStatuses()
{
    return QStringList() << "draft" << "in_progress" << "on_hold" << "completed" << "cancelled" << "archived";
}

QString Affaire::generateNumero()
{
    // Generate a unique affaire number based on current date and UUID
    QString dateStr = QDateTime::currentDateTime().toString("yyyyMMdd");
    QString uuid = QUuid::createUuid().toString(QUuid::WithoutBraces).left(8).toUpper();
    return QString("AFF-%1-%2").arg(dateStr, uuid);
}

void Affaire::connectSignals()
{
    // Connect property change signals to validation
    connect(this, &Affaire::numeroChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Affaire::clientChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Affaire::descriptionChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Affaire::statutChanged, this, [this]() { validateAndEmitSignals(); });
}

void Affaire::validateAndEmitSignals()
{
    bool valid = isValid();
    emit validationChanged(valid);
    
    if (!valid) {
        QStringList errors = validate();
        qCWarning(affaireModelLog) << "Affaire validation failed:" << errors;
    }
}

bool Affaire::isValidNumero(const QString& numero) const
{
    if (numero.length() < MIN_NUMERO_LENGTH || numero.length() > MAX_NUMERO_LENGTH) {
        return false;
    }
    
    QRegularExpression numeroRegex(R"(^[A-Z0-9\-]+$)");
    return numeroRegex.match(numero).hasMatch();
}

bool Affaire::isValidClient(const QString& client) const
{
    return client.length() >= MIN_CLIENT_LENGTH && client.length() <= MAX_CLIENT_LENGTH;
}

bool Affaire::isValidDescription(const QString& description) const
{
    return description.length() >= MIN_DESCRIPTION_LENGTH && description.length() <= MAX_DESCRIPTION_LENGTH;
}
