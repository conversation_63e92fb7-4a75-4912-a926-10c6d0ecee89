#include "SimulationControlWidget.h"
#include <QDebug>

// Constants
const double SimulationControlWidget::PRESSURE_MIN = 0.0;
const double SimulationControlWidget::PRESSURE_MAX = 300.0;
const double SimulationControlWidget::FLOW_MIN = -100.0;
const double SimulationControlWidget::FLOW_MAX = 100.0;
const double SimulationControlWidget::TEMPERATURE_MIN = -10.0;
const double SimulationControlWidget::TEMPERATURE_MAX = 100.0;
const double SimulationControlWidget::POSITION_MIN = 0.0;
const double SimulationControlWidget::POSITION_MAX = 500.0;
const double SimulationControlWidget::VELOCITY_MIN = -200.0;
const double SimulationControlWidget::VELOCITY_MAX = 200.0;
const double SimulationControlWidget::FORCE_MIN = -50000.0;
const double SimulationControlWidget::FORCE_MAX = 50000.0;

SimulationControlWidget::SimulationControlWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_simulationRunning(false)
    , m_simulationPaused(false)
    , m_manualControlEnabled(false)
    , m_statusUpdateTimer(new QTimer(this))
{
    setupUI();

    // Setup status update timer
    m_statusUpdateTimer->setInterval(1000); // Update every second
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &SimulationControlWidget::updateDeviceStatus);
    m_statusUpdateTimer->start();
}

SimulationControlWidget::~SimulationControlWidget()
{
    stopSimulation();
}

void SimulationControlWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);

    setupSimulationControls();
    setupManualControls();
    setupTestScenarios();
    setupDeviceStatus();

    updateUIState();
}

void SimulationControlWidget::setupSimulationControls()
{
    m_simulationControlGroup = new QGroupBox("Simulation Control", this);
    QVBoxLayout* layout = new QVBoxLayout(m_simulationControlGroup);

    // Status label
    m_simulationStatusLabel = new QLabel("Simulation Stopped", this);
    m_simulationStatusLabel->setStyleSheet("font-weight: bold; color: red;");
    layout->addWidget(m_simulationStatusLabel);

    // Progress bar
    m_simulationProgressBar = new QProgressBar(this);
    m_simulationProgressBar->setVisible(false);
    layout->addWidget(m_simulationProgressBar);

    // Control buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    m_startStopButton = new QPushButton("Start Simulation", this);
    m_startStopButton->setStyleSheet("QPushButton { background-color: green; color: white; font-weight: bold; }");
    connect(m_startStopButton, &QPushButton::clicked, this, &SimulationControlWidget::onStartStopClicked);
    buttonLayout->addWidget(m_startStopButton);

    m_pauseResumeButton = new QPushButton("Pause", this);
    m_pauseResumeButton->setEnabled(false);
    connect(m_pauseResumeButton, &QPushButton::clicked, this, &SimulationControlWidget::onPauseResumeClicked);
    buttonLayout->addWidget(m_pauseResumeButton);

    m_resetButton = new QPushButton("Reset", this);
    connect(m_resetButton, &QPushButton::clicked, this, &SimulationControlWidget::onResetSimulationClicked);
    buttonLayout->addWidget(m_resetButton);

    layout->addLayout(buttonLayout);

    // Manual control checkbox
    m_manualControlCheckBox = new QCheckBox("Enable Manual Control", this);
    connect(m_manualControlCheckBox, &QCheckBox::toggled, this, &SimulationControlWidget::onManualControlToggled);
    layout->addWidget(m_manualControlCheckBox);

    m_mainLayout->addWidget(m_simulationControlGroup);
}

void SimulationControlWidget::setupManualControls()
{
    m_manualControlGroup = new QGroupBox("Manual Control", this);
    m_manualControlGroup->setEnabled(false);

    QHBoxLayout* layout = new QHBoxLayout(m_manualControlGroup);

    // Add control groups
    layout->addWidget(createPressureControlGroup());
    layout->addWidget(createFlowControlGroup());
    layout->addWidget(createTemperatureControlGroup());
    layout->addWidget(createActuatorControlGroup());

    m_mainLayout->addWidget(m_manualControlGroup);
}

QGroupBox* SimulationControlWidget::createPressureControlGroup()
{
    QGroupBox* group = new QGroupBox("Pressure Control", this);
    QGridLayout* layout = new QGridLayout(group);

    // CPA Pressure
    layout->addWidget(new QLabel("CPA (bar):"), 0, 0);
    m_pressureCPASpinBox = new QDoubleSpinBox(this);
    m_pressureCPASpinBox->setRange(PRESSURE_MIN, PRESSURE_MAX);
    m_pressureCPASpinBox->setDecimals(1);
    m_pressureCPASpinBox->setSuffix(" bar");
    connect(m_pressureCPASpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onPressureValueChanged);
    layout->addWidget(m_pressureCPASpinBox, 0, 1);

    m_pressureCPASlider = new QSlider(Qt::Horizontal, this);
    m_pressureCPASlider->setRange(0, static_cast<int>(PRESSURE_MAX * 10));
    connect(m_pressureCPASlider, &QSlider::valueChanged, this, [this](int value) {
        m_pressureCPASpinBox->setValue(value / 10.0);
    });
    layout->addWidget(m_pressureCPASlider, 0, 2);

    // CPB Pressure
    layout->addWidget(new QLabel("CPB (bar):"), 1, 0);
    m_pressureCPBSpinBox = new QDoubleSpinBox(this);
    m_pressureCPBSpinBox->setRange(PRESSURE_MIN, PRESSURE_MAX);
    m_pressureCPBSpinBox->setDecimals(1);
    m_pressureCPBSpinBox->setSuffix(" bar");
    connect(m_pressureCPBSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onPressureValueChanged);
    layout->addWidget(m_pressureCPBSpinBox, 1, 1);

    m_pressureCPBSlider = new QSlider(Qt::Horizontal, this);
    m_pressureCPBSlider->setRange(0, static_cast<int>(PRESSURE_MAX * 10));
    connect(m_pressureCPBSlider, &QSlider::valueChanged, this, [this](int value) {
        m_pressureCPBSpinBox->setValue(value / 10.0);
    });
    layout->addWidget(m_pressureCPBSlider, 1, 2);

    // Supply Pressure
    layout->addWidget(new QLabel("Supply (bar):"), 2, 0);
    m_pressureSupplySpinBox = new QDoubleSpinBox(this);
    m_pressureSupplySpinBox->setRange(PRESSURE_MIN, PRESSURE_MAX);
    m_pressureSupplySpinBox->setDecimals(1);
    m_pressureSupplySpinBox->setSuffix(" bar");
    m_pressureSupplySpinBox->setValue(200.0);
    connect(m_pressureSupplySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onPressureValueChanged);
    layout->addWidget(m_pressureSupplySpinBox, 2, 1, 1, 2);

    // Return Pressure
    layout->addWidget(new QLabel("Return (bar):"), 3, 0);
    m_pressureReturnSpinBox = new QDoubleSpinBox(this);
    m_pressureReturnSpinBox->setRange(PRESSURE_MIN, 20.0);
    m_pressureReturnSpinBox->setDecimals(1);
    m_pressureReturnSpinBox->setSuffix(" bar");
    m_pressureReturnSpinBox->setValue(2.0);
    connect(m_pressureReturnSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onPressureValueChanged);
    layout->addWidget(m_pressureReturnSpinBox, 3, 1, 1, 2);

    return group;
}

QGroupBox* SimulationControlWidget::createFlowControlGroup()
{
    QGroupBox* group = new QGroupBox("Flow Control", this);
    QGridLayout* layout = new QGridLayout(group);

    // Flow Rate
    layout->addWidget(new QLabel("Flow Rate:"), 0, 0);
    m_flowRateSpinBox = new QDoubleSpinBox(this);
    m_flowRateSpinBox->setRange(FLOW_MIN, FLOW_MAX);
    m_flowRateSpinBox->setDecimals(1);
    m_flowRateSpinBox->setSuffix(" L/min");
    connect(m_flowRateSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onFlowValueChanged);
    layout->addWidget(m_flowRateSpinBox, 0, 1);

    m_flowRateSlider = new QSlider(Qt::Horizontal, this);
    m_flowRateSlider->setRange(static_cast<int>(FLOW_MIN * 10), static_cast<int>(FLOW_MAX * 10));
    connect(m_flowRateSlider, &QSlider::valueChanged, this, [this](int value) {
        m_flowRateSpinBox->setValue(value / 10.0);
    });
    layout->addWidget(m_flowRateSlider, 0, 2);

    // Flow Direction
    layout->addWidget(new QLabel("Direction:"), 1, 0);
    m_flowDirectionComboBox = new QComboBox(this);
    m_flowDirectionComboBox->addItems({"Bidirectional", "Forward", "Reverse"});
    connect(m_flowDirectionComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SimulationControlWidget::onFlowValueChanged);
    layout->addWidget(m_flowDirectionComboBox, 1, 1, 1, 2);

    return group;
}

QGroupBox* SimulationControlWidget::createTemperatureControlGroup()
{
    QGroupBox* group = new QGroupBox("Temperature Control", this);
    QGridLayout* layout = new QGridLayout(group);

    // Fluid Temperature
    layout->addWidget(new QLabel("Fluid Temp:"), 0, 0);
    m_temperatureFluidSpinBox = new QDoubleSpinBox(this);
    m_temperatureFluidSpinBox->setRange(TEMPERATURE_MIN, TEMPERATURE_MAX);
    m_temperatureFluidSpinBox->setDecimals(1);
    m_temperatureFluidSpinBox->setSuffix(" °C");
    m_temperatureFluidSpinBox->setValue(45.0);
    connect(m_temperatureFluidSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onTemperatureValueChanged);
    layout->addWidget(m_temperatureFluidSpinBox, 0, 1);

    m_temperatureFluidSlider = new QSlider(Qt::Horizontal, this);
    m_temperatureFluidSlider->setRange(static_cast<int>(TEMPERATURE_MIN * 10), static_cast<int>(TEMPERATURE_MAX * 10));
    m_temperatureFluidSlider->setValue(450); // 45.0°C
    connect(m_temperatureFluidSlider, &QSlider::valueChanged, this, [this](int value) {
        m_temperatureFluidSpinBox->setValue(value / 10.0);
    });
    layout->addWidget(m_temperatureFluidSlider, 0, 2);

    // Ambient Temperature
    layout->addWidget(new QLabel("Ambient Temp:"), 1, 0);
    m_temperatureAmbientSpinBox = new QDoubleSpinBox(this);
    m_temperatureAmbientSpinBox->setRange(TEMPERATURE_MIN, 50.0);
    m_temperatureAmbientSpinBox->setDecimals(1);
    m_temperatureAmbientSpinBox->setSuffix(" °C");
    m_temperatureAmbientSpinBox->setValue(20.0);
    connect(m_temperatureAmbientSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onTemperatureValueChanged);
    layout->addWidget(m_temperatureAmbientSpinBox, 1, 1, 1, 2);

    return group;
}

void SimulationControlWidget::setupTestScenarios()
{
    m_testScenariosGroup = new QGroupBox("Test Scenarios", this);
    QHBoxLayout* layout = new QHBoxLayout(m_testScenariosGroup);

    layout->addWidget(createPressureTestGroup());
    layout->addWidget(createFlowTestGroup());
    layout->addWidget(createTemperatureTestGroup());
    layout->addWidget(createSystemTestGroup());

    m_mainLayout->addWidget(m_testScenariosGroup);
}

QGroupBox* SimulationControlWidget::createPressureTestGroup()
{
    QGroupBox* group = new QGroupBox("Pressure Tests", this);
    QGridLayout* layout = new QGridLayout(group);

    // Pressure Test
    layout->addWidget(new QLabel("Target:"), 0, 0);
    m_pressureTestTargetSpinBox = new QDoubleSpinBox(this);
    m_pressureTestTargetSpinBox->setRange(0.0, PRESSURE_MAX);
    m_pressureTestTargetSpinBox->setSuffix(" bar");
    m_pressureTestTargetSpinBox->setValue(150.0);
    layout->addWidget(m_pressureTestTargetSpinBox, 0, 1);

    layout->addWidget(new QLabel("Duration:"), 1, 0);
    m_pressureTestDurationSpinBox = new QSpinBox(this);
    m_pressureTestDurationSpinBox->setRange(1000, 60000);
    m_pressureTestDurationSpinBox->setSuffix(" ms");
    m_pressureTestDurationSpinBox->setValue(5000);
    layout->addWidget(m_pressureTestDurationSpinBox, 1, 1);

    m_runPressureTestButton = new QPushButton("Run Pressure Test", this);
    connect(m_runPressureTestButton, &QPushButton::clicked, this, &SimulationControlWidget::onRunPressureTestClicked);
    layout->addWidget(m_runPressureTestButton, 2, 0, 1, 2);

    // Leak Test
    layout->addWidget(new QLabel("Leak Pressure:"), 3, 0);
    m_leakTestPressureSpinBox = new QDoubleSpinBox(this);
    m_leakTestPressureSpinBox->setRange(0.0, PRESSURE_MAX);
    m_leakTestPressureSpinBox->setSuffix(" bar");
    m_leakTestPressureSpinBox->setValue(100.0);
    layout->addWidget(m_leakTestPressureSpinBox, 3, 1);

    layout->addWidget(new QLabel("Leak Rate:"), 4, 0);
    m_leakTestRateSpinBox = new QDoubleSpinBox(this);
    m_leakTestRateSpinBox->setRange(0.0, 10.0);
    m_leakTestRateSpinBox->setSuffix(" bar/s");
    m_leakTestRateSpinBox->setValue(0.1);
    m_leakTestRateSpinBox->setDecimals(2);
    layout->addWidget(m_leakTestRateSpinBox, 4, 1);

    m_runLeakTestButton = new QPushButton("Run Leak Test", this);
    connect(m_runLeakTestButton, &QPushButton::clicked, this, &SimulationControlWidget::onRunLeakTestClicked);
    layout->addWidget(m_runLeakTestButton, 5, 0, 1, 2);

    return group;
}

QGroupBox* SimulationControlWidget::createFlowTestGroup()
{
    QGroupBox* group = new QGroupBox("Flow Tests", this);
    QGridLayout* layout = new QGridLayout(group);

    layout->addWidget(new QLabel("Target Flow:"), 0, 0);
    m_flowTestTargetSpinBox = new QDoubleSpinBox(this);
    m_flowTestTargetSpinBox->setRange(0.0, FLOW_MAX);
    m_flowTestTargetSpinBox->setSuffix(" L/min");
    m_flowTestTargetSpinBox->setValue(50.0);
    layout->addWidget(m_flowTestTargetSpinBox, 0, 1);

    layout->addWidget(new QLabel("Distance:"), 1, 0);
    m_flowTestDistanceSpinBox = new QDoubleSpinBox(this);
    m_flowTestDistanceSpinBox->setRange(10.0, POSITION_MAX);
    m_flowTestDistanceSpinBox->setSuffix(" mm");
    m_flowTestDistanceSpinBox->setValue(100.0);
    layout->addWidget(m_flowTestDistanceSpinBox, 1, 1);

    m_runFlowTestButton = new QPushButton("Run Flow Test", this);
    connect(m_runFlowTestButton, &QPushButton::clicked, this, &SimulationControlWidget::onRunFlowTestClicked);
    layout->addWidget(m_runFlowTestButton, 2, 0, 1, 2);

    return group;
}

QGroupBox* SimulationControlWidget::createTemperatureTestGroup()
{
    QGroupBox* group = new QGroupBox("Temperature Tests", this);
    QGridLayout* layout = new QGridLayout(group);

    layout->addWidget(new QLabel("Target Temp:"), 0, 0);
    m_tempTestTargetSpinBox = new QDoubleSpinBox(this);
    m_tempTestTargetSpinBox->setRange(TEMPERATURE_MIN, TEMPERATURE_MAX);
    m_tempTestTargetSpinBox->setSuffix(" °C");
    m_tempTestTargetSpinBox->setValue(60.0);
    layout->addWidget(m_tempTestTargetSpinBox, 0, 1);

    layout->addWidget(new QLabel("Duration:"), 1, 0);
    m_tempTestDurationSpinBox = new QSpinBox(this);
    m_tempTestDurationSpinBox->setRange(5000, 300000);
    m_tempTestDurationSpinBox->setSuffix(" ms");
    m_tempTestDurationSpinBox->setValue(10000);
    layout->addWidget(m_tempTestDurationSpinBox, 1, 1);

    m_runTempTestButton = new QPushButton("Run Temp Test", this);
    connect(m_runTempTestButton, &QPushButton::clicked, this, &SimulationControlWidget::onRunTemperatureTestClicked);
    layout->addWidget(m_runTempTestButton, 2, 0, 1, 2);

    return group;
}

QGroupBox* SimulationControlWidget::createSystemTestGroup()
{
    QGroupBox* group = new QGroupBox("System Tests", this);
    QGridLayout* layout = new QGridLayout(group);

    // Cycle Test
    layout->addWidget(new QLabel("Min Pos:"), 0, 0);
    m_cycleTestMinSpinBox = new QDoubleSpinBox(this);
    m_cycleTestMinSpinBox->setRange(POSITION_MIN, POSITION_MAX);
    m_cycleTestMinSpinBox->setSuffix(" mm");
    m_cycleTestMinSpinBox->setValue(0.0);
    layout->addWidget(m_cycleTestMinSpinBox, 0, 1);

    layout->addWidget(new QLabel("Max Pos:"), 1, 0);
    m_cycleTestMaxSpinBox = new QDoubleSpinBox(this);
    m_cycleTestMaxSpinBox->setRange(POSITION_MIN, POSITION_MAX);
    m_cycleTestMaxSpinBox->setSuffix(" mm");
    m_cycleTestMaxSpinBox->setValue(400.0);
    layout->addWidget(m_cycleTestMaxSpinBox, 1, 1);

    layout->addWidget(new QLabel("Cycles:"), 2, 0);
    m_cycleTestCountSpinBox = new QSpinBox(this);
    m_cycleTestCountSpinBox->setRange(1, 1000);
    m_cycleTestCountSpinBox->setValue(5);
    layout->addWidget(m_cycleTestCountSpinBox, 2, 1);

    layout->addWidget(new QLabel("Velocity:"), 3, 0);
    m_cycleTestVelocitySpinBox = new QDoubleSpinBox(this);
    m_cycleTestVelocitySpinBox->setRange(1.0, VELOCITY_MAX);
    m_cycleTestVelocitySpinBox->setSuffix(" mm/s");
    m_cycleTestVelocitySpinBox->setValue(50.0);
    layout->addWidget(m_cycleTestVelocitySpinBox, 3, 1);

    m_runCycleTestButton = new QPushButton("Run Cycle Test", this);
    connect(m_runCycleTestButton, &QPushButton::clicked, this, &SimulationControlWidget::onRunCycleTestClicked);
    layout->addWidget(m_runCycleTestButton, 4, 0, 1, 2);

    return group;
}

void SimulationControlWidget::setupDeviceStatus()
{
    m_deviceStatusGroup = new QGroupBox("Device Status", this);
    QVBoxLayout* layout = new QVBoxLayout(m_deviceStatusGroup);

    m_statusTextEdit = new QTextEdit(this);
    m_statusTextEdit->setMaximumHeight(150);
    m_statusTextEdit->setReadOnly(true);
    m_statusTextEdit->setFont(QFont("Courier", 9));
    layout->addWidget(m_statusTextEdit);

    m_mainLayout->addWidget(m_deviceStatusGroup);
}

// Slot implementations
void SimulationControlWidget::onStartStopClicked()
{
    if (m_simulationRunning) {
        stopSimulation();
    } else {
        startSimulation();
    }
}

void SimulationControlWidget::onPauseResumeClicked()
{
    if (m_simulationPaused) {
        resumeSimulation();
    } else {
        pauseSimulation();
    }
}

void SimulationControlWidget::onManualControlToggled(bool enabled)
{
    enableManualControl(enabled);
}

void SimulationControlWidget::onResetSimulationClicked()
{
    resetToDefaults();
}

void SimulationControlWidget::startSimulation()
{
    m_simulationRunning = true;
    m_simulationPaused = false;

    // Connect all devices
    connectDeviceSignals();

    // Start all hardware devices
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            sensor->connect();
            sensor->startSimulation();
        }
    }

    if (m_flowMeter) {
        m_flowMeter->connect();
        m_flowMeter->startSimulation();
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            sensor->connect();
            sensor->startSimulation();
        }
    }

    if (m_actuatorControl) {
        m_actuatorControl->connect();
    }

    updateUIState();
    emit simulationStarted();
    qDebug() << "Simulation started";
}

void SimulationControlWidget::stopSimulation()
{
    m_simulationRunning = false;
    m_simulationPaused = false;

    // Stop all hardware devices
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            sensor->stopSimulation();
            sensor->disconnect();
        }
    }

    if (m_flowMeter) {
        m_flowMeter->stopSimulation();
        m_flowMeter->disconnect();
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            sensor->stopSimulation();
            sensor->disconnect();
        }
    }

    if (m_actuatorControl) {
        m_actuatorControl->disconnect();
    }

    // Disconnect signals
    disconnectDeviceSignals();

    updateUIState();
    emit simulationStopped();
    qDebug() << "Simulation stopped";
}

QGroupBox* SimulationControlWidget::createActuatorControlGroup()
{
    QGroupBox* group = new QGroupBox("Actuator Control", this);
    QGridLayout* layout = new QGridLayout(group);

    // Position
    layout->addWidget(new QLabel("Position:"), 0, 0);
    m_actuatorPositionSpinBox = new QDoubleSpinBox(this);
    m_actuatorPositionSpinBox->setRange(POSITION_MIN, POSITION_MAX);
    m_actuatorPositionSpinBox->setDecimals(1);
    m_actuatorPositionSpinBox->setSuffix(" mm");
    connect(m_actuatorPositionSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onActuatorControlChanged);
    layout->addWidget(m_actuatorPositionSpinBox, 0, 1);

    // Velocity
    layout->addWidget(new QLabel("Velocity:"), 1, 0);
    m_actuatorVelocitySpinBox = new QDoubleSpinBox(this);
    m_actuatorVelocitySpinBox->setRange(VELOCITY_MIN, VELOCITY_MAX);
    m_actuatorVelocitySpinBox->setDecimals(1);
    m_actuatorVelocitySpinBox->setSuffix(" mm/s");
    connect(m_actuatorVelocitySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onActuatorControlChanged);
    layout->addWidget(m_actuatorVelocitySpinBox, 1, 1);

    // Force
    layout->addWidget(new QLabel("Force:"), 2, 0);
    m_actuatorForceSpinBox = new QDoubleSpinBox(this);
    m_actuatorForceSpinBox->setRange(FORCE_MIN, FORCE_MAX);
    m_actuatorForceSpinBox->setDecimals(0);
    m_actuatorForceSpinBox->setSuffix(" N");
    connect(m_actuatorForceSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SimulationControlWidget::onActuatorControlChanged);
    layout->addWidget(m_actuatorForceSpinBox, 2, 1);

    // Control Mode
    layout->addWidget(new QLabel("Mode:"), 3, 0);
    m_actuatorModeComboBox = new QComboBox(this);
    m_actuatorModeComboBox->addItems({"Position", "Velocity", "Force", "Manual", "Disabled"});
    m_actuatorModeComboBox->setCurrentText("Manual");
    connect(m_actuatorModeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SimulationControlWidget::onActuatorControlChanged);
    layout->addWidget(m_actuatorModeComboBox, 3, 1);

    // Control buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    m_actuatorExtendButton = new QPushButton("Extend", this);
    m_actuatorExtendButton->setStyleSheet("background-color: lightgreen;");
    buttonLayout->addWidget(m_actuatorExtendButton);

    m_actuatorRetractButton = new QPushButton("Retract", this);
    m_actuatorRetractButton->setStyleSheet("background-color: lightblue;");
    buttonLayout->addWidget(m_actuatorRetractButton);

    m_actuatorStopButton = new QPushButton("Stop", this);
    m_actuatorStopButton->setStyleSheet("background-color: yellow;");
    buttonLayout->addWidget(m_actuatorStopButton);

    m_actuatorEmergencyStopButton = new QPushButton("E-STOP", this);
    m_actuatorEmergencyStopButton->setStyleSheet("background-color: red; color: white; font-weight: bold;");
    buttonLayout->addWidget(m_actuatorEmergencyStopButton);

    layout->addLayout(buttonLayout, 4, 0, 1, 2);

    return group;
}

// Missing slot implementations
void SimulationControlWidget::onPressureValueChanged()
{
    // Handle pressure value changes
    if (m_manualControlEnabled && m_simulationRunning) {
        // Apply manual pressure values to sensors
        applyManualValues();
    }
}

void SimulationControlWidget::onFlowValueChanged()
{
    // Handle flow value changes
    if (m_manualControlEnabled && m_simulationRunning) {
        // Apply manual flow values to flow meter
        applyManualValues();
    }
}

void SimulationControlWidget::onTemperatureValueChanged()
{
    // Handle temperature value changes
    if (m_manualControlEnabled && m_simulationRunning) {
        // Apply manual temperature values to sensors
        applyManualValues();
    }
}

void SimulationControlWidget::onActuatorControlChanged()
{
    // Handle actuator control changes
    if (m_manualControlEnabled && m_simulationRunning && m_actuatorControl) {
        // Apply manual actuator values
        applyManualValues();
    }
}

void SimulationControlWidget::onRunPressureTestClicked()
{
    // Start pressure test scenario
    if (!m_simulationRunning) {
        return;
    }

    double targetPressure = m_pressureTestTargetSpinBox->value();
    int duration = m_pressureTestDurationSpinBox->value();

    emit testScenarioStarted("Pressure Test");
    qDebug() << "Starting pressure test: target =" << targetPressure << "bar, duration =" << duration << "s";

    // TODO: Implement pressure test logic
}

void SimulationControlWidget::onRunFlowTestClicked()
{
    // Start flow test scenario
    if (!m_simulationRunning) {
        return;
    }

    double targetFlow = m_flowTestTargetSpinBox->value();
    double distance = m_flowTestDistanceSpinBox->value();

    emit testScenarioStarted("Flow Test");
    qDebug() << "Starting flow test: target =" << targetFlow << "L/min, distance =" << distance << "mm";

    // TODO: Implement flow test logic
}

void SimulationControlWidget::onRunTemperatureTestClicked()
{
    // Start temperature test scenario
    if (!m_simulationRunning) {
        return;
    }

    double targetTemp = m_tempTestTargetSpinBox->value();
    int duration = m_tempTestDurationSpinBox->value();

    emit testScenarioStarted("Temperature Test");
    qDebug() << "Starting temperature test: target =" << targetTemp << "°C, duration =" << duration << "s";

    // TODO: Implement temperature test logic
}

void SimulationControlWidget::onRunCycleTestClicked()
{
    // Start cycle test scenario
    if (!m_simulationRunning) {
        return;
    }

    double minPos = m_cycleTestMinSpinBox->value();
    double maxPos = m_cycleTestMaxSpinBox->value();
    int cycles = m_cycleTestCountSpinBox->value();
    double velocity = m_cycleTestVelocitySpinBox->value();

    emit testScenarioStarted("Cycle Test");
    qDebug() << "Starting cycle test: range" << minPos << "-" << maxPos << "mm," << cycles << "cycles at" << velocity << "mm/s";

    // TODO: Implement cycle test logic
}

void SimulationControlWidget::onRunLeakTestClicked()
{
    // Start leak test scenario
    if (!m_simulationRunning) {
        return;
    }

    double testPressure = m_leakTestPressureSpinBox->value();
    double maxLeakRate = m_leakTestRateSpinBox->value();

    emit testScenarioStarted("Leak Test");
    qDebug() << "Starting leak test: pressure =" << testPressure << "bar, max leak rate =" << maxLeakRate << "mL/min";

    // TODO: Implement leak test logic
}

void SimulationControlWidget::onDeviceStatusChanged()
{
    // Handle device status changes
    updateDeviceStatus();
}

void SimulationControlWidget::updateDeviceStatus()
{
    // Update device status display
    QString statusText;

    statusText += "=== DEVICE STATUS ===\n";
    statusText += QString("Simulation: %1\n").arg(m_simulationRunning ? "RUNNING" : "STOPPED");
    statusText += QString("Manual Control: %1\n").arg(m_manualControlEnabled ? "ENABLED" : "DISABLED");
    statusText += "\n";

    // Pressure sensors status
    statusText += "Pressure Sensors:\n";
    for (int i = 0; i < m_pressureSensors.size(); ++i) {
        auto sensor = m_pressureSensors[i];
        if (sensor) {
            double pressure = sensor->readValue("pressure").toDouble();
            statusText += QString("  Sensor %1: %2 (%3 bar)\n")
                         .arg(i + 1)
                         .arg(sensor->isConnected() ? "CONNECTED" : "DISCONNECTED")
                         .arg(pressure, 0, 'f', 1);
        }
    }

    // Flow meter status
    if (m_flowMeter) {
        double flow = m_flowMeter->readValue("flow_rate").toDouble();
        statusText += QString("Flow Meter: %1 (%2 L/min)\n")
                     .arg(m_flowMeter->isConnected() ? "CONNECTED" : "DISCONNECTED")
                     .arg(flow, 0, 'f', 1);
    }

    // Temperature sensors status
    statusText += "Temperature Sensors:\n";
    for (int i = 0; i < m_temperatureSensors.size(); ++i) {
        auto sensor = m_temperatureSensors[i];
        if (sensor) {
            double temperature = sensor->readValue("temperature").toDouble();
            statusText += QString("  Sensor %1: %2 (%3 °C)\n")
                         .arg(i + 1)
                         .arg(sensor->isConnected() ? "CONNECTED" : "DISCONNECTED")
                         .arg(temperature, 0, 'f', 1);
        }
    }

    // Actuator status
    if (m_actuatorControl) {
        double position = m_actuatorControl->readValue("position").toDouble();
        double velocity = m_actuatorControl->readValue("velocity").toDouble();
        double force = m_actuatorControl->readValue("force").toDouble();

        statusText += QString("Actuator: %1 (Pos: %2 mm, Vel: %3 mm/s, Force: %4 N)\n")
                     .arg(m_actuatorControl->isConnected() ? "CONNECTED" : "DISCONNECTED")
                     .arg(position, 0, 'f', 1)
                     .arg(velocity, 0, 'f', 1)
                     .arg(force, 0, 'f', 0);
    }

    m_statusTextEdit->setPlainText(statusText);
}

void SimulationControlWidget::updateUIState()
{
    // Update UI state based on simulation status
    m_startStopButton->setText(m_simulationRunning ? "Stop" : "Start");
    m_startStopButton->setStyleSheet(m_simulationRunning ?
        "background-color: red; color: white;" :
        "background-color: green; color: white;");

    m_pauseResumeButton->setText(m_simulationPaused ? "Resume" : "Pause");
    m_pauseResumeButton->setEnabled(m_simulationRunning);

    m_resetButton->setEnabled(!m_simulationRunning);
    m_manualControlCheckBox->setEnabled(m_simulationRunning);

    // Update status label
    QString status;
    if (m_simulationRunning) {
        status = m_simulationPaused ? "PAUSED" : "RUNNING";
    } else {
        status = "STOPPED";
    }
    m_simulationStatusLabel->setText(QString("Status: %1").arg(status));

    // Enable/disable manual controls based on manual control mode
    if (m_manualControlGroup) {
        m_manualControlGroup->setEnabled(m_manualControlEnabled && m_simulationRunning);
    }

    // Enable/disable test scenarios based on simulation state
    if (m_testScenariosGroup) {
        m_testScenariosGroup->setEnabled(m_simulationRunning && !m_simulationPaused);
    }
}

void SimulationControlWidget::pauseSimulation()
{
    if (!m_simulationRunning || m_simulationPaused) {
        return;
    }

    m_simulationPaused = true;

    // Pause all hardware devices (stop simulation)
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            sensor->stopSimulation();
        }
    }

    if (m_flowMeter) {
        m_flowMeter->stopSimulation();
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            sensor->stopSimulation();
        }
    }

    if (m_actuatorControl) {
        // Actuator doesn't have pause/resume, just note the state
    }

    updateUIState();
    emit simulationPaused();
    qDebug() << "Simulation paused";
}

void SimulationControlWidget::resumeSimulation()
{
    if (!m_simulationRunning || !m_simulationPaused) {
        return;
    }

    m_simulationPaused = false;

    // Resume all hardware devices (start simulation)
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            sensor->startSimulation();
        }
    }

    if (m_flowMeter) {
        m_flowMeter->startSimulation();
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            sensor->startSimulation();
        }
    }

    if (m_actuatorControl) {
        // Actuator doesn't have pause/resume, just note the state
    }

    updateUIState();
    emit simulationResumed();
    qDebug() << "Simulation resumed";
}

void SimulationControlWidget::enableManualControl(bool enabled)
{
    m_manualControlEnabled = enabled;
    m_manualControlCheckBox->setChecked(enabled);

    updateUIState();
    emit manualControlToggled(enabled);

    qDebug() << "Manual control" << (enabled ? "enabled" : "disabled");
}

void SimulationControlWidget::resetToDefaults()
{
    // Reset all manual control values to defaults
    if (m_pressureCPASpinBox) m_pressureCPASpinBox->setValue(0.0);
    if (m_pressureCPBSpinBox) m_pressureCPBSpinBox->setValue(0.0);
    if (m_pressureSupplySpinBox) m_pressureSupplySpinBox->setValue(210.0);
    if (m_pressureReturnSpinBox) m_pressureReturnSpinBox->setValue(0.0);

    if (m_flowRateSpinBox) m_flowRateSpinBox->setValue(0.0);
    if (m_flowDirectionComboBox) m_flowDirectionComboBox->setCurrentIndex(0);

    if (m_temperatureFluidSpinBox) m_temperatureFluidSpinBox->setValue(20.0);
    if (m_temperatureAmbientSpinBox) m_temperatureAmbientSpinBox->setValue(20.0);

    if (m_actuatorPositionSpinBox) m_actuatorPositionSpinBox->setValue(0.0);
    if (m_actuatorVelocitySpinBox) m_actuatorVelocitySpinBox->setValue(0.0);
    if (m_actuatorForceSpinBox) m_actuatorForceSpinBox->setValue(0.0);
    if (m_actuatorModeComboBox) m_actuatorModeComboBox->setCurrentText("Manual");

    // Reset test scenario values
    if (m_pressureTestTargetSpinBox) m_pressureTestTargetSpinBox->setValue(100.0);
    if (m_pressureTestDurationSpinBox) m_pressureTestDurationSpinBox->setValue(30);

    if (m_flowTestTargetSpinBox) m_flowTestTargetSpinBox->setValue(10.0);
    if (m_flowTestDistanceSpinBox) m_flowTestDistanceSpinBox->setValue(100.0);

    if (m_tempTestTargetSpinBox) m_tempTestTargetSpinBox->setValue(40.0);
    if (m_tempTestDurationSpinBox) m_tempTestDurationSpinBox->setValue(60);

    if (m_cycleTestMinSpinBox) m_cycleTestMinSpinBox->setValue(0.0);
    if (m_cycleTestMaxSpinBox) m_cycleTestMaxSpinBox->setValue(100.0);
    if (m_cycleTestCountSpinBox) m_cycleTestCountSpinBox->setValue(10);
    if (m_cycleTestVelocitySpinBox) m_cycleTestVelocitySpinBox->setValue(50.0);

    if (m_leakTestPressureSpinBox) m_leakTestPressureSpinBox->setValue(150.0);
    if (m_leakTestRateSpinBox) m_leakTestRateSpinBox->setValue(5.0);

    qDebug() << "Reset to default values";
}

void SimulationControlWidget::connectDeviceSignals()
{
    // Connect signals from hardware devices
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            connect(sensor.get(), &HardwareInterface::dataReceived,
                   this, &SimulationControlWidget::onDeviceStatusChanged);
            connect(sensor.get(), &HardwareInterface::connectionStatusChanged,
                   this, &SimulationControlWidget::onDeviceStatusChanged);
        }
    }

    if (m_flowMeter) {
        connect(m_flowMeter.get(), &HardwareInterface::dataReceived,
               this, &SimulationControlWidget::onDeviceStatusChanged);
        connect(m_flowMeter.get(), &HardwareInterface::connectionStatusChanged,
               this, &SimulationControlWidget::onDeviceStatusChanged);
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            connect(sensor.get(), &HardwareInterface::dataReceived,
                   this, &SimulationControlWidget::onDeviceStatusChanged);
            connect(sensor.get(), &HardwareInterface::connectionStatusChanged,
                   this, &SimulationControlWidget::onDeviceStatusChanged);
        }
    }

    if (m_actuatorControl) {
        connect(m_actuatorControl.get(), &ActuatorControl::positionChanged,
               this, &SimulationControlWidget::onDeviceStatusChanged);
        connect(m_actuatorControl.get(), &ActuatorControl::velocityChanged,
               this, &SimulationControlWidget::onDeviceStatusChanged);
        connect(m_actuatorControl.get(), &ActuatorControl::forceChanged,
               this, &SimulationControlWidget::onDeviceStatusChanged);
        connect(m_actuatorControl.get(), &HardwareInterface::connectionStatusChanged,
               this, &SimulationControlWidget::onDeviceStatusChanged);
    }
}

void SimulationControlWidget::disconnectDeviceSignals()
{
    // Disconnect signals from hardware devices
    for (auto sensor : m_pressureSensors) {
        if (sensor) {
            disconnect(sensor.get(), nullptr, this, nullptr);
        }
    }

    if (m_flowMeter) {
        disconnect(m_flowMeter.get(), nullptr, this, nullptr);
    }

    for (auto sensor : m_temperatureSensors) {
        if (sensor) {
            disconnect(sensor.get(), nullptr, this, nullptr);
        }
    }

    if (m_actuatorControl) {
        disconnect(m_actuatorControl.get(), nullptr, this, nullptr);
    }
}

void SimulationControlWidget::applyManualValues()
{
    if (!m_manualControlEnabled || !m_simulationRunning) {
        return;
    }

    // Apply manual pressure values
    if (m_pressureCPASpinBox && !m_pressureSensors.isEmpty() && m_pressureSensors[0]) {
        m_pressureSensors[0]->setManualValue(m_pressureCPASpinBox->value());
        m_pressureSensors[0]->enableManualMode(true);
    }
    if (m_pressureCPBSpinBox && m_pressureSensors.size() > 1 && m_pressureSensors[1]) {
        m_pressureSensors[1]->setManualValue(m_pressureCPBSpinBox->value());
        m_pressureSensors[1]->enableManualMode(true);
    }

    // Apply manual flow values
    if (m_flowRateSpinBox && m_flowMeter) {
        m_flowMeter->setManualValue(m_flowRateSpinBox->value());
        m_flowMeter->enableManualMode(true);
    }

    // Apply manual temperature values
    if (m_temperatureFluidSpinBox && !m_temperatureSensors.isEmpty() && m_temperatureSensors[0]) {
        m_temperatureSensors[0]->setManualValue(m_temperatureFluidSpinBox->value());
        m_temperatureSensors[0]->enableManualMode(true);
    }
    if (m_temperatureAmbientSpinBox && m_temperatureSensors.size() > 1 && m_temperatureSensors[1]) {
        m_temperatureSensors[1]->setManualValue(m_temperatureAmbientSpinBox->value());
        m_temperatureSensors[1]->enableManualMode(true);
    }

    // Apply manual actuator values
    if (m_actuatorControl) {
        if (m_actuatorPositionSpinBox) {
            m_actuatorControl->moveToPosition(m_actuatorPositionSpinBox->value());
        }
        if (m_actuatorVelocitySpinBox) {
            m_actuatorControl->setVelocity(m_actuatorVelocitySpinBox->value());
        }
        if (m_actuatorForceSpinBox) {
            m_actuatorControl->setForce(m_actuatorForceSpinBox->value());
        }
    }
}
