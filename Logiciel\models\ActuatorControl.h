#ifndef ACTUATORCONTROL_H
#define ACTUATORCONTROL_H

#include "HardwareInterface.h"
#include <QTimer>

/**
 * @brief Simulated hydraulic actuator control
 * 
 * This class simulates hydraulic actuator control including
 * position control, velocity control, and force control for
 * hydraulic cylinder testing.
 */
class ActuatorControl : public HardwareInterface
{
    Q_OBJECT
    
public:
    enum class ControlMode {
        Position,
        Velocity,
        Force,
        Manual,
        Disabled
    };
    
    enum class ActuatorState {
        Stopped,
        Extending,
        Retracting,
        Holding,
        Error
    };
    
    explicit ActuatorControl(QObject *parent = nullptr);
    
    // HardwareInterface implementation
    bool connect() override;
    void disconnect() override;
    bool isConnected() const override;
    ConnectionStatus connectionStatus() const override;
    
    QString deviceName() const override;
    QString deviceVersion() const override;
    DeviceType deviceType() const override;
    bool isSimulated() const override { return true; }
    
    QVariant readValue(const QString& parameter) override;
    bool writeValue(const QString& parameter, const QVariant& value) override;
    QMap<QString, QVariant> readAllValues() override;
    
    bool configure(const QMap<QString, QVariant>& config) override;
    QMap<QString, QVariant> getConfiguration() const override;
    
    bool calibrate() override;
    bool isCalibrated() const override;
    QDateTime lastCalibrationDate() const override;
    
    bool performSelfTest() override;
    QString getLastError() const override;
    QMap<QString, QVariant> getDiagnosticInfo() const override;
    
    // Actuator-specific methods
    ControlMode controlMode() const { return m_controlMode; }
    void setControlMode(ControlMode mode);
    
    ActuatorState actuatorState() const { return m_actuatorState; }
    
    // Position control
    void moveToPosition(double position, double velocity = 50.0);
    void moveRelative(double distance, double velocity = 50.0);
    void setPositionLimits(double minPosition, double maxPosition);
    
    // Velocity control
    void setVelocity(double velocity);
    void setVelocityLimits(double maxVelocity);
    
    // Force control
    void setForce(double force);
    void setForceLimits(double maxForce);
    
    // Manual control
    void extend(double velocity = 25.0);
    void retract(double velocity = 25.0);
    void stop();
    void hold();
    
    // Emergency functions
    void emergencyStop();
    void resetEmergency();
    
    // Test sequences
    void runPositionTest(double startPos, double endPos, int cycles = 1);
    void runVelocityTest(double velocity, double distance);
    void runForceTest(double targetForce, int durationMs = 5000);
    void runCycleTest(double minPos, double maxPos, int cycles, double velocity = 50.0);
    
signals:
    void positionChanged(double position);
    void velocityChanged(double velocity);
    void forceChanged(double force);
    void controlModeChanged(ControlMode mode);
    void actuatorStateChanged(ActuatorState state);
    void positionReached(double position);
    void emergencyStopActivated();
    void testCompleted(const QString& testType);
    void limitReached(const QString& limitType);
    
private slots:
    void updateActuator();
    void onTestTimer();
    
private:
    QTimer* m_updateTimer;
    QTimer* m_testTimer;
    
    ControlMode m_controlMode;
    ActuatorState m_actuatorState;
    
    // Current values
    double m_currentPosition;
    double m_currentVelocity;
    double m_currentForce;
    
    // Target values
    double m_targetPosition;
    double m_targetVelocity;
    double m_targetForce;
    
    // Limits
    double m_minPosition;
    double m_maxPosition;
    double m_maxVelocity;
    double m_maxForce;
    
    // Control parameters
    double m_positionTolerance;
    double m_velocityTolerance;
    double m_forceTolerance;
    
    // Test parameters
    enum class TestMode {
        None,
        PositionTest,
        VelocityTest,
        ForceTest,
        CycleTest
    };
    
    TestMode m_currentTestMode;
    double m_testStartPos;
    double m_testEndPos;
    double m_testVelocity;
    double m_testForce;
    int m_testCycles;
    int m_currentTestCycle;
    int m_testDuration;
    QDateTime m_testStartTime;
    bool m_testDirection; // true = forward, false = reverse
    
    // Emergency state
    bool m_emergencyStop;
    
    // Simulation parameters
    double m_actuatorLength; // Total stroke length
    double m_actuatorDiameter; // Cylinder diameter
    double m_systemPressure; // Operating pressure
    
    void updatePosition();
    void updateVelocity();
    void updateForce();
    void updateState();
    
    void checkLimits();
    void checkPositionReached();
    
    // Test execution
    void executePositionTest();
    void executeVelocityTest();
    void executeForceTest();
    void executeCycleTest();
    
    // Physics simulation
    double calculateForceFromPressure(double pressure);
    double calculateVelocityFromFlow(double flow);
    double simulateInertia(double currentValue, double targetValue, double timeConstant);
    
    static const double DEFAULT_STROKE_LENGTH; // mm
    static const double DEFAULT_CYLINDER_DIAMETER; // mm
    static const double DEFAULT_MAX_VELOCITY; // mm/s
    static const double DEFAULT_MAX_FORCE; // N
    static const double UPDATE_INTERVAL_MS; // ms
};

#endif // ACTUATORCONTROL_H
