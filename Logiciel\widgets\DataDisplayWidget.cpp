#include "DataDisplayWidget.h"
#include <QApplication>
#include <QMessageBox>
#include <QJsonDocument>
#include <QJsonObject>
#include <QSettings>
#include <QDir>
#include <QMutexLocker>
#include <QtMath>
#include <QDebug>

Q_LOGGING_CATEGORY(dataDisplayLog, "hydraulic.widgets.datadisplay")

// Constants
const double DataDisplayWidget::PRESSURE_MIN = 0.0;
const double DataDisplayWidget::PRESSURE_MAX = 300.0;
const double DataDisplayWidget::FLOW_MIN = -100.0;
const double DataDisplayWidget::FLOW_MAX = 100.0;
const double DataDisplayWidget::TEMPERATURE_MIN = -10.0;
const double DataDisplayWidget::TEMPERATURE_MAX = 100.0;
const double DataDisplayWidget::POSITION_MIN = 0.0;
const double DataDisplayWidget::POSITION_MAX = 500.0;
const double DataDisplayWidget::VELOCITY_MIN = -200.0;
const double DataDisplayWidget::VELOCITY_MAX = 200.0;
const double DataDisplayWidget::FORCE_MIN = -50000.0;
const double DataDisplayWidget::FORCE_MAX = 50000.0;

DataDisplayWidget::DataDisplayWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_displayUnits(DisplayUnits::Metric)
    , m_updateInterval(DEFAULT_UPDATE_INTERVAL_MS)
    , m_dataLoggingEnabled(false)
    , m_dataQuality(DataQuality::Excellent)
    , m_actualUpdateRate(0.0)
    , m_totalSamples(0)
    , m_droppedSamples(0)
    , m_updateTimer(new QTimer(this))
    , m_statisticsTimer(new QTimer(this))
{
    setupUI();
    applyProfessionalStyling();
    
    // Setup timers
    m_updateTimer->setInterval(m_updateInterval);
    connect(m_updateTimer, &QTimer::timeout, this, &DataDisplayWidget::onUpdateTimer);
    
    m_statisticsTimer->setInterval(STATISTICS_UPDATE_INTERVAL_MS);
    connect(m_statisticsTimer, &QTimer::timeout, this, &DataDisplayWidget::updateStatistics);
    m_statisticsTimer->start();
    
    // Initialize alarm thresholds
    m_alarmThresholds["pressure_cpa"] = qMakePair(0.0, 250.0);
    m_alarmThresholds["pressure_cpb"] = qMakePair(0.0, 250.0);
    m_alarmThresholds["pressure_supply"] = qMakePair(150.0, 300.0);
    m_alarmThresholds["pressure_return"] = qMakePair(0.0, 10.0);
    m_alarmThresholds["flow_rate"] = qMakePair(-100.0, 100.0);
    m_alarmThresholds["temperature_fluid"] = qMakePair(10.0, 80.0);
    m_alarmThresholds["temperature_ambient"] = qMakePair(0.0, 50.0);
    m_alarmThresholds["actuator_position"] = qMakePair(0.0, 500.0);
    m_alarmThresholds["actuator_velocity"] = qMakePair(-200.0, 200.0);
    m_alarmThresholds["actuator_force"] = qMakePair(-50000.0, 50000.0);
    
    qCDebug(dataDisplayLog) << "DataDisplayWidget created";
}

DataDisplayWidget::~DataDisplayWidget()
{
    if (m_dataLoggingEnabled) {
        setDataLoggingEnabled(false);
    }
}

void DataDisplayWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // Create main display area with scroll
    QHBoxLayout* displayLayout = new QHBoxLayout();
    
    // Left column - Primary displays
    QVBoxLayout* leftColumn = new QVBoxLayout();
    leftColumn->addWidget(createPressureGroup());
    leftColumn->addWidget(createFlowGroup());
    leftColumn->addWidget(createTemperatureGroup());
    displayLayout->addLayout(leftColumn);
    
    // Right column - Actuator and system status
    QVBoxLayout* rightColumn = new QVBoxLayout();
    rightColumn->addWidget(createActuatorGroup());
    rightColumn->addWidget(createSystemStatusGroup());
    rightColumn->addWidget(createStatisticsGroup());
    displayLayout->addLayout(rightColumn);
    
    m_mainLayout->addLayout(displayLayout);
    
    // Control panel at bottom
    setupControlPanel();
    setupDataLogging();
}

QGroupBox* DataDisplayWidget::createPressureGroup()
{
    m_pressureGroup = new QGroupBox("Pressure Readings", this);
    QGridLayout* layout = new QGridLayout(m_pressureGroup);
    
    // CPA Pressure
    QWidget* cpaWidget = createValueDisplay("CPA", getPressureUnit(),
                                           m_pressureCPALCD, m_pressureCPABar, &m_pressureCPAStatus);
    layout->addWidget(cpaWidget, 0, 0);

    // CPB Pressure
    QWidget* cpbWidget = createValueDisplay("CPB", getPressureUnit(),
                                           m_pressureCPBLCD, m_pressureCPBBar, &m_pressureCPBStatus);
    layout->addWidget(cpbWidget, 0, 1);

    // Supply Pressure
    QWidget* supplyWidget = createValueDisplay("Supply", getPressureUnit(),
                                              m_pressureSupplyLCD, m_pressureSupplyBar, &m_pressureSupplyStatus);
    layout->addWidget(supplyWidget, 1, 0);

    // Return Pressure
    QWidget* returnWidget = createValueDisplay("Return", getPressureUnit(),
                                              m_pressureReturnLCD, m_pressureReturnBar, &m_pressureReturnStatus);
    layout->addWidget(returnWidget, 1, 1);
    
    return m_pressureGroup;
}

QGroupBox* DataDisplayWidget::createFlowGroup()
{
    m_flowGroup = new QGroupBox("Flow Measurement", this);
    QHBoxLayout* layout = new QHBoxLayout(m_flowGroup);
    
    // Flow rate display
    QWidget* flowWidget = createValueDisplay("Flow Rate", getFlowUnit(),
                                            m_flowRateLCD, m_flowRateBar, &m_flowStatusLabel);
    layout->addWidget(flowWidget);
    
    // Flow direction indicator
    QVBoxLayout* directionLayout = new QVBoxLayout();
    directionLayout->addWidget(new QLabel("Direction:"));
    
    m_flowDirectionDial = new QDial();
    m_flowDirectionDial->setRange(-100, 100);
    m_flowDirectionDial->setNotchesVisible(true);
    m_flowDirectionDial->setMaximumSize(80, 80);
    m_flowDirectionDial->setEnabled(false);
    directionLayout->addWidget(m_flowDirectionDial);
    
    m_flowDirectionLabel = new QLabel("Stopped");
    m_flowDirectionLabel->setAlignment(Qt::AlignCenter);
    m_flowDirectionLabel->setStyleSheet("font-weight: bold;");
    directionLayout->addWidget(m_flowDirectionLabel);
    
    layout->addLayout(directionLayout);
    
    return m_flowGroup;
}

QGroupBox* DataDisplayWidget::createTemperatureGroup()
{
    m_temperatureGroup = new QGroupBox("Temperature Monitoring", this);
    QHBoxLayout* layout = new QHBoxLayout(m_temperatureGroup);
    
    // Fluid temperature
    QWidget* fluidWidget = createValueDisplay("Fluid", getTemperatureUnit(),
                                             m_tempFluidLCD, m_tempFluidBar, &m_tempFluidStatus);
    layout->addWidget(fluidWidget);

    // Ambient temperature
    QWidget* ambientWidget = createValueDisplay("Ambient", getTemperatureUnit(),
                                               m_tempAmbientLCD, m_tempAmbientBar, &m_tempAmbientStatus);
    layout->addWidget(ambientWidget);
    
    // Temperature trend
    QVBoxLayout* trendLayout = new QVBoxLayout();
    trendLayout->addWidget(new QLabel("Trend:"));
    m_tempTrendLabel = new QLabel("Stable");
    m_tempTrendLabel->setAlignment(Qt::AlignCenter);
    m_tempTrendLabel->setStyleSheet("font-weight: bold; color: green;");
    trendLayout->addWidget(m_tempTrendLabel);
    layout->addLayout(trendLayout);
    
    return m_temperatureGroup;
}

QGroupBox* DataDisplayWidget::createActuatorGroup()
{
    m_actuatorGroup = new QGroupBox("Actuator Status", this);
    QVBoxLayout* layout = new QVBoxLayout(m_actuatorGroup);
    
    // Position display
    QWidget* positionWidget = createValueDisplay("Position", getPositionUnit(),
                                                 m_actuatorPositionLCD, m_actuatorPositionBar);
    layout->addWidget(positionWidget);

    // Position slider (visual indicator)
    m_actuatorPositionSlider = new QSlider(Qt::Horizontal);
    m_actuatorPositionSlider->setRange(static_cast<int>(POSITION_MIN), static_cast<int>(POSITION_MAX));
    m_actuatorPositionSlider->setEnabled(false);
    m_actuatorPositionSlider->setStyleSheet("QSlider::handle:horizontal { background: red; width: 20px; }");
    layout->addWidget(m_actuatorPositionSlider);

    // Velocity and force in horizontal layout
    QHBoxLayout* velForceLayout = new QHBoxLayout();

    QWidget* velocityWidget = createValueDisplay("Velocity", getVelocityUnit(),
                                                m_actuatorVelocityLCD, m_actuatorVelocityBar);
    velForceLayout->addWidget(velocityWidget);

    QWidget* forceWidget = createValueDisplay("Force", getForceUnit(),
                                             m_actuatorForceLCD, m_actuatorForceBar);
    velForceLayout->addWidget(forceWidget);
    
    layout->addLayout(velForceLayout);
    
    // Actuator status
    m_actuatorStatusLabel = new QLabel("Stopped");
    m_actuatorStatusLabel->setAlignment(Qt::AlignCenter);
    m_actuatorStatusLabel->setStyleSheet("font-weight: bold; padding: 5px; border: 1px solid gray; border-radius: 3px;");
    layout->addWidget(m_actuatorStatusLabel);
    
    return m_actuatorGroup;
}

QGroupBox* DataDisplayWidget::createSystemStatusGroup()
{
    m_systemStatusGroup = new QGroupBox("System Status", this);
    QVBoxLayout* layout = new QVBoxLayout(m_systemStatusGroup);
    
    // Overall system status
    m_systemStatusLabel = new QLabel("System Ready");
    m_systemStatusLabel->setAlignment(Qt::AlignCenter);
    m_systemStatusLabel->setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; "
                                      "background-color: lightgreen; border-radius: 5px;");
    layout->addWidget(m_systemStatusLabel);
    
    // Data quality indicator
    QHBoxLayout* qualityLayout = new QHBoxLayout();
    qualityLayout->addWidget(new QLabel("Data Quality:"));
    m_dataQualityLabel = new QLabel("Excellent");
    m_dataQualityLabel->setStyleSheet("font-weight: bold; color: green;");
    qualityLayout->addWidget(m_dataQualityLabel);
    m_dataQualityBar = new QProgressBar();
    m_dataQualityBar->setRange(0, 100);
    m_dataQualityBar->setValue(100);
    qualityLayout->addWidget(m_dataQualityBar);
    layout->addLayout(qualityLayout);
    
    // Last update time
    QHBoxLayout* updateLayout = new QHBoxLayout();
    updateLayout->addWidget(new QLabel("Last Update:"));
    m_lastUpdateLabel = new QLabel("Never");
    m_lastUpdateLabel->setStyleSheet("font-family: monospace;");
    updateLayout->addWidget(m_lastUpdateLabel);
    layout->addLayout(updateLayout);
    
    return m_systemStatusGroup;
}

QGroupBox* DataDisplayWidget::createStatisticsGroup()
{
    m_statisticsGroup = new QGroupBox("Data Acquisition Statistics", this);
    QGridLayout* layout = new QGridLayout(m_statisticsGroup);
    
    // Update rate
    layout->addWidget(new QLabel("Update Rate:"), 0, 0);
    m_updateRateLabel = new QLabel("0.0 Hz");
    m_updateRateLabel->setStyleSheet("font-family: monospace; font-weight: bold;");
    layout->addWidget(m_updateRateLabel, 0, 1);
    
    // Total samples
    layout->addWidget(new QLabel("Total Samples:"), 1, 0);
    m_totalSamplesLabel = new QLabel("0");
    m_totalSamplesLabel->setStyleSheet("font-family: monospace; font-weight: bold;");
    layout->addWidget(m_totalSamplesLabel, 1, 1);
    
    // Dropped samples
    layout->addWidget(new QLabel("Dropped Samples:"), 2, 0);
    m_droppedSamplesLabel = new QLabel("0");
    m_droppedSamplesLabel->setStyleSheet("font-family: monospace; font-weight: bold; color: red;");
    layout->addWidget(m_droppedSamplesLabel, 2, 1);
    
    return m_statisticsGroup;
}

QWidget* DataDisplayWidget::createValueDisplay(const QString& label, const QString& units,
                                              QLCDNumber*& lcdDisplay, QProgressBar*& progressBar,
                                              QLabel** statusLabel)
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(5, 5, 5, 5);

    // Label
    QLabel* titleLabel = new QLabel(QString("%1 (%2)").arg(label, units));
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setStyleSheet("font-weight: bold; color: navy;");
    layout->addWidget(titleLabel);

    // LCD Display
    lcdDisplay = new QLCDNumber(6);
    lcdDisplay->setSegmentStyle(QLCDNumber::Flat);
    lcdDisplay->setStyleSheet("QLCDNumber { background-color: black; color: lime; border: 2px solid gray; }");
    lcdDisplay->display(0.0);
    layout->addWidget(lcdDisplay);

    // Progress Bar
    progressBar = new QProgressBar();
    progressBar->setRange(0, 1000);
    progressBar->setValue(0);
    progressBar->setTextVisible(false);
    progressBar->setMaximumHeight(15);
    layout->addWidget(progressBar);

    // Status Label (optional)
    if (statusLabel) {
        *statusLabel = new QLabel("OK");
        (*statusLabel)->setAlignment(Qt::AlignCenter);
        (*statusLabel)->setStyleSheet("font-size: 10px; font-weight: bold; color: green; "
                                     "padding: 2px; border: 1px solid green; border-radius: 3px;");
        layout->addWidget(*statusLabel);
    }

    return widget;
}

void DataDisplayWidget::setupControlPanel()
{
    QGroupBox* controlGroup = new QGroupBox("Display Controls", this);
    QHBoxLayout* layout = new QHBoxLayout(controlGroup);

    // Units selection
    layout->addWidget(new QLabel("Units:"));
    m_unitsComboBox = new QComboBox();
    m_unitsComboBox->addItems({"Metric", "Imperial", "Mixed"});
    connect(m_unitsComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &DataDisplayWidget::onUnitsChanged);
    layout->addWidget(m_unitsComboBox);

    layout->addWidget(new QLabel("Update Rate:"));
    m_updateIntervalSpinBox = new QSpinBox();
    m_updateIntervalSpinBox->setRange(10, 5000);
    m_updateIntervalSpinBox->setSuffix(" ms");
    m_updateIntervalSpinBox->setValue(m_updateInterval);
    connect(m_updateIntervalSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &DataDisplayWidget::setUpdateInterval);
    layout->addWidget(m_updateIntervalSpinBox);

    // Data logging
    m_loggingCheckBox = new QCheckBox("Enable Logging");
    connect(m_loggingCheckBox, &QCheckBox::toggled, this, &DataDisplayWidget::onLoggingToggled);
    layout->addWidget(m_loggingCheckBox);

    // Export button
    m_exportButton = new QPushButton("Export Data");
    connect(m_exportButton, &QPushButton::clicked, this, &DataDisplayWidget::onExportClicked);
    layout->addWidget(m_exportButton);

    // Reset statistics
    m_resetStatsButton = new QPushButton("Reset Stats");
    connect(m_resetStatsButton, &QPushButton::clicked, this, &DataDisplayWidget::onResetStatsClicked);
    layout->addWidget(m_resetStatsButton);

    // Configure alarms
    m_configureAlarmsButton = new QPushButton("Configure Alarms");
    connect(m_configureAlarmsButton, &QPushButton::clicked, this, &DataDisplayWidget::onConfigureAlarmsClicked);
    layout->addWidget(m_configureAlarmsButton);

    m_mainLayout->addWidget(controlGroup);
}

void DataDisplayWidget::setupDataLogging()
{
    QGroupBox* loggingGroup = new QGroupBox("Data Log", this);
    loggingGroup->setMaximumHeight(150);
    QVBoxLayout* layout = new QVBoxLayout(loggingGroup);

    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setMaximumHeight(100);
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->setFont(QFont("Courier", 8));
    layout->addWidget(m_logTextEdit);

    QHBoxLayout* logButtonLayout = new QHBoxLayout();
    m_clearLogButton = new QPushButton("Clear Log");
    connect(m_clearLogButton, &QPushButton::clicked, m_logTextEdit, &QTextEdit::clear);
    logButtonLayout->addWidget(m_clearLogButton);

    m_saveLogButton = new QPushButton("Save Log");
    connect(m_saveLogButton, &QPushButton::clicked, this, [this]() {
        QString filename = QFileDialog::getSaveFileName(this, "Save Log",
                                                       QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
                                                       "Text Files (*.txt)");
        if (!filename.isEmpty()) {
            QFile file(filename);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << m_logTextEdit->toPlainText();
                QMessageBox::information(this, "Success", "Log saved successfully");
            }
        }
    });
    logButtonLayout->addWidget(m_saveLogButton);
    logButtonLayout->addStretch();

    layout->addLayout(logButtonLayout);
    m_mainLayout->addWidget(loggingGroup);
}

// Public slots
void DataDisplayWidget::updateData(const TestData& data)
{
    QMutexLocker locker(&m_dataMutex);

    if (!validateData(data)) {
        m_droppedSamples++;
        return;
    }

    m_currentData = data;
    m_totalSamples++;
    m_lastUpdateTime = QDateTime::currentDateTime();

    // Add to history
    m_dataHistory.append(data);
    if (m_dataHistory.size() > MAX_HISTORY_SIZE) {
        m_dataHistory.removeFirst();
    }

    // Update displays (must be called from main thread)
    QMetaObject::invokeMethod(this, [this, data]() {
        updatePressureDisplays(data);
        updateFlowDisplays(data);
        updateTemperatureDisplays(data);
        updateActuatorDisplays(data);
        updateSystemStatus(data);
        checkAlarms(data);

        // Update last update time display
        m_lastUpdateLabel->setText(m_lastUpdateTime.toString("hh:mm:ss.zzz"));

        // Log data if enabled
        if (m_dataLoggingEnabled) {
            QString logEntry = QString("%1: P_CPA=%.1f, P_CPB=%.1f, Flow=%.1f, Temp=%.1f")
                              .arg(m_lastUpdateTime.toString("hh:mm:ss.zzz"))
                              .arg(convertPressure(data.pressureCPA()))
                              .arg(convertPressure(data.pressureCPB()))
                              .arg(convertFlow(data.flowRate()))
                              .arg(convertTemperature(data.temperatureFluid()));
            m_logTextEdit->append(logEntry);

            // Limit log size
            if (m_logTextEdit->document()->blockCount() > 1000) {
                QTextCursor cursor = m_logTextEdit->textCursor();
                cursor.movePosition(QTextCursor::Start);
                cursor.movePosition(QTextCursor::Down, QTextCursor::KeepAnchor, 100);
                cursor.removeSelectedText();
            }
        }

        emit dataUpdated(data);
    }, Qt::QueuedConnection);

    // Update rate calculation
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    m_updateTimes.append(currentTime);
    if (m_updateTimes.size() > RATE_CALCULATION_SAMPLES) {
        m_updateTimes.removeFirst();
    }
}

void DataDisplayWidget::clearData()
{
    QMutexLocker locker(&m_dataMutex);
    m_dataHistory.clear();
    m_currentData = TestData();

    // Clear displays
    QMetaObject::invokeMethod(this, [this]() {
        m_pressureCPALCD->display(0.0);
        m_pressureCPBLCD->display(0.0);
        m_pressureSupplyLCD->display(0.0);
        m_pressureReturnLCD->display(0.0);
        m_flowRateLCD->display(0.0);
        m_tempFluidLCD->display(0.0);
        m_tempAmbientLCD->display(0.0);
        m_actuatorPositionLCD->display(0.0);
        m_actuatorVelocityLCD->display(0.0);
        m_actuatorForceLCD->display(0.0);

        // Reset progress bars
        m_pressureCPABar->setValue(0);
        m_pressureCPBBar->setValue(0);
        m_pressureSupplyBar->setValue(0);
        m_pressureReturnBar->setValue(0);
        m_flowRateBar->setValue(500); // Center for bidirectional
        m_tempFluidBar->setValue(0);
        m_tempAmbientBar->setValue(0);
        m_actuatorPositionBar->setValue(0);
        m_actuatorVelocityBar->setValue(500); // Center for bidirectional
        m_actuatorForceBar->setValue(500); // Center for bidirectional

        m_lastUpdateLabel->setText("Never");
    }, Qt::QueuedConnection);
}

void DataDisplayWidget::resetStatistics()
{
    QMutexLocker locker(&m_dataMutex);
    m_totalSamples = 0;
    m_droppedSamples = 0;
    m_actualUpdateRate = 0.0;
    m_updateTimes.clear();

    QMetaObject::invokeMethod(this, [this]() {
        m_updateRateLabel->setText("0.0 Hz");
        m_totalSamplesLabel->setText("0");
        m_droppedSamplesLabel->setText("0");
    }, Qt::QueuedConnection);
}

// Configuration methods
void DataDisplayWidget::setDisplayUnits(DisplayUnits units)
{
    if (m_displayUnits != units) {
        m_displayUnits = units;
        m_unitsComboBox->setCurrentIndex(static_cast<int>(units));

        // Update all display labels with new units
        updateDisplayColors();
        emit configurationChanged();
    }
}

void DataDisplayWidget::setUpdateInterval(int intervalMs)
{
    if (m_updateInterval != intervalMs) {
        m_updateInterval = intervalMs;
        m_updateTimer->setInterval(intervalMs);
        m_updateIntervalSpinBox->setValue(intervalMs);
        emit configurationChanged();
    }
}

void DataDisplayWidget::setDataLoggingEnabled(bool enabled)
{
    if (m_dataLoggingEnabled != enabled) {
        m_dataLoggingEnabled = enabled;
        m_loggingCheckBox->setChecked(enabled);

        if (enabled) {
            m_logTextEdit->append(QString("[%1] Data logging started")
                                 .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));
        } else {
            m_logTextEdit->append(QString("[%1] Data logging stopped")
                                 .arg(QDateTime::currentDateTime().toString("hh:mm:ss")));
        }

        emit configurationChanged();
    }
}

void DataDisplayWidget::setAlarmThresholds(const QMap<QString, QPair<double, double>>& thresholds)
{
    m_alarmThresholds = thresholds;
    emit configurationChanged();
}

// Data access methods
TestData DataDisplayWidget::currentData() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_currentData;
}

QList<TestData> DataDisplayWidget::dataHistory(int maxSamples) const
{
    QMutexLocker locker(&m_dataMutex);
    if (maxSamples <= 0 || maxSamples >= m_dataHistory.size()) {
        return m_dataHistory;
    }
    return m_dataHistory.mid(m_dataHistory.size() - maxSamples);
}

// Update display methods
void DataDisplayWidget::updatePressureDisplays(const TestData& data)
{
    // CPA Pressure
    double cpaPressure = convertPressure(data.pressureCPA());
    m_pressureCPALCD->display(cpaPressure);
    int cpaPercent = static_cast<int>((cpaPressure / convertPressure(PRESSURE_MAX)) * 1000);
    m_pressureCPABar->setValue(qBound(0, cpaPercent, 1000));

    // CPB Pressure
    double cpbPressure = convertPressure(data.pressureCPB());
    m_pressureCPBLCD->display(cpbPressure);
    int cpbPercent = static_cast<int>((cpbPressure / convertPressure(PRESSURE_MAX)) * 1000);
    m_pressureCPBBar->setValue(qBound(0, cpbPercent, 1000));

    // Supply Pressure
    double supplyPressure = convertPressure(data.pressureSupply());
    m_pressureSupplyLCD->display(supplyPressure);
    int supplyPercent = static_cast<int>((supplyPressure / convertPressure(PRESSURE_MAX)) * 1000);
    m_pressureSupplyBar->setValue(qBound(0, supplyPercent, 1000));

    // Return Pressure
    double returnPressure = convertPressure(data.pressureReturn());
    m_pressureReturnLCD->display(returnPressure);
    int returnPercent = static_cast<int>((returnPressure / convertPressure(PRESSURE_MAX)) * 1000);
    m_pressureReturnBar->setValue(qBound(0, returnPercent, 1000));

    // Update status indicators
    if (m_pressureCPAStatus) {
        auto thresholds = m_alarmThresholds.value("pressure_cpa", qMakePair(0.0, 250.0));
        bool alarm = cpaPressure < convertPressure(thresholds.first) || cpaPressure > convertPressure(thresholds.second);
        setAlarmStatus(m_pressureCPAStatus, alarm, alarm ? "OUT OF RANGE" : "OK");
    }

    if (m_pressureCPBStatus) {
        auto thresholds = m_alarmThresholds.value("pressure_cpb", qMakePair(0.0, 250.0));
        bool alarm = cpbPressure < convertPressure(thresholds.first) || cpbPressure > convertPressure(thresholds.second);
        setAlarmStatus(m_pressureCPBStatus, alarm, alarm ? "OUT OF RANGE" : "OK");
    }

    if (m_pressureSupplyStatus) {
        auto thresholds = m_alarmThresholds.value("pressure_supply", qMakePair(150.0, 300.0));
        bool alarm = supplyPressure < convertPressure(thresholds.first) || supplyPressure > convertPressure(thresholds.second);
        setAlarmStatus(m_pressureSupplyStatus, alarm, alarm ? "LOW/HIGH" : "OK");
    }

    if (m_pressureReturnStatus) {
        auto thresholds = m_alarmThresholds.value("pressure_return", qMakePair(0.0, 10.0));
        bool alarm = returnPressure < convertPressure(thresholds.first) || returnPressure > convertPressure(thresholds.second);
        setAlarmStatus(m_pressureReturnStatus, alarm, alarm ? "HIGH" : "OK");
    }
}

void DataDisplayWidget::updateFlowDisplays(const TestData& data)
{
    double flowRate = convertFlow(data.flowRate());
    m_flowRateLCD->display(qAbs(flowRate));

    // Flow rate bar (centered for bidirectional)
    double flowRange = convertFlow(FLOW_MAX) - convertFlow(FLOW_MIN);
    int flowPercent = static_cast<int>(((flowRate - convertFlow(FLOW_MIN)) / flowRange) * 1000);
    m_flowRateBar->setValue(qBound(0, flowPercent, 1000));

    // Flow direction
    m_flowDirectionDial->setValue(static_cast<int>(flowRate));

    if (qAbs(flowRate) < 1.0) {
        m_flowDirectionLabel->setText("Stopped");
        m_flowDirectionLabel->setStyleSheet("font-weight: bold; color: gray;");
    } else if (flowRate > 0) {
        m_flowDirectionLabel->setText("Forward");
        m_flowDirectionLabel->setStyleSheet("font-weight: bold; color: green;");
    } else {
        m_flowDirectionLabel->setText("Reverse");
        m_flowDirectionLabel->setStyleSheet("font-weight: bold; color: orange;");
    }

    // Flow status
    auto thresholds = m_alarmThresholds.value("flow_rate", qMakePair(-100.0, 100.0));
    bool alarm = flowRate < convertFlow(thresholds.first) || flowRate > convertFlow(thresholds.second);
    setAlarmStatus(m_flowStatusLabel, alarm, alarm ? "OUT OF RANGE" : "OK");
}

void DataDisplayWidget::updateTemperatureDisplays(const TestData& data)
{
    // Fluid temperature
    double fluidTemp = convertTemperature(data.temperatureFluid());
    m_tempFluidLCD->display(fluidTemp);
    double tempRange = convertTemperature(TEMPERATURE_MAX) - convertTemperature(TEMPERATURE_MIN);
    int fluidPercent = static_cast<int>(((fluidTemp - convertTemperature(TEMPERATURE_MIN)) / tempRange) * 1000);
    m_tempFluidBar->setValue(qBound(0, fluidPercent, 1000));

    // Ambient temperature
    double ambientTemp = convertTemperature(data.temperatureAmbient());
    m_tempAmbientLCD->display(ambientTemp);
    int ambientPercent = static_cast<int>(((ambientTemp - convertTemperature(TEMPERATURE_MIN)) / tempRange) * 1000);
    m_tempAmbientBar->setValue(qBound(0, ambientPercent, 1000));

    // Temperature trend (simplified)
    static double lastFluidTemp = fluidTemp;
    double tempDiff = fluidTemp - lastFluidTemp;
    lastFluidTemp = fluidTemp;

    if (qAbs(tempDiff) < 0.1) {
        m_tempTrendLabel->setText("Stable");
        m_tempTrendLabel->setStyleSheet("font-weight: bold; color: green;");
    } else if (tempDiff > 0) {
        m_tempTrendLabel->setText("Rising");
        m_tempTrendLabel->setStyleSheet("font-weight: bold; color: orange;");
    } else {
        m_tempTrendLabel->setText("Falling");
        m_tempTrendLabel->setStyleSheet("font-weight: bold; color: blue;");
    }

    // Temperature status
    auto fluidThresholds = m_alarmThresholds.value("temperature_fluid", qMakePair(10.0, 80.0));
    bool fluidAlarm = fluidTemp < convertTemperature(fluidThresholds.first) || fluidTemp > convertTemperature(fluidThresholds.second);
    setAlarmStatus(m_tempFluidStatus, fluidAlarm, fluidAlarm ? "OUT OF RANGE" : "OK");

    auto ambientThresholds = m_alarmThresholds.value("temperature_ambient", qMakePair(0.0, 50.0));
    bool ambientAlarm = ambientTemp < convertTemperature(ambientThresholds.first) || ambientTemp > convertTemperature(ambientThresholds.second);
    setAlarmStatus(m_tempAmbientStatus, ambientAlarm, ambientAlarm ? "OUT OF RANGE" : "OK");
}

void DataDisplayWidget::updateActuatorDisplays(const TestData& data)
{
    // Position
    double position = convertPosition(data.actuatorPosition());
    m_actuatorPositionLCD->display(position);
    int positionPercent = static_cast<int>((position / convertPosition(POSITION_MAX)) * 1000);
    m_actuatorPositionBar->setValue(qBound(0, positionPercent, 1000));
    m_actuatorPositionSlider->setValue(static_cast<int>(position));

    // Velocity
    double velocity = convertVelocity(data.actuatorVelocity());
    m_actuatorVelocityLCD->display(qAbs(velocity));
    double velocityRange = convertVelocity(VELOCITY_MAX) - convertVelocity(VELOCITY_MIN);
    int velocityPercent = static_cast<int>(((velocity - convertVelocity(VELOCITY_MIN)) / velocityRange) * 1000);
    m_actuatorVelocityBar->setValue(qBound(0, velocityPercent, 1000));

    // Force
    double force = convertForce(data.actuatorForce());
    m_actuatorForceLCD->display(qAbs(force));
    double forceRange = convertForce(FORCE_MAX) - convertForce(FORCE_MIN);
    int forcePercent = static_cast<int>(((force - convertForce(FORCE_MIN)) / forceRange) * 1000);
    m_actuatorForceBar->setValue(qBound(0, forcePercent, 1000));

    // Actuator status
    if (qAbs(velocity) < 1.0) {
        m_actuatorStatusLabel->setText("Stopped");
        m_actuatorStatusLabel->setStyleSheet("font-weight: bold; color: gray; padding: 5px; border: 1px solid gray; border-radius: 3px;");
    } else if (velocity > 0) {
        m_actuatorStatusLabel->setText("Extending");
        m_actuatorStatusLabel->setStyleSheet("font-weight: bold; color: green; padding: 5px; border: 1px solid green; border-radius: 3px;");
    } else {
        m_actuatorStatusLabel->setText("Retracting");
        m_actuatorStatusLabel->setStyleSheet("font-weight: bold; color: orange; padding: 5px; border: 1px solid orange; border-radius: 3px;");
    }
}

void DataDisplayWidget::updateSystemStatus(const TestData& data)
{
    // Determine overall system status based on data quality and alarms
    bool hasAlarms = false;
    QStringList alarmMessages;

    // Check all alarm conditions
    for (auto it = m_alarmThresholds.begin(); it != m_alarmThresholds.end(); ++it) {
        QString param = it.key();
        auto thresholds = it.value();
        double value = 0.0;

        if (param == "pressure_cpa") value = data.pressureCPA();
        else if (param == "pressure_cpb") value = data.pressureCPB();
        else if (param == "pressure_supply") value = data.pressureSupply();
        else if (param == "pressure_return") value = data.pressureReturn();
        else if (param == "flow_rate") value = data.flowRate();
        else if (param == "temperature_fluid") value = data.temperatureFluid();
        else if (param == "temperature_ambient") value = data.temperatureAmbient();
        else if (param == "actuator_position") value = data.actuatorPosition();
        else if (param == "actuator_velocity") value = data.actuatorVelocity();
        else if (param == "actuator_force") value = data.actuatorForce();

        if (value < thresholds.first || value > thresholds.second) {
            hasAlarms = true;
            alarmMessages.append(QString("%1: %.2f").arg(param).arg(value));
        }
    }

    // Update system status display
    if (hasAlarms) {
        m_systemStatusLabel->setText("ALARM CONDITION");
        m_systemStatusLabel->setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; "
                                          "background-color: red; color: white; border-radius: 5px;");
    } else if (m_dataQuality == DataQuality::Poor) {
        m_systemStatusLabel->setText("DATA QUALITY POOR");
        m_systemStatusLabel->setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; "
                                          "background-color: orange; color: white; border-radius: 5px;");
    } else {
        m_systemStatusLabel->setText("SYSTEM NORMAL");
        m_systemStatusLabel->setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; "
                                          "background-color: lightgreen; color: black; border-radius: 5px;");
    }
}

void DataDisplayWidget::updateStatistics()
{
    // Calculate actual update rate
    if (m_updateTimes.size() >= 2) {
        qint64 timeSpan = m_updateTimes.last() - m_updateTimes.first();
        if (timeSpan > 0) {
            m_actualUpdateRate = (m_updateTimes.size() - 1) * 1000.0 / timeSpan;
        }
    }

    // Update data quality
    updateDataQuality();

    // Update display labels
    m_updateRateLabel->setText(QString("%1 Hz").arg(m_actualUpdateRate, 0, 'f', 1));
    m_totalSamplesLabel->setText(QString::number(m_totalSamples));
    m_droppedSamplesLabel->setText(QString::number(m_droppedSamples));

    // Update data quality display
    QString qualityText;
    QString qualityColor;
    int qualityPercent = 100;

    switch (m_dataQuality) {
        case DataQuality::Excellent:
            qualityText = "Excellent";
            qualityColor = "green";
            qualityPercent = 100;
            break;
        case DataQuality::Good:
            qualityText = "Good";
            qualityColor = "lightgreen";
            qualityPercent = 80;
            break;
        case DataQuality::Fair:
            qualityText = "Fair";
            qualityColor = "orange";
            qualityPercent = 60;
            break;
        case DataQuality::Poor:
            qualityText = "Poor";
            qualityColor = "red";
            qualityPercent = 40;
            break;
    }

    m_dataQualityLabel->setText(qualityText);
    m_dataQualityLabel->setStyleSheet(QString("font-weight: bold; color: %1;").arg(qualityColor));
    m_dataQualityBar->setValue(qualityPercent);
}

// Unit conversion methods
double DataDisplayWidget::convertPressure(double pressure) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return pressure * 14.5038; // bar to PSI
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return pressure; // bar
    }
}

double DataDisplayWidget::convertFlow(double flow) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return flow * 0.264172; // L/min to GPM
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return flow; // L/min
    }
}

double DataDisplayWidget::convertTemperature(double temperature) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return temperature * 9.0/5.0 + 32.0; // °C to °F
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return temperature; // °C
    }
}

double DataDisplayWidget::convertPosition(double position) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return position * 0.0393701; // mm to inch
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return position; // mm
    }
}

double DataDisplayWidget::convertVelocity(double velocity) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return velocity * 0.0393701; // mm/s to inch/s
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return velocity; // mm/s
    }
}

double DataDisplayWidget::convertForce(double force) const
{
    switch (m_displayUnits) {
        case DisplayUnits::Imperial:
            return force * 0.224809; // N to lbf
        case DisplayUnits::Metric:
        case DisplayUnits::Mixed:
        default:
            return force; // N
    }
}

// Unit string methods
QString DataDisplayWidget::getPressureUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "PSI" : "bar";
}

QString DataDisplayWidget::getFlowUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "GPM" : "L/min";
}

QString DataDisplayWidget::getTemperatureUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "°F" : "°C";
}

QString DataDisplayWidget::getPositionUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "inch" : "mm";
}

QString DataDisplayWidget::getVelocityUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "inch/s" : "mm/s";
}

QString DataDisplayWidget::getForceUnit() const
{
    return (m_displayUnits == DisplayUnits::Imperial) ? "lbf" : "N";
}

// Alarm and validation methods
void DataDisplayWidget::checkAlarms(const TestData& data)
{
    for (auto it = m_alarmThresholds.begin(); it != m_alarmThresholds.end(); ++it) {
        QString param = it.key();
        auto thresholds = it.value();
        double value = 0.0;

        if (param == "pressure_cpa") value = data.pressureCPA();
        else if (param == "pressure_cpb") value = data.pressureCPB();
        else if (param == "pressure_supply") value = data.pressureSupply();
        else if (param == "pressure_return") value = data.pressureReturn();
        else if (param == "flow_rate") value = data.flowRate();
        else if (param == "temperature_fluid") value = data.temperatureFluid();
        else if (param == "temperature_ambient") value = data.temperatureAmbient();
        else if (param == "actuator_position") value = data.actuatorPosition();
        else if (param == "actuator_velocity") value = data.actuatorVelocity();
        else if (param == "actuator_force") value = data.actuatorForce();

        if (value < thresholds.first) {
            emit alarmTriggered(param, value, QString("Below minimum threshold: %1").arg(thresholds.first));
        } else if (value > thresholds.second) {
            emit alarmTriggered(param, value, QString("Above maximum threshold: %1").arg(thresholds.second));
        }
    }
}

void DataDisplayWidget::setAlarmStatus(QLabel* statusLabel, bool alarm, const QString& message)
{
    if (!statusLabel) return;

    statusLabel->setText(message);
    if (alarm) {
        statusLabel->setStyleSheet("font-size: 10px; font-weight: bold; color: red; "
                                  "padding: 2px; border: 1px solid red; border-radius: 3px;");
    } else {
        statusLabel->setStyleSheet("font-size: 10px; font-weight: bold; color: green; "
                                  "padding: 2px; border: 1px solid green; border-radius: 3px;");
    }
}

bool DataDisplayWidget::validateData(const TestData& data) const
{
    // Basic validation - check for NaN or infinite values
    if (!data.isValid()) {
        return false;
    }

    // Check timestamp
    if (!data.timestamp().isValid()) {
        return false;
    }

    // Check for reasonable value ranges
    if (qIsNaN(data.pressureCPA()) || qIsInf(data.pressureCPA()) ||
        qIsNaN(data.pressureCPB()) || qIsInf(data.pressureCPB()) ||
        qIsNaN(data.flowRate()) || qIsInf(data.flowRate()) ||
        qIsNaN(data.temperatureFluid()) || qIsInf(data.temperatureFluid())) {
        return false;
    }

    return true;
}

void DataDisplayWidget::updateDataQuality()
{
    if (m_totalSamples == 0) {
        m_dataQuality = DataQuality::Excellent;
        return;
    }

    double dropRate = static_cast<double>(m_droppedSamples) / m_totalSamples;

    if (dropRate < 0.01) {
        m_dataQuality = DataQuality::Excellent;
    } else if (dropRate < 0.05) {
        m_dataQuality = DataQuality::Good;
    } else if (dropRate < 0.10) {
        m_dataQuality = DataQuality::Fair;
    } else {
        m_dataQuality = DataQuality::Poor;
    }
}

// Slot implementations
void DataDisplayWidget::onUpdateTimer()
{
    // This timer is for any periodic updates that don't depend on data
    // Currently not used as updates are driven by data arrival
}

void DataDisplayWidget::onUnitsChanged()
{
    DisplayUnits newUnits = static_cast<DisplayUnits>(m_unitsComboBox->currentIndex());
    setDisplayUnits(newUnits);
}

void DataDisplayWidget::onThresholdChanged()
{
    // This would be connected to threshold configuration widgets
    emit configurationChanged();
}

void DataDisplayWidget::onLoggingToggled(bool enabled)
{
    setDataLoggingEnabled(enabled);
}

void DataDisplayWidget::onExportClicked()
{
    exportData();
}

void DataDisplayWidget::onResetStatsClicked()
{
    resetStatistics();
}

void DataDisplayWidget::onConfigureAlarmsClicked()
{
    // This would open an alarm configuration dialog
    // For now, just show current thresholds
    QStringList thresholdInfo;
    for (auto it = m_alarmThresholds.begin(); it != m_alarmThresholds.end(); ++it) {
        thresholdInfo.append(QString("%1: [%2, %3]")
                            .arg(it.key())
                            .arg(it.value().first)
                            .arg(it.value().second));
    }

    QMessageBox::information(this, "Current Alarm Thresholds",
                           thresholdInfo.join("\n"));
}

void DataDisplayWidget::exportData()
{
    QString filename = QFileDialog::getSaveFileName(this, "Export Data",
                                                   QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
                                                   "CSV Files (*.csv);;JSON Files (*.json)");

    if (filename.isEmpty()) {
        return;
    }

    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Export Error",
                            QString("Failed to open file for writing: %1").arg(filename));
        return;
    }

    QTextStream out(&file);

    if (filename.endsWith(".csv")) {
        // Export as CSV
        out << "Timestamp,Pressure_CPA,Pressure_CPB,Pressure_Supply,Pressure_Return,"
            << "Flow_Rate,Temperature_Fluid,Temperature_Ambient,"
            << "Actuator_Position,Actuator_Velocity,Actuator_Force\n";

        QMutexLocker locker(&m_dataMutex);
        for (const TestData& data : m_dataHistory) {
            out << data.timestamp().toString(Qt::ISODate) << ","
                << convertPressure(data.pressureCPA()) << ","
                << convertPressure(data.pressureCPB()) << ","
                << convertPressure(data.pressureSupply()) << ","
                << convertPressure(data.pressureReturn()) << ","
                << convertFlow(data.flowRate()) << ","
                << convertTemperature(data.temperatureFluid()) << ","
                << convertTemperature(data.temperatureAmbient()) << ","
                << convertPosition(data.actuatorPosition()) << ","
                << convertVelocity(data.actuatorVelocity()) << ","
                << convertForce(data.actuatorForce()) << "\n";
        }
    } else if (filename.endsWith(".json")) {
        // Export as JSON
        QJsonArray dataArray;
        QMutexLocker locker(&m_dataMutex);
        for (const TestData& data : m_dataHistory) {
            QJsonObject dataObj = data.toJson();
            // Add converted values
            dataObj["pressure_cpa_converted"] = convertPressure(data.pressureCPA());
            dataObj["pressure_cpb_converted"] = convertPressure(data.pressureCPB());
            dataObj["flow_rate_converted"] = convertFlow(data.flowRate());
            dataObj["temperature_fluid_converted"] = convertTemperature(data.temperatureFluid());
            dataObj["units"] = static_cast<int>(m_displayUnits);
            dataArray.append(dataObj);
        }

        QJsonObject exportObj;
        exportObj["export_timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        exportObj["display_units"] = static_cast<int>(m_displayUnits);
        exportObj["total_samples"] = m_totalSamples;
        exportObj["dropped_samples"] = m_droppedSamples;
        exportObj["data_quality"] = static_cast<int>(m_dataQuality);
        exportObj["data"] = dataArray;

        QJsonDocument doc(exportObj);
        out << doc.toJson();
    }

    QMessageBox::information(this, "Export Complete",
                           QString("Data exported successfully to: %1").arg(filename));
    emit exportCompleted(filename);
}

void DataDisplayWidget::saveConfiguration()
{
    QSettings settings;
    settings.beginGroup("DataDisplayWidget");

    settings.setValue("displayUnits", static_cast<int>(m_displayUnits));
    settings.setValue("updateInterval", m_updateInterval);
    settings.setValue("dataLoggingEnabled", m_dataLoggingEnabled);

    // Save alarm thresholds
    settings.beginGroup("AlarmThresholds");
    for (auto it = m_alarmThresholds.begin(); it != m_alarmThresholds.end(); ++it) {
        settings.setValue(it.key() + "_min", it.value().first);
        settings.setValue(it.key() + "_max", it.value().second);
    }
    settings.endGroup();

    settings.endGroup();

    qCDebug(dataDisplayLog) << "Configuration saved";
}

void DataDisplayWidget::loadConfiguration()
{
    QSettings settings;
    settings.beginGroup("DataDisplayWidget");

    setDisplayUnits(static_cast<DisplayUnits>(settings.value("displayUnits", 0).toInt()));
    setUpdateInterval(settings.value("updateInterval", DEFAULT_UPDATE_INTERVAL_MS).toInt());
    setDataLoggingEnabled(settings.value("dataLoggingEnabled", false).toBool());

    // Load alarm thresholds
    settings.beginGroup("AlarmThresholds");
    QStringList keys = settings.childKeys();
    QMap<QString, QPair<double, double>> thresholds;

    QStringList paramNames = {"pressure_cpa", "pressure_cpb", "pressure_supply", "pressure_return",
                             "flow_rate", "temperature_fluid", "temperature_ambient",
                             "actuator_position", "actuator_velocity", "actuator_force"};

    for (const QString& param : paramNames) {
        QString minKey = param + "_min";
        QString maxKey = param + "_max";
        if (settings.contains(minKey) && settings.contains(maxKey)) {
            double minVal = settings.value(minKey).toDouble();
            double maxVal = settings.value(maxKey).toDouble();
            thresholds[param] = qMakePair(minVal, maxVal);
        }
    }

    if (!thresholds.isEmpty()) {
        setAlarmThresholds(thresholds);
    }

    settings.endGroup();
    settings.endGroup();

    qCDebug(dataDisplayLog) << "Configuration loaded";
}

void DataDisplayWidget::applyProfessionalStyling()
{
    // Apply professional styling to the widget
    setStyleSheet(
        "QGroupBox {"
        "    font-weight: bold;"
        "    border: 2px solid gray;"
        "    border-radius: 5px;"
        "    margin-top: 1ex;"
        "    padding-top: 10px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 10px;"
        "    padding: 0 5px 0 5px;"
        "}"
        "QLCDNumber {"
        "    background-color: black;"
        "    color: lime;"
        "    border: 2px solid gray;"
        "    border-radius: 3px;"
        "}"
        "QProgressBar {"
        "    border: 1px solid gray;"
        "    border-radius: 3px;"
        "    text-align: center;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,"
        "                                      stop:0 #05B8CC, stop:1 #80E0FF);"
        "    border-radius: 2px;"
        "}"
        "QPushButton {"
        "    border: 1px solid gray;"
        "    border-radius: 3px;"
        "    padding: 5px;"
        "    background-color: lightgray;"
        "}"
        "QPushButton:hover {"
        "    background-color: lightblue;"
        "}"
        "QPushButton:pressed {"
        "    background-color: gray;"
        "}"
    );
}

void DataDisplayWidget::updateDisplayColors()
{
    // Update display colors based on current data and thresholds
    // This could be enhanced to provide more sophisticated color coding

    // Update progress bar colors based on alarm conditions
    QString normalStyle = "QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #05B8CC, stop:1 #80E0FF); }";
    QString warningStyle = "QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #FFA500, stop:1 #FFD700); }";
    QString alarmStyle = "QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #FF0000, stop:1 #FF6666); }";

    // Apply styles based on current data quality
    QString styleToApply = normalStyle;
    if (m_dataQuality == DataQuality::Poor) {
        styleToApply = alarmStyle;
    } else if (m_dataQuality == DataQuality::Fair) {
        styleToApply = warningStyle;
    }

    // Apply to all progress bars
    if (m_pressureCPABar) m_pressureCPABar->setStyleSheet(styleToApply);
    if (m_pressureCPBBar) m_pressureCPBBar->setStyleSheet(styleToApply);
    if (m_pressureSupplyBar) m_pressureSupplyBar->setStyleSheet(styleToApply);
    if (m_pressureReturnBar) m_pressureReturnBar->setStyleSheet(styleToApply);
    if (m_flowRateBar) m_flowRateBar->setStyleSheet(styleToApply);
    if (m_tempFluidBar) m_tempFluidBar->setStyleSheet(styleToApply);
    if (m_tempAmbientBar) m_tempAmbientBar->setStyleSheet(styleToApply);
    if (m_actuatorPositionBar) m_actuatorPositionBar->setStyleSheet(styleToApply);
    if (m_actuatorVelocityBar) m_actuatorVelocityBar->setStyleSheet(styleToApply);
    if (m_actuatorForceBar) m_actuatorForceBar->setStyleSheet(styleToApply);
}

QString DataDisplayWidget::getStatusColor(double value, double min, double max) const
{
    if (value < min || value > max) {
        return "red";
    } else if (value < min * 1.1 || value > max * 0.9) {
        return "orange";
    } else {
        return "green";
    }
}
