#ifndef COURBE_H
#define COURBE_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QPointF>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(courbeModelLog)

/**
 * @brief Curve data model for hydraulic test bench system
 * 
 * This class represents curve data collected during hydraulic tests.
 * It stores time-series data points with X/Y coordinates and provides
 * methods for data analysis, interpolation, and visualization.
 */
class Courbe : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(int essaiId READ essaiId WRITE setEssaiId NOTIFY essaiIdChanged)
    Q_PROPERTY(QString typeCourbe READ typeCourbe WRITE setTypeCourbe NOTIFY typeCourbeChanged)
    Q_PROPERTY(QJsonArray pointsDonnees READ pointsDonnees WRITE setPointsDonnees NOTIFY pointsDonneesChanged)
    Q_PROPERTY(QString uniteX READ uniteX WRITE setUniteX NOTIFY uniteXChanged)
    Q_PROPERTY(QString uniteY READ uniteY WRITE setUniteY NOTIFY uniteYChanged)
    
public:
    /**
     * @brief Types of curves for hydraulic testing
     */
    enum class CurveType {
        PressureVsTime,         ///< Pressure evolution over time
        FlowVsTime,             ///< Flow rate evolution over time
        PositionVsTime,         ///< Actuator position over time
        VelocityVsTime,         ///< Actuator velocity over time
        ForceVsTime,            ///< Force evolution over time
        TemperatureVsTime,      ///< Temperature evolution over time
        PressureVsPosition,     ///< Pressure vs actuator position
        FlowVsPosition,         ///< Flow vs actuator position
        ForceVsPosition,        ///< Force vs actuator position
        EfficiencyVsTime,       ///< Efficiency evolution over time
        PowerVsTime,            ///< Power consumption over time
        PressureVsFlow,         ///< Pressure vs flow characteristic
        Custom                  ///< User-defined curve type
    };
    Q_ENUM(CurveType)
    
    explicit Courbe(QObject *parent = nullptr);
    Courbe(const Courbe& other);
    Courbe& operator=(const Courbe& other);
    virtual ~Courbe() = default;
    
    // Property accessors
    int id() const { return m_id; }
    void setId(int id);
    
    int essaiId() const { return m_essaiId; }
    void setEssaiId(int essaiId);
    
    QString typeCourbe() const { return m_typeCourbe; }
    void setTypeCourbe(const QString& typeCourbe);
    
    QJsonArray pointsDonnees() const { return m_pointsDonnees; }
    void setPointsDonnees(const QJsonArray& pointsDonnees);
    
    QString uniteX() const { return m_uniteX; }
    void setUniteX(const QString& uniteX);
    
    QString uniteY() const { return m_uniteY; }
    void setUniteY(const QString& uniteY);
    
    // Curve type management
    CurveType curveType() const;
    void setCurveType(CurveType type);
    QString curveTypeDisplayName() const;
    static QString curveTypeToString(CurveType type);
    static CurveType curveTypeFromString(const QString& typeStr);
    
    // Data point management
    void addDataPoint(double x, double y);
    void addDataPoint(const QPointF& point);
    void addDataPoints(const QList<QPointF>& points);
    void removeDataPoint(int index);
    void clearDataPoints();
    
    QList<QPointF> dataPoints() const;
    QPointF dataPoint(int index) const;
    int dataPointCount() const;
    bool hasDataPoints() const;
    
    // Data analysis
    double minX() const;
    double maxX() const;
    double minY() const;
    double maxY() const;
    QPointF minPoint() const;
    QPointF maxPoint() const;
    double averageY() const;
    double standardDeviationY() const;
    
    // Data processing
    QList<QPointF> getDataInRange(double minX, double maxX) const;
    QList<QPointF> downsample(int maxPoints) const;
    QList<QPointF> smooth(int windowSize = 5) const;
    double interpolateY(double x) const;
    
    // Statistics
    double calculateSlope(double x1, double x2) const;
    double calculateArea(double minX = -1, double maxX = -1) const;
    QPointF findPeak() const;
    QPointF findValley() const;
    
    // Validation
    bool isValid() const;
    QStringList validate() const;
    bool areDataPointsValid() const;
    bool areUnitsValid() const;
    
    // JSON serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    QJsonObject toJsonPartial() const; // For API updates
    
    // Utility methods
    QString displayName() const;
    QString description() const;
    QString formattedRange() const;
    
    // Operators
    bool operator==(const Courbe& other) const;
    bool operator!=(const Courbe& other) const { return !(*this == other); }
    
    // Static factory methods
    static Courbe createFromJson(const QJsonObject& json);
    static Courbe createNew(int essaiId, CurveType type);
    static QStringList getAllCurveTypes();
    static QPair<QString, QString> getDefaultUnits(CurveType type);
    
signals:
    void idChanged(int id);
    void essaiIdChanged(int essaiId);
    void typeCourbeChanged(const QString& typeCourbe);
    void pointsDonneesChanged(const QJsonArray& pointsDonnees);
    void uniteXChanged(const QString& uniteX);
    void uniteYChanged(const QString& uniteY);
    void courbeDataChanged();
    void validationChanged(bool isValid);
    void dataPointAdded(const QPointF& point);
    void dataPointRemoved(int index);
    void dataPointsCleared();
    
private:
    int m_id;
    int m_essaiId;
    QString m_typeCourbe;
    QJsonArray m_pointsDonnees;
    QString m_uniteX;
    QString m_uniteY;
    
    void connectSignals();
    void validateAndEmitSignals();
    bool isValidCurveType(const QString& type) const;
    QJsonArray pointsToJsonArray(const QList<QPointF>& points) const;
    QList<QPointF> jsonArrayToPoints(const QJsonArray& array) const;
    double calculateVariance(const QList<double>& values, double mean) const;
    
    // Constants
    static const int MAX_DATA_POINTS = 100000;
    static constexpr double INTERPOLATION_TOLERANCE = 1e-10;
};

#endif // COURBE_H
