#ifndef APIRESPONSE_H
#define APIRESPONSE_H

#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QVariant>
#include <type_traits>
#include "NetworkError.h"

/**
 * @brief Wrapper for API responses with error handling
 * 
 * This class provides a standardized way to handle API responses,
 * including success/error status, JSON data parsing, and error information.
 */
template<typename T = QJsonObject>
class ApiResponse
{
public:
    ApiResponse();
    ApiResponse(const T& data);
    ApiResponse(const NetworkError& error);
    ApiResponse(const QJsonDocument& jsonDoc);
    ApiResponse(const QByteArray& jsonData);
    
    // Status methods
    bool isSuccess() const { return !m_error.isValid(); }
    bool hasError() const { return m_error.isValid(); }
    
    // Data access
    const T& data() const { return m_data; }
    T& data() { return m_data; }
    void setData(const T& data) { m_data = data; }
    
    // Error access
    const NetworkError& error() const { return m_error; }
    void setError(const NetworkError& error) { m_error = error; }
    
    // JSON convenience methods
    QJsonObject jsonObject() const;
    QJsonArray jsonArray() const;
    QVariant value(const QString& key, const QVariant& defaultValue = QVariant()) const;
    QString message() const;
    
    // Validation
    bool hasKey(const QString& key) const;
    bool isNull() const;
    bool isEmpty() const;
    
    // Static factory methods
    static ApiResponse<T> fromJson(const QByteArray& jsonData);
    static ApiResponse<T> fromJsonDocument(const QJsonDocument& doc);
    static ApiResponse<T> success(const T& data);
    static ApiResponse<T> error(const NetworkError& error);
    static ApiResponse<T> error(const QString& message, NetworkError::Type type = NetworkError::Type::UnknownError);
    
private:
    T m_data;
    NetworkError m_error;
    
    void parseJsonResponse(const QJsonDocument& doc);
};

// Type aliases for common response types
using JsonResponse = ApiResponse<QJsonObject>;
using ArrayResponse = ApiResponse<QJsonArray>;

// Template implementation
template<typename T>
ApiResponse<T>::ApiResponse()
{
}

template<typename T>
ApiResponse<T>::ApiResponse(const T& data)
    : m_data(data)
{
}

template<typename T>
ApiResponse<T>::ApiResponse(const NetworkError& error)
    : m_error(error)
{
}

template<typename T>
ApiResponse<T>::ApiResponse(const QJsonDocument& jsonDoc)
{
    parseJsonResponse(jsonDoc);
}

template<typename T>
ApiResponse<T>::ApiResponse(const QByteArray& jsonData)
{
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonData, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        m_error = NetworkError::jsonParseError(parseError.errorString());
        return;
    }
    
    parseJsonResponse(doc);
}

template<typename T>
QJsonObject ApiResponse<T>::jsonObject() const
{
    if constexpr (std::is_same_v<T, QJsonObject>) {
        return m_data;
    } else {
        return QJsonObject();
    }
}

template<typename T>
QJsonArray ApiResponse<T>::jsonArray() const
{
    if constexpr (std::is_same_v<T, QJsonArray>) {
        return m_data;
    } else {
        return QJsonArray();
    }
}

template<typename T>
QVariant ApiResponse<T>::value(const QString& key, const QVariant& defaultValue) const
{
    if constexpr (std::is_same_v<T, QJsonObject>) {
        if (m_data.contains(key)) {
            return m_data.value(key).toVariant();
        }
    }
    return defaultValue;
}

template<typename T>
QString ApiResponse<T>::message() const
{
    if (hasError()) {
        return m_error.message();
    }
    
    if constexpr (std::is_same_v<T, QJsonObject>) {
        return m_data.value("message").toString();
    }
    
    return QString();
}

template<typename T>
bool ApiResponse<T>::hasKey(const QString& key) const
{
    if constexpr (std::is_same_v<T, QJsonObject>) {
        return m_data.contains(key);
    }
    return false;
}

template<typename T>
bool ApiResponse<T>::isNull() const
{
    if constexpr (std::is_same_v<T, QJsonObject>) {
        return m_data.isEmpty();
    } else if constexpr (std::is_same_v<T, QJsonArray>) {
        return m_data.isEmpty();
    }
    return false;
}

template<typename T>
bool ApiResponse<T>::isEmpty() const
{
    return isNull();
}

template<typename T>
ApiResponse<T> ApiResponse<T>::fromJson(const QByteArray& jsonData)
{
    return ApiResponse<T>(jsonData);
}

template<typename T>
ApiResponse<T> ApiResponse<T>::fromJsonDocument(const QJsonDocument& doc)
{
    return ApiResponse<T>(doc);
}

template<typename T>
ApiResponse<T> ApiResponse<T>::success(const T& data)
{
    return ApiResponse<T>(data);
}

template<typename T>
ApiResponse<T> ApiResponse<T>::error(const NetworkError& error)
{
    return ApiResponse<T>(error);
}

template<typename T>
ApiResponse<T> ApiResponse<T>::error(const QString& message, NetworkError::Type type)
{
    return ApiResponse<T>(NetworkError(type, message));
}

template<typename T>
void ApiResponse<T>::parseJsonResponse(const QJsonDocument& doc)
{
    if (doc.isNull() || doc.isEmpty()) {
        m_error = NetworkError::jsonParseError("Empty JSON document");
        return;
    }
    
    // Check for error response format
    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        if (obj.contains("error")) {
            QString errorMsg = obj.value("error").toString();
            m_error = NetworkError(NetworkError::Type::ServerError, errorMsg);
            return;
        }
    }
    
    // Parse successful response
    if constexpr (std::is_same_v<T, QJsonObject>) {
        if (doc.isObject()) {
            m_data = doc.object();
        } else {
            m_error = NetworkError::jsonParseError("Expected JSON object");
        }
    } else if constexpr (std::is_same_v<T, QJsonArray>) {
        if (doc.isArray()) {
            m_data = doc.array();
        } else {
            m_error = NetworkError::jsonParseError("Expected JSON array");
        }
    }
}

#endif // APIRESPONSE_H
