#include "RealTimeChart.h"
#include <QDebug>
#include <QFileDialog>
#include <QMessageBox>
#include <QTextStream>
#include <QtMath>

Q_LOGGING_CATEGORY(realTimeChartLog, "hydraulic.widgets.realtimechart")

const QStringList RealTimeChart::DEFAULT_PARAMETERS = {
    "pressure_cpa", "pressure_cpb", "flow_rate", "actuator_position", "actuator_velocity"
};

RealTimeChart::RealTimeChart(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_chart(nullptr)
    , m_chartView(nullptr)
    , m_xAxis(nullptr)
    , m_yAxis(nullptr)
    , m_updateTimer(new QTimer(this))
    , m_realTimeActive(false)
    , m_isPaused(false)
    , m_totalDataPoints(0)
{
    // Initialize default configuration
    m_config.title = "Hydraulic Test Data";
    m_config.xAxisTitle = "Time";
    m_config.yAxisTitle = "Value";
    m_config.maxDataPoints = DEFAULT_MAX_POINTS;
    m_config.timeWindowSeconds = DEFAULT_TIME_WINDOW_SECONDS;
    m_config.autoScale = true;
    m_config.yAxisMin = 0.0;
    m_config.yAxisMax = 100.0;
    m_config.showGrid = true;
    m_config.showLegend = true;
    m_config.enableZoom = true;
    m_config.enablePan = true;
    m_config.updateIntervalMs = DEFAULT_UPDATE_INTERVAL_MS;
    
    setupUI();
    
    // Setup update timer
    m_updateTimer->setInterval(m_config.updateIntervalMs);
    connect(m_updateTimer, &QTimer::timeout, this, &RealTimeChart::onUpdateTimer);
    
    // Initialize start time
    m_startTime = QDateTime::currentDateTime();
    
    qCDebug(realTimeChartLog) << "RealTimeChart initialized";
}

void RealTimeChart::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(5);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    
    setupChart();
    setupControls();
}

void RealTimeChart::setupChart()
{
    // Create chart
    m_chart = new QChart();
    m_chart->setTitle(m_config.title);
    m_chart->setAnimationOptions(QChart::NoAnimation); // Disable for real-time performance

    // Create chart view
    m_chartView = new QChartView(m_chart, this);
    m_chartView->setRenderHint(QPainter::Antialiasing);
    m_chartView->setMinimumHeight(400);

    // Enable zoom and pan if configured
    if (m_config.enableZoom) {
        m_chartView->setRubberBand(QChartView::RectangleRubberBand);
    }

    // Create axes
    m_xAxis = new QDateTimeAxis();
    m_xAxis->setTitleText(m_config.xAxisTitle);
    m_xAxis->setFormat("hh:mm:ss");
    m_chart->addAxis(m_xAxis, Qt::AlignBottom);

    m_yAxis = new QValueAxis();
    m_yAxis->setTitleText(m_config.yAxisTitle);
    m_chart->addAxis(m_yAxis, Qt::AlignLeft);
    
    // Configure legend
    if (m_config.showLegend) {
        m_chart->legend()->setVisible(true);
        m_chart->legend()->setAlignment(Qt::AlignBottom);
    } else {
        m_chart->legend()->setVisible(false);
    }
    
    m_mainLayout->addWidget(m_chartView);
    
    applyChartConfig();
}

void RealTimeChart::setupControls()
{
    m_controlGroup = new QGroupBox("Chart Controls", this);
    QHBoxLayout* controlLayout = new QHBoxLayout(m_controlGroup);
    
    // Time window control
    controlLayout->addWidget(new QLabel("Time Window:", this));
    m_timeWindowComboBox = new QComboBox(this);
    m_timeWindowComboBox->addItems({"10s", "30s", "1min", "2min", "5min", "10min"});
    m_timeWindowComboBox->setCurrentText("1min");
    connect(m_timeWindowComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &RealTimeChart::onTimeWindowChanged);
    controlLayout->addWidget(m_timeWindowComboBox);
    
    controlLayout->addWidget(new QLabel("|", this)); // Separator
    
    // Auto-scale control
    m_autoScaleCheckBox = new QCheckBox("Auto Scale", this);
    m_autoScaleCheckBox->setChecked(m_config.autoScale);
    connect(m_autoScaleCheckBox, &QCheckBox::toggled, this, &RealTimeChart::onAutoScaleToggled);
    controlLayout->addWidget(m_autoScaleCheckBox);
    
    // Y-axis range controls
    controlLayout->addWidget(new QLabel("Y Min:", this));
    m_yMinSpinBox = new QDoubleSpinBox(this);
    m_yMinSpinBox->setRange(-10000.0, 10000.0);
    m_yMinSpinBox->setValue(m_config.yAxisMin);
    m_yMinSpinBox->setEnabled(!m_config.autoScale);
    connect(m_yMinSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &RealTimeChart::onYAxisRangeChanged);
    controlLayout->addWidget(m_yMinSpinBox);
    
    controlLayout->addWidget(new QLabel("Y Max:", this));
    m_yMaxSpinBox = new QDoubleSpinBox(this);
    m_yMaxSpinBox->setRange(-10000.0, 10000.0);
    m_yMaxSpinBox->setValue(m_config.yAxisMax);
    m_yMaxSpinBox->setEnabled(!m_config.autoScale);
    connect(m_yMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &RealTimeChart::onYAxisRangeChanged);
    controlLayout->addWidget(m_yMaxSpinBox);
    
    controlLayout->addWidget(new QLabel("|", this)); // Separator
    
    // Control buttons
    m_pauseResumeButton = new QPushButton("Pause", this);
    connect(m_pauseResumeButton, &QPushButton::clicked, this, [this]() {
        if (m_isPaused) {
            resumeRealTimeUpdate();
        } else {
            pauseRealTimeUpdate();
        }
    });
    controlLayout->addWidget(m_pauseResumeButton);
    
    m_resetZoomButton = new QPushButton("Reset Zoom", this);
    connect(m_resetZoomButton, &QPushButton::clicked, this, &RealTimeChart::onResetZoomClicked);
    controlLayout->addWidget(m_resetZoomButton);
    
    m_clearDataButton = new QPushButton("Clear Data", this);
    connect(m_clearDataButton, &QPushButton::clicked, this, &RealTimeChart::onClearDataClicked);
    controlLayout->addWidget(m_clearDataButton);
    
    m_exportButton = new QPushButton("Export", this);
    connect(m_exportButton, &QPushButton::clicked, this, &RealTimeChart::onExportClicked);
    controlLayout->addWidget(m_exportButton);
    
    controlLayout->addStretch();
    
    m_mainLayout->addWidget(m_controlGroup);
}

void RealTimeChart::addSeries(const SeriesConfig& config)
{
    if (m_series.contains(config.name)) {
        qCWarning(realTimeChartLog) << "Series already exists:" << config.name;
        return;
    }
    
    // Create new series
    QLineSeries* series = new QLineSeries();
    series->setName(config.name);
    series->setColor(config.color);
    series->setPen(QPen(config.color, config.lineWidth));
    series->setVisible(config.visible);
    
    // Add to chart
    m_chart->addSeries(series);
    series->attachAxis(m_xAxis);
    series->attachAxis(m_yAxis);
    
    // Store references
    m_series[config.name] = series;
    m_seriesConfigs[config.name] = config;
    m_seriesData[config.name] = QList<QPointF>();
    
    emit seriesAdded(config.name);
    qCDebug(realTimeChartLog) << "Series added:" << config.name;
}

void RealTimeChart::removeSeries(const QString& seriesName)
{
    if (!m_series.contains(seriesName)) {
        return;
    }
    
    // Remove from chart
    QLineSeries* series = m_series.take(seriesName);
    m_chart->removeSeries(series);
    delete series;
    
    // Remove configurations and data
    m_seriesConfigs.remove(seriesName);
    m_seriesData.remove(seriesName);
    
    emit seriesRemoved(seriesName);
    qCDebug(realTimeChartLog) << "Series removed:" << seriesName;
}

void RealTimeChart::clearSeries()
{
    QStringList seriesNames = m_series.keys();
    for (const QString& name : seriesNames) {
        removeSeries(name);
    }
}

void RealTimeChart::addDataPoint(const TestData& data)
{
    if (!data.isValid()) {
        return;
    }
    
    m_recentData.append(data);
    m_totalDataPoints++;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Limit recent data size
    while (m_recentData.size() > m_config.maxDataPoints) {
        m_recentData.removeFirst();
    }
    
    // Process data for all series
    for (auto it = m_seriesConfigs.begin(); it != m_seriesConfigs.end(); ++it) {
        const QString& seriesName = it.key();
        const SeriesConfig& config = it.value();
        
        QPointF point = extractDataPoint(data, config.parameterName);
        if (!point.isNull()) {
            m_seriesData[seriesName].append(point);
            
            // Limit series data
            while (m_seriesData[seriesName].size() > m_config.maxDataPoints) {
                m_seriesData[seriesName].removeFirst();
            }
        }
    }
    
    emit dataPointAdded(data);
}

void RealTimeChart::startRealTimeUpdate()
{
    if (m_realTimeActive) {
        return;
    }
    
    m_realTimeActive = true;
    m_isPaused = false;
    m_updateTimer->start();
    
    qCDebug(realTimeChartLog) << "Real-time update started";
}

void RealTimeChart::stopRealTimeUpdate()
{
    if (!m_realTimeActive) {
        return;
    }
    
    m_realTimeActive = false;
    m_isPaused = false;
    m_updateTimer->stop();
    
    qCDebug(realTimeChartLog) << "Real-time update stopped";
}

void RealTimeChart::pauseRealTimeUpdate()
{
    if (!m_realTimeActive || m_isPaused) {
        return;
    }
    
    m_isPaused = true;
    m_updateTimer->stop();
    m_pauseResumeButton->setText("Resume");
    
    qCDebug(realTimeChartLog) << "Real-time update paused";
}

void RealTimeChart::resumeRealTimeUpdate()
{
    if (!m_realTimeActive || !m_isPaused) {
        return;
    }
    
    m_isPaused = false;
    m_updateTimer->start();
    m_pauseResumeButton->setText("Pause");
    
    qCDebug(realTimeChartLog) << "Real-time update resumed";
}

void RealTimeChart::clearData()
{
    m_recentData.clear();
    m_totalDataPoints = 0;
    
    // Clear all series data
    for (auto it = m_seriesData.begin(); it != m_seriesData.end(); ++it) {
        it.value().clear();
    }
    
    // Update chart
    updateChart();
    
    qCDebug(realTimeChartLog) << "Chart data cleared";
}

void RealTimeChart::onUpdateTimer()
{
    if (m_isPaused) {
        return;
    }

    updateChart();
}

void RealTimeChart::onSeriesVisibilityChanged()
{
    // This slot is called when series visibility changes
    // Update the chart to reflect visibility changes
    updateChart();
    emit chartConfigChanged();
}

void RealTimeChart::updateChart()
{
    updateSeries();
    updateAxes();
    
    if (m_config.autoScale) {
        calculateAutoScale();
    }
}

void RealTimeChart::updateSeries()
{
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeWindowMs = static_cast<qint64>(m_config.timeWindowSeconds * 1000);
    QDateTime cutoffTime = currentTime.addMSecs(-timeWindowMs);
    
    for (auto it = m_series.begin(); it != m_series.end(); ++it) {
        const QString& seriesName = it.key();
        QLineSeries* series = it.value();
        const SeriesConfig& config = m_seriesConfigs.value(seriesName);
        
        if (!config.visible) {
            continue;
        }
        
        // Filter data points within time window
        QList<QPointF> visibleData;
        const QList<QPointF>& seriesData = m_seriesData.value(seriesName);
        
        for (const QPointF& point : seriesData) {
            QDateTime pointTime = QDateTime::fromMSecsSinceEpoch(static_cast<qint64>(point.x()));
            if (pointTime >= cutoffTime) {
                visibleData.append(point);
            }
        }
        
        // Update series with visible data
        series->clear();
        if (!visibleData.isEmpty()) {
            series->append(visibleData);
        }
    }
}

void RealTimeChart::updateAxes()
{
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeWindowMs = static_cast<qint64>(m_config.timeWindowSeconds * 1000);
    QDateTime startTime = currentTime.addMSecs(-timeWindowMs);
    
    m_xAxis->setRange(startTime, currentTime);
    
    if (!m_config.autoScale) {
        m_yAxis->setRange(m_config.yAxisMin, m_config.yAxisMax);
    }
}

void RealTimeChart::calculateAutoScale()
{
    if (!m_config.autoScale) {
        return;
    }
    
    double minValue = std::numeric_limits<double>::max();
    double maxValue = std::numeric_limits<double>::lowest();
    bool hasData = false;
    
    // Find min/max across all visible series
    for (auto it = m_series.begin(); it != m_series.end(); ++it) {
        const QString& seriesName = it.key();
        QLineSeries* series = it.value();
        const SeriesConfig& config = m_seriesConfigs.value(seriesName);
        
        if (!config.visible || series->count() == 0) {
            continue;
        }
        
        const QList<QPointF>& points = series->points();
        for (const QPointF& point : points) {
            double value = point.y();
            minValue = qMin(minValue, value);
            maxValue = qMax(maxValue, value);
            hasData = true;
        }
    }
    
    if (hasData) {
        // Add 10% margin
        double range = maxValue - minValue;
        double margin = range * 0.1;
        
        if (range < 1e-6) { // Very small range
            margin = 1.0;
        }
        
        m_yAxis->setRange(minValue - margin, maxValue + margin);
    }
}

QPointF RealTimeChart::extractDataPoint(const TestData& data, const QString& parameterName) const
{
    if (!data.isValid() || !data.timestamp().isValid()) {
        return QPointF();
    }
    
    double timestamp = data.timestamp().toMSecsSinceEpoch();
    double value = 0.0;
    
    // Extract value based on parameter name
    if (parameterName == "pressure_cpa") {
        value = data.pressureCPA();
    } else if (parameterName == "pressure_cpb") {
        value = data.pressureCPB();
    } else if (parameterName == "flow_rate") {
        value = data.flowRate();
    } else if (parameterName == "actuator_position") {
        value = data.actuatorPosition();
    } else if (parameterName == "actuator_velocity") {
        value = data.actuatorVelocity();
    } else if (parameterName == "actuator_force") {
        value = data.actuatorForce();
    } else if (parameterName == "temperature_fluid") {
        value = data.temperatureFluid();
    } else if (parameterName == "power_hydraulic") {
        value = data.powerHydraulic();
    } else if (parameterName == "efficiency") {
        value = data.efficiency();
    } else {
        // Try to get from parameters
        QVariant paramValue = data.parameter(parameterName);
        if (paramValue.isValid()) {
            value = paramValue.toDouble();
        } else {
            return QPointF(); // Parameter not found
        }
    }
    
    return QPointF(timestamp, value);
}

void RealTimeChart::onTimeWindowChanged()
{
    QString timeText = m_timeWindowComboBox->currentText();
    double seconds = DEFAULT_TIME_WINDOW_SECONDS;
    
    if (timeText == "10s") seconds = 10.0;
    else if (timeText == "30s") seconds = 30.0;
    else if (timeText == "1min") seconds = 60.0;
    else if (timeText == "2min") seconds = 120.0;
    else if (timeText == "5min") seconds = 300.0;
    else if (timeText == "10min") seconds = 600.0;
    
    setTimeWindow(seconds);
}

void RealTimeChart::setTimeWindow(double seconds)
{
    m_config.timeWindowSeconds = seconds;
    emit timeWindowChanged(seconds);
    
    qCDebug(realTimeChartLog) << "Time window changed to" << seconds << "seconds";
}

void RealTimeChart::onAutoScaleToggled(bool enabled)
{
    m_config.autoScale = enabled;
    m_yMinSpinBox->setEnabled(!enabled);
    m_yMaxSpinBox->setEnabled(!enabled);
    
    if (!enabled) {
        onYAxisRangeChanged();
    }
    
    emit chartConfigChanged();
}

void RealTimeChart::onYAxisRangeChanged()
{
    if (m_config.autoScale) {
        return;
    }
    
    m_config.yAxisMin = m_yMinSpinBox->value();
    m_config.yAxisMax = m_yMaxSpinBox->value();
    
    m_yAxis->setRange(m_config.yAxisMin, m_config.yAxisMax);
    
    emit chartConfigChanged();
}

void RealTimeChart::onResetZoomClicked()
{
    resetZoom();
}

void RealTimeChart::resetZoom()
{
    m_chart->zoomReset();
    updateAxes();

    emit zoomChanged();
    qCDebug(realTimeChartLog) << "Zoom reset";
}

void RealTimeChart::onExportClicked()
{
    QString filename = QFileDialog::getSaveFileName(this, 
        "Export Chart", 
        QString("chart_%1.png").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        "PNG Images (*.png);;JPEG Images (*.jpg);;CSV Data (*.csv)");
    
    if (filename.isEmpty()) {
        return;
    }
    
    if (filename.endsWith(".csv")) {
        exportDataToCsv(filename);
    } else {
        saveChartImage(filename);
    }
}

void RealTimeChart::onClearDataClicked()
{
    int ret = QMessageBox::question(this, "Clear Data", 
                                  "Are you sure you want to clear all chart data?",
                                  QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        clearData();
    }
}

bool RealTimeChart::saveChartImage(const QString& filename) const
{
    QPixmap pixmap = exportChart();
    bool success = pixmap.save(filename);
    
    if (success) {
        qCDebug(realTimeChartLog) << "Chart image saved to" << filename;
    } else {
        qCWarning(realTimeChartLog) << "Failed to save chart image to" << filename;
    }
    
    return success;
}

QPixmap RealTimeChart::exportChart() const
{
    return m_chartView->grab();
}

bool RealTimeChart::exportDataToCsv(const QString& filename) const
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qCWarning(realTimeChartLog) << "Failed to open file for writing:" << filename;
        return false;
    }
    
    QTextStream stream(&file);
    
    // Write header
    stream << "Timestamp";
    for (const QString& seriesName : m_series.keys()) {
        stream << "," << seriesName;
    }
    stream << "\n";
    
    // Find all unique timestamps
    QSet<qint64> timestamps;
    for (const auto& seriesData : m_seriesData) {
        for (const QPointF& point : seriesData) {
            timestamps.insert(static_cast<qint64>(point.x()));
        }
    }
    
    QList<qint64> sortedTimestamps(timestamps.begin(), timestamps.end());
    std::sort(sortedTimestamps.begin(), sortedTimestamps.end());
    
    // Write data
    for (qint64 timestamp : sortedTimestamps) {
        QDateTime dateTime = QDateTime::fromMSecsSinceEpoch(timestamp);
        stream << dateTime.toString(Qt::ISODate);
        
        for (const QString& seriesName : m_series.keys()) {
            double value = 0.0;
            bool found = false;
            
            const QList<QPointF>& seriesData = m_seriesData.value(seriesName);
            for (const QPointF& point : seriesData) {
                if (static_cast<qint64>(point.x()) == timestamp) {
                    value = point.y();
                    found = true;
                    break;
                }
            }
            
            stream << ",";
            if (found) {
                stream << value;
            }
        }
        stream << "\n";
    }
    
    qCDebug(realTimeChartLog) << "Data exported to CSV:" << filename;
    return true;
}

void RealTimeChart::applyChartConfig()
{
    m_chart->setTitle(m_config.title);
    
    if (m_xAxis) {
        m_xAxis->setTitleText(m_config.xAxisTitle);
        m_xAxis->setGridLineVisible(m_config.showGrid);
    }
    
    if (m_yAxis) {
        m_yAxis->setTitleText(m_config.yAxisTitle);
        m_yAxis->setGridLineVisible(m_config.showGrid);
    }
    
    if (m_chart->legend()) {
        m_chart->legend()->setVisible(m_config.showLegend);
    }
    
    m_updateTimer->setInterval(m_config.updateIntervalMs);
}
