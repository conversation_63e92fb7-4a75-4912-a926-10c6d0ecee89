#include "FlowMeter.h"
#include <QDebug>
#include <QtMath>

// Default flow values in L/min
const double FlowMeter::DEFAULT_MAX_FLOW = 100.0;
const double FlowMeter::DEFAULT_MIN_FLOW = 0.0;
const double FlowMeter::CAVITATION_THRESHOLD = 10.0; // bar

FlowMeter::FlowMeter(QObject *parent)
    : SimulatedSensor(parent)
    , m_flowDirection(FlowDirection::Bidirectional)
    , m_flowTestTimer(new QTimer(this))
    , m_flowRampTimer(new QTimer(this))
    , m_flowStepTimer(new QTimer(this))
    , m_flowPulseTimer(new QTimer(this))
    , m_currentFlowTestMode(FlowTestMode::Normal)
    , m_testTargetFlow(0.0)
    , m_testStartFlow(0.0)
    , m_testEndFlow(0.0)
    , m_testBaseFlow(0.0)
    , m_testPulseAmplitude(0.0)
    , m_testDuration(0)
    , m_rampTime(0)
    , m_stepDuration(0)
    , m_pulseFrequency(1)
{
    // Configure for flow measurement
    m_simConfig.minValue = DEFAULT_MIN_FLOW;
    m_simConfig.maxValue = DEFAULT_MAX_FLOW;
    m_simConfig.noiseLevel = 0.02; // 2% noise
    m_simConfig.updateIntervalMs = 50; // Higher frequency for flow
    
    // Setup test timers
    m_flowTestTimer->setSingleShot(true);
    QObject::connect(m_flowTestTimer, &QTimer::timeout, this, &FlowMeter::onFlowTestTimer);

    m_flowRampTimer->setSingleShot(false);
    m_flowRampTimer->setInterval(100); // Update every 100ms during ramp
    QObject::connect(m_flowRampTimer, &QTimer::timeout, this, &FlowMeter::onFlowRampTimer);

    m_flowStepTimer->setSingleShot(true);
    QObject::connect(m_flowStepTimer, &QTimer::timeout, this, &FlowMeter::onFlowStepTimer);

    m_flowPulseTimer->setSingleShot(false);
    QObject::connect(m_flowPulseTimer, &QTimer::timeout, this, &FlowMeter::onFlowPulseTimer);
}

QString FlowMeter::deviceName() const
{
    return "Hydraulic Flow Meter";
}

QString FlowMeter::deviceVersion() const
{
    return "Simulated v1.0";
}

HardwareInterface::DeviceType FlowMeter::deviceType() const
{
    return DeviceType::FlowMeter;
}

double FlowMeter::generateRealisticValue()
{
    switch (m_currentFlowTestMode) {
        case FlowTestMode::FlowTest:
            return generateFlowTestValue();
        case FlowTestMode::FlowRamp:
            return generateFlowRampValue();
        case FlowTestMode::FlowStep:
            return generateFlowStepValue();
        case FlowTestMode::FlowPulse:
            return generateFlowPulseValue();
        case FlowTestMode::Normal:
        default:
            return generateNormalFlowValue();
    }
}

QString FlowMeter::getPrimaryParameter() const
{
    return "flow";
}

QStringList FlowMeter::getAllParameters() const
{
    return QStringList() << "flow" << "flow_gpm" << "flow_cms" << "velocity" << "reynolds_number";
}

void FlowMeter::setFlowDirection(FlowDirection direction)
{
    if (m_flowDirection != direction) {
        m_flowDirection = direction;
        emit flowDirectionChanged(direction);
    }
}

void FlowMeter::simulateFlowTest(double targetFlow, int durationMs)
{
    m_currentFlowTestMode = FlowTestMode::FlowTest;
    m_testTargetFlow = targetFlow;
    m_testDuration = durationMs;
    m_flowTestStartTime = QDateTime::currentDateTime();
    
    m_flowTestTimer->start(durationMs);
    
    qDebug() << "Starting flow test:" << targetFlow << "L/min for" << durationMs << "ms";
}

void FlowMeter::simulateFlowRamp(double startFlow, double endFlow, int rampTimeMs)
{
    m_currentFlowTestMode = FlowTestMode::FlowRamp;
    m_testStartFlow = startFlow;
    m_testEndFlow = endFlow;
    m_rampTime = rampTimeMs;
    m_flowTestStartTime = QDateTime::currentDateTime();
    
    m_flowRampTimer->start();
    
    // Set timer to complete ramp
    QTimer::singleShot(rampTimeMs, this, [this]() {
        m_flowRampTimer->stop();
        emit flowRampCompleted();
        m_currentFlowTestMode = FlowTestMode::Normal;
    });
    
    qDebug() << "Starting flow ramp:" << startFlow << "to" << endFlow << "L/min over" << rampTimeMs << "ms";
}

void FlowMeter::simulateFlowStep(double stepFlow, int stepDurationMs)
{
    m_currentFlowTestMode = FlowTestMode::FlowStep;
    m_testTargetFlow = stepFlow;
    m_stepDuration = stepDurationMs;
    m_flowTestStartTime = QDateTime::currentDateTime();
    
    m_flowStepTimer->start(stepDurationMs);
    
    qDebug() << "Starting flow step:" << stepFlow << "L/min for" << stepDurationMs << "ms";
}

void FlowMeter::simulateFlowPulse(double baseFlow, double pulseAmplitude, int pulseFrequencyHz)
{
    m_currentFlowTestMode = FlowTestMode::FlowPulse;
    m_testBaseFlow = baseFlow;
    m_testPulseAmplitude = pulseAmplitude;
    m_pulseFrequency = pulseFrequencyHz;
    m_flowTestStartTime = QDateTime::currentDateTime();
    
    int pulseIntervalMs = 1000 / pulseFrequencyHz;
    m_flowPulseTimer->start(pulseIntervalMs);
    
    qDebug() << "Starting flow pulse:" << baseFlow << "±" << pulseAmplitude << "L/min at" << pulseFrequencyHz << "Hz";
}

double FlowMeter::lpmToGpm(double lpm)
{
    return lpm * 0.264172;
}

double FlowMeter::gpmToLpm(double gpm)
{
    return gpm / 0.264172;
}

double FlowMeter::lpmToCms(double lpm)
{
    return lpm / 60000.0; // L/min to m³/s
}

double FlowMeter::cmsToLpm(double cms)
{
    return cms * 60000.0; // m³/s to L/min
}

double FlowMeter::calculateVolumetricEfficiency(double theoreticalFlow) const
{
    double actualFlow = m_currentValues.value("flow", 0.0);
    if (theoreticalFlow == 0.0) return 0.0;
    return (actualFlow / theoreticalFlow) * 100.0;
}

double FlowMeter::calculateReynoldsNumber(double pipeDiameter, double kinematicViscosity) const
{
    double flow = m_currentValues.value("flow", 0.0);
    double velocity = flow / (M_PI * qPow(pipeDiameter / 2.0, 2) * 60.0); // m/s
    
    if (kinematicViscosity == 0.0) return 0.0;
    return (velocity * pipeDiameter) / kinematicViscosity;
}

void FlowMeter::onFlowTestTimer()
{
    if (m_currentFlowTestMode == FlowTestMode::FlowTest) {
        emit flowTestCompleted();
        m_currentFlowTestMode = FlowTestMode::Normal;
        qDebug() << "Flow test completed";
    }
}

void FlowMeter::onFlowRampTimer()
{
    // This is handled by the lambda in simulateFlowRamp
}

void FlowMeter::onFlowStepTimer()
{
    if (m_currentFlowTestMode == FlowTestMode::FlowStep) {
        emit flowStepCompleted();
        m_currentFlowTestMode = FlowTestMode::Normal;
        qDebug() << "Flow step completed";
    }
}

void FlowMeter::onFlowPulseTimer()
{
    // Pulse timer continues until manually stopped
}

double FlowMeter::generateFlowTestValue()
{
    // Gradually approach target flow
    double currentFlow = m_currentValues.value("flow", 0.0);
    double flowDiff = m_testTargetFlow - currentFlow;
    
    // Approach target with realistic response
    double approachRate = 0.15; // 15% per update
    double newFlow = currentFlow + flowDiff * approachRate;
    
    return addFlowNoise(newFlow);
}

double FlowMeter::generateFlowRampValue()
{
    qint64 elapsedMs = m_flowTestStartTime.msecsTo(QDateTime::currentDateTime());
    double progress = qMin(1.0, double(elapsedMs) / m_rampTime);
    
    double currentFlow = m_testStartFlow + (m_testEndFlow - m_testStartFlow) * progress;
    
    return addFlowNoise(currentFlow);
}

double FlowMeter::generateFlowStepValue()
{
    // Immediate step to target flow with some overshoot
    qint64 elapsedMs = m_flowTestStartTime.msecsTo(QDateTime::currentDateTime());
    double overshootFactor = qExp(-elapsedMs / 500.0); // Decay over 500ms
    double overshoot = m_testTargetFlow * 0.1 * overshootFactor;
    
    return addFlowNoise(m_testTargetFlow + overshoot);
}

double FlowMeter::generateFlowPulseValue()
{
    qint64 elapsedMs = m_flowTestStartTime.msecsTo(QDateTime::currentDateTime());
    double timeSeconds = elapsedMs / 1000.0;
    
    double pulseValue = m_testPulseAmplitude * qSin(2 * M_PI * m_pulseFrequency * timeSeconds);
    double currentFlow = m_testBaseFlow + pulseValue;
    
    return addFlowNoise(currentFlow);
}

double FlowMeter::generateNormalFlowValue()
{
    // Generate realistic flow based on target
    double targetFlow = m_targetValues.value("flow", 30.0);
    
    // Add realistic flow characteristics
    double flow = generateTurbulentFlow(targetFlow);
    flow = simulateCavitation(flow);
    
    return addFlowNoise(flow);
}

double FlowMeter::generateTurbulentFlow(double baseFlow)
{
    // Add turbulent fluctuations
    double turbulenceLevel = 0.05; // 5% turbulence
    double turbulence = turbulenceLevel * baseFlow * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
    
    return baseFlow + turbulence;
}

double FlowMeter::generateLaminarFlow(double baseFlow)
{
    // Laminar flow is more stable
    double variation = 0.01; // 1% variation
    double noise = variation * baseFlow * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
    
    return baseFlow + noise;
}

double FlowMeter::addFlowNoise(double baseFlow)
{
    double noiseLevel = m_simConfig.noiseLevel;
    double noise = noiseLevel * baseFlow * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
    
    return baseFlow + noise;
}

double FlowMeter::simulateCavitation(double baseFlow)
{
    if (isCavitationCondition()) {
        // Cavitation causes flow instability and reduction
        double cavitationEffect = 0.8 + 0.2 * m_randomGenerator->generateDouble(); // 80-100% of normal flow
        emit cavitationDetected();
        return baseFlow * cavitationEffect;
    }
    
    return baseFlow;
}

bool FlowMeter::isCavitationCondition() const
{
    // Simplified cavitation detection based on low pressure
    // In real implementation, this would check actual pressure sensors
    return m_randomGenerator->bounded(1000) < 5; // 0.5% chance for simulation
}

double FlowMeter::getFlowCoefficient() const
{
    // Flow coefficient varies with Reynolds number and pipe geometry
    // Simplified model
    return 0.95 + 0.05 * m_randomGenerator->generateDouble();
}
