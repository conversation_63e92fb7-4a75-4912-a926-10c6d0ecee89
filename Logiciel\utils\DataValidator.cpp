#include "DataValidator.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDebug>
#include <QMutexLocker>
#include <QtMath>

Q_LOGGING_CATEGORY(dataValidatorLog, "hydraulic.utils.datavalidator")

// Static constants
const double DataValidator::DEFAULT_VALIDATION_TOLERANCE = 0.05; // 5%
const double DataValidator::DEFAULT_MAX_RATE_OF_CHANGE = 1000.0; // per second
const int DataValidator::VALIDATION_HISTORY_SIZE = 1000;

DataValidator::DataValidator(QObject *parent)
    : QObject(parent)
    , m_validationEnabled(true)
    , m_strictMode(false)
    , m_validationTolerance(DEFAULT_VALIDATION_TOLERANCE)
{
    // Load default validation rules
    loadDefaultRules();
    
    qCDebug(dataValidatorLog) << "DataValidator initialized";
}

void DataValidator::addValidationRule(const ValidationRule& rule)
{
    QMutexLocker locker(&m_mutex);
    m_validationRules[rule.parameterName] = rule;
    emit validationRulesChanged();
    
    qCDebug(dataValidatorLog) << "Added validation rule for" << rule.parameterName;
}

void DataValidator::removeValidationRule(const QString& parameterName)
{
    QMutexLocker locker(&m_mutex);
    if (m_validationRules.remove(parameterName) > 0) {
        emit validationRulesChanged();
        qCDebug(dataValidatorLog) << "Removed validation rule for" << parameterName;
    }
}

void DataValidator::clearValidationRules()
{
    QMutexLocker locker(&m_mutex);
    m_validationRules.clear();
    emit validationRulesChanged();
    
    qCDebug(dataValidatorLog) << "Cleared all validation rules";
}

QList<DataValidator::ValidationRule> DataValidator::validationRules() const
{
    QMutexLocker locker(&m_mutex);
    return m_validationRules.values();
}

DataValidator::ValidationRule DataValidator::getValidationRule(const QString& parameterName) const
{
    QMutexLocker locker(&m_mutex);
    return m_validationRules.value(parameterName);
}

bool DataValidator::hasValidationRule(const QString& parameterName) const
{
    QMutexLocker locker(&m_mutex);
    return m_validationRules.contains(parameterName);
}

void DataValidator::setTestConfiguration(std::shared_ptr<TestConfiguration> config)
{
    QMutexLocker locker(&m_mutex);
    m_testConfig = config;
    
    if (m_testConfig) {
        connect(m_testConfig.get(), &TestConfiguration::configurationChanged,
                this, &DataValidator::onTestConfigurationChanged);
        
        // Update validation rules based on test configuration
        createHydraulicValidationRules();
    }
    
    qCDebug(dataValidatorLog) << "Test configuration set";
}

std::shared_ptr<TestConfiguration> DataValidator::testConfiguration() const
{
    QMutexLocker locker(&m_mutex);
    return m_testConfig;
}

DataValidator::ValidationResult DataValidator::validateSensorReading(const QString& sensor, double value)
{
    ValidationResult result;
    result.validationTime = QDateTime::currentDateTime();
    
    if (!m_validationEnabled) {
        result.isValid = true;
        result.summary = "Validation disabled";
        return result;
    }
    
    QMutexLocker locker(&m_mutex);
    
    // Check if we have a validation rule for this sensor
    if (!hasValidationRule(sensor)) {
        if (m_strictMode) {
            result.isValid = false;
            result.errors.append(QString("No validation rule defined for sensor: %1").arg(sensor));
        } else {
            result.warnings.append(QString("No validation rule defined for sensor: %1").arg(sensor));
        }
    } else {
        ValidationRule rule = getValidationRule(sensor);
        
        // Validate range
        if (rule.enableRangeCheck) {
            validateRange(sensor, value, rule, result.errors);
        }
        
        // Validate rate of change
        if (rule.enableRateCheck && m_previousValues.contains(sensor)) {
            validateRateOfChange(sensor, value, rule, result.errors);
        }
        
        // Check if this is a critical error
        if (!result.errors.isEmpty() && rule.isCritical) {
            result.criticalErrors = result.errors;
        }
    }
    
    // Update statistics
    result.errorCount = result.errors.size();
    result.warningCount = result.warnings.size();
    result.isValid = result.errors.isEmpty();
    result.summary = formatValidationSummary(result);
    
    updateValidationStatistics(result);
    updatePreviousValues(TestData()); // Update with current sensor value
    
    emit validationCompleted(result);
    
    if (!result.isValid) {
        qCWarning(dataValidatorLog) << "Sensor validation failed for" << sensor << ":" << result.errors;
    }
    
    return result;
}

DataValidator::ValidationResult DataValidator::validateTestData(const TestData& data)
{
    ValidationResult result;
    result.validationTime = QDateTime::currentDateTime();
    
    if (!m_validationEnabled) {
        result.isValid = true;
        result.summary = "Validation disabled";
        return result;
    }
    
    QMutexLocker locker(&m_mutex);

    // Declare variables that need to be accessible outside the try block
    QStringList safetyViolations;

    try {
        // Basic data integrity check
        checkDataIntegrity(data, result.errors);

        // Validate individual sensor readings
        QStringList parameters = data.parameterNames();
        for (const QString& param : parameters) {
            if (hasValidationRule(param)) {
                ValidationRule rule = getValidationRule(param);
                double value = data.parameter(param).toDouble();

                // Range validation
                if (rule.enableRangeCheck) {
                    validateRange(param, value, rule, result.errors);
                }

                // Rate of change validation
                if (rule.enableRateCheck) {
                    validateRateOfChange(param, value, rule, result.errors);
                }

                // Correlation validation
                if (rule.enableCorrelationCheck) {
                    validateCorrelation(data, rule, result.errors);
                }

                // Check for critical errors
                if (!result.errors.isEmpty() && rule.isCritical) {
                    result.criticalErrors.append(result.errors.last());
                }
            }
        }

        // Safety limits check
        if (checkSafetyLimits(data, safetyViolations)) {
            result.errors.append(safetyViolations);
            result.criticalErrors.append(safetyViolations); // Safety violations are always critical
        }

        // Sensor correlation check
        QStringList correlationErrors;
        if (!checkSensorCorrelation(data, correlationErrors)) {
            result.warnings.append(correlationErrors);
        }

    } catch (const std::exception& e) {
        result.errors.append(QString("Validation exception: %1").arg(e.what()));
        qCCritical(dataValidatorLog) << "Exception during validation:" << e.what();
    }
    
    // Finalize result
    result.errorCount = result.errors.size();
    result.warningCount = result.warnings.size();
    result.isValid = result.errors.isEmpty();
    result.summary = formatValidationSummary(result);
    
    updateValidationStatistics(result);
    updatePreviousValues(data);
    
    emit validationCompleted(result);
    
    if (!result.isValid) {
        emit validationFailed(data, result.errors);
        
        if (!result.criticalErrors.isEmpty()) {
            emit criticalValidationFailure(data, result.criticalErrors);
        }
    }
    
    if (!safetyViolations.isEmpty()) {
        emit safetyLimitViolation(data, safetyViolations);
    }
    
    return result;
}

DataValidator::ValidationResult DataValidator::validateTestParameters(const TestConfiguration& config)
{
    ValidationResult result;
    result.validationTime = QDateTime::currentDateTime();
    
    QStringList configErrors = config.validate();
    result.errors = configErrors;
    result.errorCount = configErrors.size();
    result.isValid = configErrors.isEmpty();
    result.summary = result.isValid ? "Test configuration valid" : "Test configuration invalid";
    
    updateValidationStatistics(result);
    emit validationCompleted(result);
    
    return result;
}

bool DataValidator::checkSafetyLimits(const TestData& data, QStringList& violations)
{
    violations.clear();
    
    if (!m_testConfig) {
        return true; // No configuration to check against
    }
    
    // Use test configuration safety checks
    if (!m_testConfig->isPressureSafe(data.pressureCPA())) {
        violations.append(formatValidationError("pressure_cpa", "exceeds safety limit", data.pressureCPA()));
    }
    
    if (!m_testConfig->isPressureSafe(data.pressureCPB())) {
        violations.append(formatValidationError("pressure_cpb", "exceeds safety limit", data.pressureCPB()));
    }
    
    if (!m_testConfig->isTemperatureSafe(data.temperatureFluid())) {
        violations.append(formatValidationError("temperature_fluid", "exceeds safety limit", data.temperatureFluid()));
    }
    
    if (!m_testConfig->isFlowSafe(data.flowRate())) {
        violations.append(formatValidationError("flow_rate", "exceeds safety limit", data.flowRate()));
    }
    
    if (!m_testConfig->isVelocitySafe(data.actuatorVelocity())) {
        violations.append(formatValidationError("actuator_velocity", "exceeds safety limit", data.actuatorVelocity()));
    }
    
    if (!m_testConfig->isForceSafe(data.actuatorForce())) {
        violations.append(formatValidationError("actuator_force", "exceeds safety limit", data.actuatorForce()));
    }
    
    return !violations.isEmpty();
}

bool DataValidator::checkDataIntegrity(const TestData& data, QStringList& errors)
{
    // Basic data validation
    if (!data.isValid()) {
        QStringList dataErrors = data.validate();
        errors.append(dataErrors);
        return false;
    }
    
    // Check for NaN or infinite values
    QStringList parameters = data.parameterNames();
    for (const QString& param : parameters) {
        QVariant value = data.parameter(param);
        if (value.typeId() == QMetaType::Double) {
            double doubleValue = value.toDouble();
            if (qIsNaN(doubleValue)) {
                errors.append(formatValidationError(param, "is NaN", doubleValue));
            } else if (qIsInf(doubleValue)) {
                errors.append(formatValidationError(param, "is infinite", doubleValue));
            }
        }
    }
    
    return errors.isEmpty();
}

bool DataValidator::checkSensorCorrelation(const TestData& data, QStringList& correlationErrors)
{
    correlationErrors.clear();
    
    // Check pressure-force correlation
    double pressureDiff = data.pressureCPA() - data.pressureCPB();
    double force = data.actuatorForce();
    
    // Simplified correlation check
    if (qAbs(pressureDiff) > 10.0) { // Significant pressure difference
        double expectedForceDirection = pressureDiff > 0 ? 1.0 : -1.0;
        double actualForceDirection = force > 0 ? 1.0 : -1.0;
        
        if (expectedForceDirection != actualForceDirection && qAbs(force) > 1000.0) {
            correlationErrors.append("Pressure-force correlation anomaly detected");
        }
    }
    
    // Check flow-velocity correlation
    double flow = data.flowRate();
    double velocity = data.actuatorVelocity();
    
    if (qAbs(flow) > 5.0 && qAbs(velocity) < 1.0) {
        correlationErrors.append("Flow-velocity correlation anomaly detected");
    }
    
    return correlationErrors.isEmpty();
}

void DataValidator::loadDefaultRules()
{
    clearValidationRules();
    
    // Create default hydraulic validation rules
    addValidationRule(createPressureValidationRule("pressure_cpa", 0.0, 300.0));
    addValidationRule(createPressureValidationRule("pressure_cpb", 0.0, 300.0));
    addValidationRule(createPressureValidationRule("pressure_supply", 50.0, 250.0));
    addValidationRule(createPressureValidationRule("pressure_return", 0.0, 20.0));
    
    addValidationRule(createFlowValidationRule("flow_rate", -150.0, 150.0));
    
    addValidationRule(createTemperatureValidationRule("temperature_fluid", 85.0));
    addValidationRule(createTemperatureValidationRule("temperature_ambient", 50.0));
    
    addValidationRule(createPositionValidationRule("actuator_position", 0.0, 500.0));
    addValidationRule(createVelocityValidationRule("actuator_velocity", 250.0));
    addValidationRule(createForceValidationRule("actuator_force", 60000.0));
    
    qCDebug(dataValidatorLog) << "Default validation rules loaded";
}

void DataValidator::createHydraulicValidationRules()
{
    if (!m_testConfig) {
        return;
    }
    
    // Update rules based on test configuration
    auto pressureRange = m_testConfig->pressureRange();
    auto flowRange = m_testConfig->flowRange();
    double maxTemp = m_testConfig->temperatureMax();
    
    // Update pressure rules
    if (hasValidationRule("pressure_cpa")) {
        ValidationRule rule = getValidationRule("pressure_cpa");
        rule.minValue = pressureRange.first;
        rule.maxValue = pressureRange.second;
        addValidationRule(rule);
    }
    
    if (hasValidationRule("pressure_cpb")) {
        ValidationRule rule = getValidationRule("pressure_cpb");
        rule.minValue = pressureRange.first;
        rule.maxValue = pressureRange.second;
        addValidationRule(rule);
    }
    
    // Update flow rules
    if (hasValidationRule("flow_rate")) {
        ValidationRule rule = getValidationRule("flow_rate");
        rule.minValue = flowRange.first;
        rule.maxValue = flowRange.second;
        addValidationRule(rule);
    }
    
    // Update temperature rules
    if (hasValidationRule("temperature_fluid")) {
        ValidationRule rule = getValidationRule("temperature_fluid");
        rule.maxValue = maxTemp;
        addValidationRule(rule);
    }
    
    qCDebug(dataValidatorLog) << "Hydraulic validation rules updated from test configuration";
}

DataValidator::ValidationStatistics DataValidator::getValidationStatistics() const
{
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void DataValidator::resetValidationStatistics()
{
    QMutexLocker locker(&m_mutex);
    m_statistics = ValidationStatistics();
    emit validationStatisticsUpdated(m_statistics);
    
    qCDebug(dataValidatorLog) << "Validation statistics reset";
}

void DataValidator::setValidationEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_validationEnabled = enabled;
    
    qCDebug(dataValidatorLog) << "Validation" << (enabled ? "enabled" : "disabled");
}

bool DataValidator::isValidationEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_validationEnabled;
}

bool DataValidator::validateRange(const QString& parameter, double value, const ValidationRule& rule, QStringList& errors)
{
    if (value < rule.minValue) {
        errors.append(formatValidationError(parameter, 
            QString("below minimum %1").arg(rule.minValue), value));
        return false;
    }
    
    if (value > rule.maxValue) {
        errors.append(formatValidationError(parameter, 
            QString("above maximum %1").arg(rule.maxValue), value));
        return false;
    }
    
    return true;
}

bool DataValidator::validateRateOfChange(const QString& parameter, double value, const ValidationRule& rule, QStringList& errors)
{
    if (!m_previousValues.contains(parameter) || !m_previousTimestamps.contains(parameter)) {
        return true; // No previous value to compare
    }
    
    double previousValue = m_previousValues.value(parameter);
    QDateTime previousTime = m_previousTimestamps.value(parameter);
    QDateTime currentTime = QDateTime::currentDateTime();
    
    double deltaTime = previousTime.msecsTo(currentTime) / 1000.0; // Convert to seconds
    if (deltaTime <= 0.0) {
        return true; // Invalid time delta
    }
    
    double rateOfChange = qAbs(value - previousValue) / deltaTime;
    
    if (rateOfChange > rule.maxRateOfChange) {
        errors.append(formatValidationError(parameter, 
            QString("rate of change %1/s exceeds maximum %2/s").arg(rateOfChange).arg(rule.maxRateOfChange), value));
        return false;
    }
    
    return true;
}

bool DataValidator::validateCorrelation(const TestData& data, const ValidationRule& rule, QStringList& errors)
{
    if (!rule.enableCorrelationCheck || rule.correlatedParameter.isEmpty()) {
        return true; // No correlation check needed
    }

    // Get the main parameter value
    QVariant mainValue = data.parameter(rule.parameterName);
    if (!mainValue.isValid() || mainValue.typeId() != QMetaType::Double) {
        errors.append(formatValidationError(rule.parameterName, "invalid value for correlation check", 0.0));
        return false;
    }

    // Get the correlated parameter value
    QVariant correlatedValue = data.parameter(rule.correlatedParameter);
    if (!correlatedValue.isValid() || correlatedValue.typeId() != QMetaType::Double) {
        errors.append(formatValidationError(rule.correlatedParameter, "invalid correlated value", 0.0));
        return false;
    }

    double mainVal = mainValue.toDouble();
    double corrVal = correlatedValue.toDouble();

    // Calculate expected correlation
    double expectedCorrelation = corrVal * rule.correlationFactor;
    double actualDifference = qAbs(mainVal - expectedCorrelation);
    double tolerance = qAbs(expectedCorrelation * rule.correlationTolerance);

    // Ensure minimum tolerance
    if (tolerance < 0.1) {
        tolerance = 0.1;
    }

    if (actualDifference > tolerance) {
        errors.append(formatValidationError(rule.parameterName,
            QString("correlation with %1 failed: expected %2, got %3 (tolerance: %4)")
                .arg(rule.correlatedParameter)
                .arg(expectedCorrelation, 0, 'f', 2)
                .arg(mainVal, 0, 'f', 2)
                .arg(tolerance, 0, 'f', 2), mainVal));
        return false;
    }

    return true;
}

void DataValidator::updateValidationStatistics(const ValidationResult& result)
{
    m_statistics.totalValidations++;
    m_statistics.lastValidation = result.validationTime;
    
    if (result.isValid) {
        m_statistics.successfulValidations++;
    } else {
        m_statistics.failedValidations++;
        
        if (!result.criticalErrors.isEmpty()) {
            m_statistics.criticalFailures++;
        }
        
        // Update error counts
        for (const QString& error : result.errors) {
            m_statistics.errorCounts[error]++;
        }
    }
    
    // Calculate success rate
    if (m_statistics.totalValidations > 0) {
        m_statistics.successRate = (double(m_statistics.successfulValidations) / m_statistics.totalValidations) * 100.0;
    }
    
    // Emit statistics update periodically
    static int updateCounter = 0;
    if (++updateCounter % 100 == 0) { // Every 100 validations
        emit validationStatisticsUpdated(m_statistics);
    }
}

void DataValidator::updatePreviousValues(const TestData& data)
{
    QDateTime currentTime = QDateTime::currentDateTime();
    
    QStringList parameters = data.parameterNames();
    for (const QString& param : parameters) {
        QVariant value = data.parameter(param);
        if (value.typeId() == QMetaType::Double) {
            m_previousValues[param] = value.toDouble();
            m_previousTimestamps[param] = currentTime;
        }
    }
    
    // Limit history size
    if (m_previousValues.size() > VALIDATION_HISTORY_SIZE) {
        // Remove oldest entries (simplified approach)
        auto it = m_previousValues.begin();
        while (m_previousValues.size() > VALIDATION_HISTORY_SIZE && it != m_previousValues.end()) {
            it = m_previousValues.erase(it);
        }
    }
}

QString DataValidator::formatValidationError(const QString& parameter, const QString& error, double value) const
{
    return QString("%1: %2 (value: %3)").arg(parameter, error).arg(value);
}

QString DataValidator::formatValidationSummary(const ValidationResult& result) const
{
    if (result.isValid) {
        return "Validation passed";
    } else {
        return QString("Validation failed: %1 errors, %2 warnings")
                   .arg(result.errorCount).arg(result.warningCount);
    }
}

DataValidator::ValidationRule DataValidator::createPressureValidationRule(const QString& parameterName, double minPressure, double maxPressure)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = minPressure;
    rule.maxValue = maxPressure;
    rule.maxRateOfChange = 100.0; // bar/s
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = parameterName.contains("supply") || parameterName.contains("cpa") || parameterName.contains("cpb");
    rule.description = QString("Pressure validation for %1").arg(parameterName);
    return rule;
}

DataValidator::ValidationRule DataValidator::createFlowValidationRule(const QString& parameterName, double minFlow, double maxFlow)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = minFlow;
    rule.maxValue = maxFlow;
    rule.maxRateOfChange = 50.0; // L/min/s
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = false;
    rule.description = QString("Flow validation for %1").arg(parameterName);
    return rule;
}

DataValidator::ValidationRule DataValidator::createTemperatureValidationRule(const QString& parameterName, double maxTemperature)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = -10.0; // °C
    rule.maxValue = maxTemperature;
    rule.maxRateOfChange = 5.0; // °C/s
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = parameterName.contains("fluid");
    rule.description = QString("Temperature validation for %1").arg(parameterName);
    return rule;
}

DataValidator::ValidationRule DataValidator::createPositionValidationRule(const QString& parameterName, double minPosition, double maxPosition)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = minPosition;
    rule.maxValue = maxPosition;
    rule.maxRateOfChange = 300.0; // mm/s (velocity limit)
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = true;
    rule.description = QString("Position validation for %1").arg(parameterName);
    return rule;
}

DataValidator::ValidationRule DataValidator::createVelocityValidationRule(const QString& parameterName, double maxVelocity)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = -maxVelocity;
    rule.maxValue = maxVelocity;
    rule.maxRateOfChange = 500.0; // mm/s²
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = true;
    rule.description = QString("Velocity validation for %1").arg(parameterName);
    return rule;
}

DataValidator::ValidationRule DataValidator::createForceValidationRule(const QString& parameterName, double maxForce)
{
    ValidationRule rule;
    rule.parameterName = parameterName;
    rule.minValue = -maxForce;
    rule.maxValue = maxForce;
    rule.maxRateOfChange = 10000.0; // N/s
    rule.enableRangeCheck = true;
    rule.enableRateCheck = true;
    rule.isCritical = true;
    rule.description = QString("Force validation for %1").arg(parameterName);
    return rule;
}

void DataValidator::onTestConfigurationChanged()
{
    createHydraulicValidationRules();
    qCDebug(dataValidatorLog) << "Validation rules updated due to test configuration change";
}
