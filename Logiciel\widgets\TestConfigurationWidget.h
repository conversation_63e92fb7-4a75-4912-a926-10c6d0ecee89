#ifndef TESTCONFIGURATIONWIDGET_H
#define TESTCONFIGURATIONWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QTabWidget>
#include <QFormLayout>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QSlider>
#include <QProgressBar>
#include <QLoggingCategory>
#include <memory>

#include "models/TestConfiguration.h"

Q_DECLARE_LOGGING_CATEGORY(testConfigWidgetLog)

/**
 * @brief Test configuration widget for hydraulic test bench system
 * 
 * This widget provides a comprehensive interface for configuring hydraulic
 * test parameters including pressure ranges, flow limits, safety thresholds,
 * acquisition settings, and execution parameters.
 */
class TestConfigurationWidget : public QWidget
{
    Q_OBJECT
    
public:
    explicit TestConfigurationWidget(QWidget *parent = nullptr);
    virtual ~TestConfigurationWidget() = default;
    
    // Configuration management
    void setTestConfiguration(std::shared_ptr<TestConfiguration> config);
    std::shared_ptr<TestConfiguration> testConfiguration() const;
    
    // UI state management
    void updateFromConfiguration();
    void applyToConfiguration();
    void resetToDefaults();
    void validateConfiguration();
    
    // Preset management
    void loadPreset(const QString& presetName);
    void savePreset(const QString& presetName);
    QStringList availablePresets() const;
    
signals:
    void configurationChanged();
    void configurationValidated(bool isValid, const QStringList& errors);
    void presetLoaded(const QString& presetName);
    void presetSaved(const QString& presetName);
    
private slots:
    // Basic parameter slots
    void onNameChanged();
    void onDescriptionChanged();
    void onPressureRangeChanged();
    void onFlowRangeChanged();
    void onTemperatureMaxChanged();
    void onAcquisitionRateChanged();
    void onTestDurationChanged();
    
    // Safety threshold slots
    void onSafetyThresholdChanged();
    
    // Acquisition config slots
    void onAcquisitionConfigChanged();
    
    // Execution config slots
    void onExecutionConfigChanged();
    
    // Button slots
    void onApplyClicked();
    void onResetClicked();
    void onValidateClicked();
    void onLoadPresetClicked();
    void onSavePresetClicked();
    void onCreatePresetClicked();
    
private:
    // UI setup
    void setupUI();
    void setupBasicTab();
    void setupSafetyTab();
    void setupAcquisitionTab();
    void setupExecutionTab();
    void setupPresetTab();
    void setupButtonPanel();
    
    // UI update methods
    void updateBasicTab();
    void updateSafetyTab();
    void updateAcquisitionTab();
    void updateExecutionTab();
    void updatePresetTab();
    void updateValidationStatus();
    
    // Validation helpers
    QStringList validateBasicParameters() const;
    QStringList validateSafetyThresholds() const;
    QStringList validateAcquisitionConfig() const;
    QStringList validateExecutionConfig() const;
    
    // Preset management
    void loadPresetsFromFile();
    void savePresetsToFile();
    void createDefaultPresets();
    QString getPresetFilePath() const;
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    QTabWidget* m_tabWidget;
    
    // Basic parameters tab
    QWidget* m_basicTab;
    QLineEdit* m_nameEdit;
    QTextEdit* m_descriptionEdit;
    QDoubleSpinBox* m_pressureMinSpinBox;
    QDoubleSpinBox* m_pressureMaxSpinBox;
    QDoubleSpinBox* m_flowMinSpinBox;
    QDoubleSpinBox* m_flowMaxSpinBox;
    QDoubleSpinBox* m_temperatureMaxSpinBox;
    QSpinBox* m_acquisitionRateSpinBox;
    QSpinBox* m_testDurationSpinBox;
    
    // Safety thresholds tab
    QWidget* m_safetyTab;
    QDoubleSpinBox* m_maxPressureSpinBox;
    QDoubleSpinBox* m_maxTemperatureSpinBox;
    QDoubleSpinBox* m_maxFlowSpinBox;
    QDoubleSpinBox* m_maxVelocitySpinBox;
    QDoubleSpinBox* m_maxForceSpinBox;
    QDoubleSpinBox* m_minPressureSpinBox;
    QDoubleSpinBox* m_leakageThresholdSpinBox;
    QSpinBox* m_emergencyStopDelaySpinBox;
    
    // Acquisition configuration tab
    QWidget* m_acquisitionTab;
    QSpinBox* m_sampleRateSpinBox;
    QSpinBox* m_bufferSizeSpinBox;
    QCheckBox* m_enableFilteringCheckBox;
    QDoubleSpinBox* m_filterCutoffSpinBox;
    QCheckBox* m_enableValidationCheckBox;
    QCheckBox* m_enableLoggingCheckBox;
    QComboBox* m_logFormatComboBox;
    
    // Execution configuration tab
    QWidget* m_executionTab;
    QCheckBox* m_autoStartCheckBox;
    QCheckBox* m_autoStopCheckBox;
    QCheckBox* m_pauseOnErrorCheckBox;
    QCheckBox* m_saveResultsCheckBox;
    QCheckBox* m_generateReportCheckBox;
    QComboBox* m_reportFormatComboBox;
    QSpinBox* m_maxRetriesSpinBox;
    QSpinBox* m_retryDelaySpinBox;
    
    // Preset management tab
    QWidget* m_presetTab;
    QComboBox* m_presetComboBox;
    QLineEdit* m_newPresetNameEdit;
    QPushButton* m_loadPresetButton;
    QPushButton* m_savePresetButton;
    QPushButton* m_createPresetButton;
    QPushButton* m_deletePresetButton;
    
    // Button panel
    QWidget* m_buttonPanel;
    QPushButton* m_applyButton;
    QPushButton* m_resetButton;
    QPushButton* m_validateButton;
    QLabel* m_validationStatusLabel;
    QProgressBar* m_validationProgressBar;
    
    // Configuration reference
    std::shared_ptr<TestConfiguration> m_testConfig;
    
    // State management
    bool m_updating;
    QStringList m_validationErrors;
    QMap<QString, TestConfiguration> m_presets;
    
    // Constants
    static const QString PRESETS_FILE_NAME;
    static const QStringList DEFAULT_PRESET_NAMES;
};

#endif // TESTCONFIGURATIONWIDGET_H
