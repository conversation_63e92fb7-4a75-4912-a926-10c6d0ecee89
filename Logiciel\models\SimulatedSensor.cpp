#include "SimulatedSensor.h"
#include <QDebug>
#include <QtMath>

SimulatedSensor::SimulatedSensor(QObject *parent)
    : HardwareInterface(parent)
    , m_updateTimer(new QTimer(this))
    , m_manualMode(false)
    , m_manualValue(0.0)
    , m_driftOffset(0.0)
    , m_randomGenerator(QRandomGenerator::global())
{
    // Default simulation configuration
    m_simConfig.minValue = 0.0;
    m_simConfig.maxValue = 100.0;
    m_simConfig.noiseLevel = 0.01;
    m_simConfig.updateIntervalMs = 100;
    m_simConfig.enableDrift = true;
    m_simConfig.driftRate = 0.001;
    m_simConfig.enableSpikes = false;
    m_simConfig.spikeFrequency = 0.01;
    m_simConfig.spikeAmplitude = 0.1;
    
    // Setup update timer
    m_updateTimer->setSingleShot(false);
    m_updateTimer->setInterval(m_simConfig.updateIntervalMs);
    QObject::connect(m_updateTimer, &QTimer::timeout, this, &SimulatedSensor::updateSimulatedValues);
    
    // Initialize calibration
    m_isCalibrated = true;
    m_lastCalibration = QDateTime::currentDateTime();
}

SimulatedSensor::~SimulatedSensor()
{
    stopSimulation();
}

bool SimulatedSensor::connect()
{
    setConnectionStatus(ConnectionStatus::Connecting);
    
    // Simulate connection delay
    QTimer::singleShot(500, this, [this]() {
        setConnectionStatus(ConnectionStatus::Connected);
        startSimulation();
    });
    
    return true;
}

void SimulatedSensor::disconnect()
{
    stopSimulation();
    setConnectionStatus(ConnectionStatus::Disconnected);
}

bool SimulatedSensor::isConnected() const
{
    return m_connectionStatus == ConnectionStatus::Connected;
}

HardwareInterface::ConnectionStatus SimulatedSensor::connectionStatus() const
{
    return m_connectionStatus;
}

QVariant SimulatedSensor::readValue(const QString& parameter)
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return QVariant();
    }
    
    if (m_currentValues.contains(parameter)) {
        return m_currentValues.value(parameter);
    }
    
    setLastError(QString("Unknown parameter: %1").arg(parameter));
    return QVariant();
}

bool SimulatedSensor::writeValue(const QString& parameter, const QVariant& value)
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }
    
    // For sensors, writing usually means setting target values or configuration
    bool ok;
    double doubleValue = value.toDouble(&ok);
    if (!ok) {
        setLastError("Invalid value type for parameter");
        return false;
    }
    
    if (parameter == "target" || parameter == getPrimaryParameter()) {
        m_targetValues[getPrimaryParameter()] = clampValue(doubleValue);
        return true;
    }
    
    setLastError(QString("Parameter %1 is read-only").arg(parameter));
    return false;
}

QMap<QString, QVariant> SimulatedSensor::readAllValues()
{
    QMap<QString, QVariant> result;
    
    if (!isConnected()) {
        setLastError("Device not connected");
        return result;
    }
    
    for (auto it = m_currentValues.begin(); it != m_currentValues.end(); ++it) {
        result[it.key()] = it.value();
    }
    
    return result;
}

bool SimulatedSensor::configure(const QMap<QString, QVariant>& config)
{
    m_configuration = config;
    
    // Update simulation config from provided configuration
    if (config.contains("minValue")) {
        m_simConfig.minValue = config.value("minValue").toDouble();
    }
    if (config.contains("maxValue")) {
        m_simConfig.maxValue = config.value("maxValue").toDouble();
    }
    if (config.contains("noiseLevel")) {
        m_simConfig.noiseLevel = config.value("noiseLevel").toDouble();
    }
    if (config.contains("updateInterval")) {
        m_simConfig.updateIntervalMs = config.value("updateInterval").toInt();
        m_updateTimer->setInterval(m_simConfig.updateIntervalMs);
    }
    
    return true;
}

QMap<QString, QVariant> SimulatedSensor::getConfiguration() const
{
    QMap<QString, QVariant> config = m_configuration;
    
    // Add simulation-specific configuration
    config["minValue"] = m_simConfig.minValue;
    config["maxValue"] = m_simConfig.maxValue;
    config["noiseLevel"] = m_simConfig.noiseLevel;
    config["updateInterval"] = m_simConfig.updateIntervalMs;
    config["isSimulated"] = true;
    config["manualMode"] = m_manualMode;
    
    return config;
}

bool SimulatedSensor::calibrate()
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }
    
    // Simulate calibration process
    m_lastCalibration = QDateTime::currentDateTime();
    m_isCalibrated = true;
    m_driftOffset = 0.0; // Reset drift
    
    return true;
}

bool SimulatedSensor::isCalibrated() const
{
    return m_isCalibrated;
}

QDateTime SimulatedSensor::lastCalibrationDate() const
{
    return m_lastCalibration;
}

bool SimulatedSensor::performSelfTest()
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }
    
    // Simulate self-test
    bool success = m_randomGenerator->bounded(100) > 5; // 95% success rate
    
    if (!success) {
        setLastError("Self-test failed: Simulated hardware error");
    }
    
    emit selfTestCompleted(success);
    return success;
}

QString SimulatedSensor::getLastError() const
{
    return m_lastError;
}

QMap<QString, QVariant> SimulatedSensor::getDiagnosticInfo() const
{
    QMap<QString, QVariant> info;
    
    info["deviceType"] = "Simulated Sensor";
    info["connectionStatus"] = static_cast<int>(m_connectionStatus);
    info["isCalibrated"] = m_isCalibrated;
    info["lastCalibration"] = m_lastCalibration;
    info["simulationRunning"] = isSimulationRunning();
    info["manualMode"] = m_manualMode;
    info["driftOffset"] = m_driftOffset;
    info["updateInterval"] = m_simConfig.updateIntervalMs;
    
    if (isSimulationRunning()) {
        qint64 runtimeMs = m_simulationStartTime.msecsTo(QDateTime::currentDateTime());
        info["runtimeSeconds"] = runtimeMs / 1000.0;
    }
    
    return info;
}

void SimulatedSensor::setSimulationConfig(const SimulationConfig& config)
{
    m_simConfig = config;
    m_updateTimer->setInterval(config.updateIntervalMs);
}

void SimulatedSensor::setManualValue(double value)
{
    m_manualValue = clampValue(value);
    if (m_manualMode) {
        QString primaryParam = getPrimaryParameter();
        m_currentValues[primaryParam] = m_manualValue;
        emitDataReceived(primaryParam, m_manualValue);
    }
}

void SimulatedSensor::enableManualMode(bool enabled)
{
    if (m_manualMode != enabled) {
        m_manualMode = enabled;
        emit manualModeChanged(enabled);
        
        if (enabled) {
            // Set current value to manual value
            setManualValue(m_manualValue);
        }
    }
}

void SimulatedSensor::startSimulation()
{
    if (!m_updateTimer->isActive()) {
        m_simulationStartTime = QDateTime::currentDateTime();
        updateTargetValues();
        m_updateTimer->start();
        emit simulationStarted();
    }
}

void SimulatedSensor::stopSimulation()
{
    if (m_updateTimer->isActive()) {
        m_updateTimer->stop();
        emit simulationStopped();
    }
}

bool SimulatedSensor::isSimulationRunning() const
{
    return m_updateTimer->isActive();
}

void SimulatedSensor::updateSimulatedValues()
{
    if (m_manualMode) {
        return; // Don't update in manual mode
    }
    
    // Generate new realistic value
    double newValue = generateRealisticValue();
    
    // Apply noise, drift, and spikes
    newValue = addNoise(newValue);
    newValue = addDrift(newValue);
    newValue = addSpikes(newValue);
    
    // Smooth transition from current to new value
    QString primaryParam = getPrimaryParameter();
    double currentValue = m_currentValues.value(primaryParam, newValue);
    double smoothedValue = smoothTransition(currentValue, newValue);
    
    // Clamp to valid range
    smoothedValue = clampValue(smoothedValue);
    
    // Update current value
    m_currentValues[primaryParam] = smoothedValue;
    
    // Emit data received signal
    emitDataReceived(primaryParam, smoothedValue);
}

double SimulatedSensor::addNoise(double baseValue)
{
    if (m_simConfig.noiseLevel <= 0.0) {
        return baseValue;
    }
    
    double range = m_simConfig.maxValue - m_simConfig.minValue;
    double noiseAmplitude = range * m_simConfig.noiseLevel;
    double noise = (m_randomGenerator->generateDouble() - 0.5) * 2.0 * noiseAmplitude;
    
    return baseValue + noise;
}

double SimulatedSensor::addDrift(double baseValue)
{
    if (!m_simConfig.enableDrift) {
        return baseValue;
    }
    
    // Update drift offset
    double deltaTime = m_simConfig.updateIntervalMs / 1000.0; // Convert to seconds
    double driftChange = (m_randomGenerator->generateDouble() - 0.5) * 2.0 * m_simConfig.driftRate * deltaTime;
    m_driftOffset += driftChange;
    
    // Limit drift to reasonable bounds
    double maxDrift = (m_simConfig.maxValue - m_simConfig.minValue) * 0.05; // 5% of range
    m_driftOffset = qBound(-maxDrift, m_driftOffset, maxDrift);
    
    return baseValue + m_driftOffset;
}

double SimulatedSensor::addSpikes(double baseValue)
{
    if (!m_simConfig.enableSpikes) {
        return baseValue;
    }
    
    if (m_randomGenerator->generateDouble() < m_simConfig.spikeFrequency) {
        double range = m_simConfig.maxValue - m_simConfig.minValue;
        double spikeAmplitude = range * m_simConfig.spikeAmplitude;
        double spike = (m_randomGenerator->generateDouble() - 0.5) * 2.0 * spikeAmplitude;
        return baseValue + spike;
    }
    
    return baseValue;
}

double SimulatedSensor::smoothTransition(double currentValue, double targetValue, double factor)
{
    return currentValue + (targetValue - currentValue) * factor;
}

double SimulatedSensor::clampValue(double value) const
{
    return qBound(m_simConfig.minValue, value, m_simConfig.maxValue);
}

double SimulatedSensor::normalizeValue(double value) const
{
    double range = m_simConfig.maxValue - m_simConfig.minValue;
    if (range == 0.0) return 0.0;
    return (value - m_simConfig.minValue) / range;
}

void SimulatedSensor::updateTargetValues()
{
    // Initialize target values if not set
    QStringList params = getAllParameters();
    for (const QString& param : params) {
        if (!m_targetValues.contains(param)) {
            m_targetValues[param] = (m_simConfig.minValue + m_simConfig.maxValue) / 2.0;
        }
        if (!m_currentValues.contains(param)) {
            m_currentValues[param] = m_targetValues[param];
        }
    }
}
