#ifndef AFFAIRE_H
#define AFFAIRE_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(affaireModelLog)

/**
 * @brief Business case model for hydraulic test bench system
 * 
 * This class represents a business case (affaire) in the hydraulic test system.
 * Each affaire contains multiple test cases (essais) and represents a complete
 * testing project for a specific client or internal requirement.
 */
class Affaire : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(QString numero READ numero WRITE setNumero NOTIFY numeroChanged)
    Q_PROPERTY(QString client READ client WRITE setClient NOTIFY clientChanged)
    Q_PROPERTY(QString description READ description WRITE setDescription NOTIFY descriptionChanged)
    Q_PROPERTY(QString statut READ statut WRITE setStatut NOTIFY statutChanged)
    Q_PROPERTY(QDateTime dateCreation READ dateCreation WRITE setDateCreation NOTIFY dateCreationChanged)
    
public:
    /**
     * @brief Status values for business cases
     */
    enum class Status {
        Draft,          ///< Initial creation, not yet started
        InProgress,     ///< Tests are being executed
        OnHold,         ///< Temporarily suspended
        Completed,      ///< All tests completed successfully
        Cancelled,      ///< Project cancelled
        Archived        ///< Completed and archived
    };
    Q_ENUM(Status)
    
    explicit Affaire(QObject *parent = nullptr);
    Affaire(const Affaire& other);
    Affaire& operator=(const Affaire& other);
    virtual ~Affaire() = default;
    
    // Property accessors
    int id() const { return m_id; }
    void setId(int id);
    
    QString numero() const { return m_numero; }
    void setNumero(const QString& numero);
    
    QString client() const { return m_client; }
    void setClient(const QString& client);
    
    QString description() const { return m_description; }
    void setDescription(const QString& description);
    
    QString statut() const { return m_statut; }
    void setStatut(const QString& statut);
    
    QDateTime dateCreation() const { return m_dateCreation; }
    void setDateCreation(const QDateTime& dateCreation);
    
    // Status management
    Status affaireStatus() const;
    void setAffaireStatus(Status status);
    QString statusDisplayName() const;
    static QString statusToString(Status status);
    static Status statusFromString(const QString& statusStr);
    
    // Business logic
    bool canBeModified() const;
    bool canBeDeleted() const;
    bool canAddTests() const;
    bool isActive() const;
    bool isCompleted() const;
    
    // Validation
    bool isValid() const;
    QStringList validate() const;
    bool isNumeroValid() const;
    bool isClientValid() const;
    bool isDescriptionValid() const;
    bool isStatutValid() const;
    
    // JSON serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    QJsonObject toJsonPartial() const; // For API updates
    
    // Utility methods
    QString displayName() const;
    QString shortDescription() const;
    int daysSinceCreation() const;
    QString formattedCreationDate() const;
    
    // Operators
    bool operator==(const Affaire& other) const;
    bool operator!=(const Affaire& other) const { return !(*this == other); }
    
    // Static factory methods
    static Affaire createFromJson(const QJsonObject& json);
    static Affaire createNew(const QString& numero, const QString& client, const QString& description);
    static QStringList getAllStatuses();
    static QString generateNumero(); // Generate unique affaire number
    
signals:
    void idChanged(int id);
    void numeroChanged(const QString& numero);
    void clientChanged(const QString& client);
    void descriptionChanged(const QString& description);
    void statutChanged(const QString& statut);
    void dateCreationChanged(const QDateTime& dateCreation);
    void affaireDataChanged();
    void validationChanged(bool isValid);
    void statusChanged(Status oldStatus, Status newStatus);
    
private:
    int m_id;
    QString m_numero;
    QString m_client;
    QString m_description;
    QString m_statut;
    QDateTime m_dateCreation;
    
    void connectSignals();
    void validateAndEmitSignals();
    bool isValidNumero(const QString& numero) const;
    bool isValidClient(const QString& client) const;
    bool isValidDescription(const QString& description) const;
    
    // Constants
    static const int MIN_NUMERO_LENGTH = 3;
    static const int MAX_NUMERO_LENGTH = 20;
    static const int MIN_CLIENT_LENGTH = 2;
    static const int MAX_CLIENT_LENGTH = 100;
    static const int MIN_DESCRIPTION_LENGTH = 10;
    static const int MAX_DESCRIPTION_LENGTH = 1000;
};

#endif // AFFAIRE_H
