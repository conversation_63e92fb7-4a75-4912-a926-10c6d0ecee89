#ifndef TESTCONTROLPANEL_H
#define TESTCONTROLPANEL_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QProgressBar>
#include <QLabel>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QTextEdit>
#include <QTimer>
#include <QLoggingCategory>
#include <memory>

#include "utils/TestSessionManager.h"
#include "models/TestConfiguration.h"
#include "models/Essai.h"

Q_DECLARE_LOGGING_CATEGORY(testControlPanelLog)

/**
 * @brief Main test control interface for hydraulic test bench system
 * 
 * This widget provides the primary interface for controlling test execution,
 * including start/stop/pause controls, progress monitoring, status display,
 * and test configuration management. It connects to TestSessionManager signals
 * for real-time updates.
 */
class TestControlPanel : public QWidget
{
    Q_OBJECT
    
public:
    explicit TestControlPanel(QWidget *parent = nullptr);
    virtual ~TestControlPanel() = default;
    
    // Session manager integration
    void setTestSessionManager(std::shared_ptr<TestSessionManager> sessionManager);
    std::shared_ptr<TestSessionManager> testSessionManager() const;
    
    // Test configuration
    void setTestConfiguration(std::shared_ptr<TestConfiguration> config);
    std::shared_ptr<TestConfiguration> testConfiguration() const;
    
    void setCurrentEssai(std::shared_ptr<Essai> essai);
    std::shared_ptr<Essai> currentEssai() const;
    
    // UI state management
    void updateControlsState();
    void resetControls();
    void enableControls(bool enabled);
    
signals:
    void testStartRequested();
    void testStopRequested();
    void testPauseRequested();
    void testResumeRequested();
    void testCancelRequested();
    void configurationChangeRequested();
    void emergencyStopRequested();
    
private slots:
    // Control button slots
    void onStartButtonClicked();
    void onStopButtonClicked();
    void onPauseResumeButtonClicked();
    void onCancelButtonClicked();
    void onEmergencyStopClicked();
    void onConfigureButtonClicked();
    void onResetButtonClicked();
    
    // Session manager slots
    void onSessionStarted();
    void onSessionStopped();
    void onSessionPaused();
    void onSessionResumed();
    void onSessionCompleted();
    void onSessionFailed(const QString& error);
    void onSessionCancelled();
    void onSessionStatusChanged(TestSessionManager::SessionStatus status);
    void onSessionProgressChanged(double percentage);
    void onSessionPhaseChanged(const QString& phase);
    void onSessionStatisticsUpdated(const TestSessionManager::SessionStatistics& stats);
    void onSessionError(const QString& error);
    void onCriticalSessionError(const QString& error);
    
    // UI update slots
    void onUpdateTimer();
    void onTestTypeChanged();
    void onTestDurationChanged();
    
private:
    // UI setup
    void setupUI();
    void setupControlButtons();
    void setupProgressSection();
    void setupStatusSection();
    void setupQuickConfigSection();
    void setupStatisticsSection();
    
    // UI update methods
    void updateProgressDisplay();
    void updateStatusDisplay();
    void updateStatisticsDisplay();
    void updateButtonStates();
    void updateQuickConfigDisplay();
    
    // Utility methods
    QString formatDuration(double seconds) const;
    QString formatDataRate(double rate) const;
    QString formatSessionStatus(TestSessionManager::SessionStatus status) const;
    QColor getStatusColor(TestSessionManager::SessionStatus status) const;
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    
    // Control buttons section
    QGroupBox* m_controlGroup;
    QPushButton* m_startButton;
    QPushButton* m_stopButton;
    QPushButton* m_pauseResumeButton;
    QPushButton* m_cancelButton;
    QPushButton* m_emergencyStopButton;
    QPushButton* m_configureButton;
    QPushButton* m_resetButton;
    
    // Progress section
    QGroupBox* m_progressGroup;
    QProgressBar* m_progressBar;
    QLabel* m_progressLabel;
    QLabel* m_phaseLabel;
    QLabel* m_timeElapsedLabel;
    QLabel* m_timeRemainingLabel;
    
    // Status section
    QGroupBox* m_statusGroup;
    QLabel* m_statusLabel;
    QLabel* m_statusIcon;
    QTextEdit* m_statusMessages;
    QPushButton* m_clearMessagesButton;
    
    // Quick configuration section
    QGroupBox* m_quickConfigGroup;
    QComboBox* m_testTypeComboBox;
    QSpinBox* m_testDurationSpinBox;
    QDoubleSpinBox* m_pressureTargetSpinBox;
    QDoubleSpinBox* m_flowTargetSpinBox;
    QPushButton* m_applyConfigButton;
    
    // Statistics section
    QGroupBox* m_statisticsGroup;
    QLabel* m_totalDataPointsLabel;
    QLabel* m_validDataPointsLabel;
    QLabel* m_dataRateLabel;
    QLabel* m_errorsLabel;
    QLabel* m_sessionTimeLabel;
    
    // Component references
    std::shared_ptr<TestSessionManager> m_sessionManager;
    std::shared_ptr<TestConfiguration> m_testConfig;
    std::shared_ptr<Essai> m_currentEssai;
    
    // UI state
    TestSessionManager::SessionStatus m_currentStatus;
    TestSessionManager::SessionStatistics m_currentStats;
    QTimer* m_updateTimer;
    QDateTime m_sessionStartTime;
    
    // Constants
    static const int UPDATE_INTERVAL_MS = 100;
    static const int MAX_STATUS_MESSAGES = 100;
};

#endif // TESTCONTROLPANEL_H
