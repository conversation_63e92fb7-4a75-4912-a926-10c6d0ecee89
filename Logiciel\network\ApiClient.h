#ifndef APICLIENT_H
#define APICLIENT_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>
#include <QUrlQuery>
#include <functional>

#include "models/ApiResponse.h"
#include "models/NetworkError.h"
#include "AuthManager.h"

/**
 * @brief Main REST API client for communicating with the PHP backend
 *
 * This class provides a high-level interface for making HTTP requests
 * to the hydraulic test bench web server API.
 */
class ApiClient : public QObject
{
    Q_OBJECT

public:
    explicit ApiClient(QObject *parent = nullptr);
    ~ApiClient();

    // Authentication
    AuthManager* authManager() const { return m_authManager; }
    bool isAuthenticated() const;

    // Configuration
    void setBaseUrl(const QString& baseUrl);
    QString baseUrl() const { return m_baseUrl; }
    void setTimeout(int timeoutMs) { m_timeoutMs = timeoutMs; }
    int timeout() const { return m_timeoutMs; }

    // Authentication API
    void login(const QString& username, const QString& password);
    void logout();

    // User API
    void getUsers();
    void getUserById(int id);

    // Business cases (Affaires) API
    void getAffaires();
    void getAffaireById(int id);
    void getAffairesByStatus(const QString& status);
    void createAffaire(const QJsonObject& affaireData);
    void updateAffaire(int id, const QJsonObject& affaireData);
    void deleteAffaire(int id);

    // Test cases (Essais) API
    void getEssais();
    void getEssaiById(int id);
    void getEssaisByAffaire(int affaireId);
    void getEssaisByStatus(const QString& status);
    void createEssai(const QJsonObject& essaiData);
    void updateEssai(int id, const QJsonObject& essaiData);
    void deleteEssai(int id);

    // Curve data API
    void getCourbes();
    void getCourbeById(int id);
    void getCourbesByEssai(int essaiId);
    void createCourbe(const QJsonObject& courbeData);
    void updateCourbe(int id, const QJsonObject& courbeData);
    void deleteCourbe(int id);

    // Process verbal (PV) API
    void getPVs();
    void getPVById(int id);
    void getPVsByAffaire(int affaireId);
    void createPV(const QJsonObject& pvData);
    void updatePV(int id, const QJsonObject& pvData);
    void deletePV(int id);

    // Performance/efficiency API
    void getRendements();
    void getRendementByEssai(int essaiId);
    void createRendement(const QJsonObject& rendementData);

    // Generic HTTP methods
    void get(const QString& endpoint, const QUrlQuery& params = QUrlQuery());
    void post(const QString& endpoint, const QJsonObject& data);
    void put(const QString& endpoint, const QJsonObject& data);
    void deleteResource(const QString& endpoint, const QUrlQuery& params = QUrlQuery());

signals:
    // Authentication signals
    void loginSuccessful(const AuthManager::UserInfo& user);
    void loginFailed(const NetworkError& error);
    void loggedOut();

    // Generic response signals
    void responseReceived(const QString& endpoint, const JsonResponse& response);
    void errorOccurred(const QString& endpoint, const NetworkError& error);

    // Specific API response signals
    void usersReceived(const ArrayResponse& response);
    void userReceived(const JsonResponse& response);
    void affairesReceived(const ArrayResponse& response);
    void affaireReceived(const JsonResponse& response);
    void essaisReceived(const ArrayResponse& response);
    void essaiReceived(const JsonResponse& response);
    void courbesReceived(const ArrayResponse& response);
    void courbeReceived(const JsonResponse& response);
    void pvsReceived(const ArrayResponse& response);
    void pvReceived(const JsonResponse& response);
    void rendementsReceived(const ArrayResponse& response);
    void rendementReceived(const JsonResponse& response);

    // CRUD operation signals
    void resourceCreated(const QString& resourceType, const JsonResponse& response);
    void resourceUpdated(const QString& resourceType, const JsonResponse& response);
    void resourceDeleted(const QString& resourceType, const JsonResponse& response);

private slots:
    void onNetworkReplyFinished();
    void onRequestTimeout();
    void onAuthenticationRequired();

private:
    QNetworkAccessManager* m_networkManager;
    AuthManager* m_authManager;
    QString m_baseUrl;
    int m_timeoutMs;
    QMap<QNetworkReply*, QTimer*> m_timeoutTimers;
    QMap<QNetworkReply*, QString> m_replyEndpoints;

    // Request building
    QNetworkRequest buildRequest(const QString& endpoint, const QUrlQuery& params = QUrlQuery());
    void addAuthenticationHeader(QNetworkRequest& request);
    void addCommonHeaders(QNetworkRequest& request);

    // Response handling
    void handleResponse(QNetworkReply* reply, const QString& endpoint);
    JsonResponse parseJsonResponse(const QByteArray& data);
    ArrayResponse parseArrayResponse(const QByteArray& data);

    // Request management
    void setupTimeoutTimer(QNetworkReply* reply);
    void cleanupReply(QNetworkReply* reply);

    // URL building
    QString buildUrl(const QString& endpoint, const QUrlQuery& params = QUrlQuery());

    // Signal emission
    void emitSpecificSignals(const QString& endpoint, const JsonResponse& response, const QByteArray& data);
    void emitSpecificArraySignals(const QString& endpoint, const ArrayResponse& response);

    static const int DEFAULT_TIMEOUT_MS = 30000; // 30 seconds
};

#endif // APICLIENT_H
