---
sidebar_position: 4
title: Restauration Sécurisée de Backups
description: Guide pour la restauration sécurisée des sauvegardes avec protection contre les conflits
---

# Restauration Sécurisée de Backups

## Vue d'ensemble

La restauration sécurisée de backups résout le problème des conflits de structure lors de la restauration de sauvegardes. Cette fonctionnalité garantit qu'en cas d'erreur, la base de données peut être restaurée à son état précédent.

## Problème résolu

### Problème initial
- La restauration directe de backups causait des conflits lorsque la structure de base de données existante était incompatible
- Les erreurs de restauration pouvaient laisser la base dans un état corrompu
- Aucun mécanisme de rollback automatique n'était disponible

### Solution implémentée
La restauration sécurisée suit un processus en 4 étapes :

1. **Backup temporaire** : Création automatique d'une sauvegarde de l'état actuel
2. **Suppression complète** : Suppression de toutes les tables dans l'ordre correct
3. **Restauration** : Application de la backup sélectionnée
4. **Rollback automatique** : En cas d'erreur, restauration de la backup temporaire

## Utilisation

### Interface Web

#### Restauration Simple (Existante)
- Bouton "Restaurer" : Restauration directe sans protection
- Risque de conflits de structure
- Plus rapide mais moins sûre

#### Restauration Sécurisée (Nouvelle)
- Bouton "Restaurer (Sécurisé)" : Restauration avec protection
- Backup temporaire automatique
- Rollback en cas d'erreur
- Plus lente mais sûre

### API REST

#### Endpoint de restauration sécurisée
```http
POST /api/backup
Content-Type: application/json

{
    "action": "restore_safe",
    "backup_file": "/path/to/backup.sql",
    "confirmed": true
}
```

#### Réponse en cas de succès
```json
{
    "success": true,
    "message": "Base de données restaurée avec succès (restauration sécurisée)"
}
```

#### Réponse en cas d'erreur avec rollback
```json
{
    "success": false,
    "message": "Erreur lors de la restauration: [détails]. La base de données a été restaurée à son état précédent."
}
```

## Fonctions techniques

### `Backup::dropAllTables()`
Supprime toutes les tables dans l'ordre correct en respectant les contraintes de clés étrangères.

**Tables supprimées dans l'ordre :**
1. `pv_attachments`
2. `affaires_tags`
3. `affaires_historique`
4. `rappels`
5. `rendements`
6. `courbes`
7. `pv`
8. `essais`
9. `affaires`
10. `tags`
11. `modeles_essais`
12. `users`

### `Backup::restoreBackupSafe()`
Effectue une restauration sécurisée avec les étapes suivantes :

1. **Validation** : Vérification des paramètres et de l'existence du fichier
2. **Backup temporaire** : Création d'une sauvegarde avec timestamp
3. **Suppression** : Appel de `dropAllTables()`
4. **Restauration** : Application de la backup sélectionnée
5. **Gestion d'erreur** : Rollback automatique si nécessaire
6. **Nettoyage** : Suppression de la backup temporaire en cas de succès

## Gestion des erreurs

### Types d'erreurs gérées

#### Erreur de backup temporaire
```
"Impossible de créer la backup temporaire: [détails]"
```
→ Arrêt immédiat, aucune modification

#### Erreur de suppression des tables
```
"Erreur lors de la suppression des tables: [détails]. Backup temporaire restaurée."
```
→ Rollback automatique

#### Erreur de restauration
```
"Erreur lors de la restauration: [détails]. La base de données a été restaurée à son état précédent."
```
→ Rollback automatique

#### Erreur critique de rollback
```
"ERREUR CRITIQUE: Impossible de restaurer la backup temporaire: [détails]"
```
→ Intervention manuelle requise

## Bonnes pratiques

### Pour les contrôleurs

1. **Utiliser la restauration sécurisée** pour les environnements de production
2. **Vérifier l'espace disque** avant la restauration (backup temporaire requise)
3. **Planifier les restaurations** pendant les heures de faible activité
4. **Conserver les logs** pour le debugging en cas de problème

### Surveillance

- Surveiller l'espace disque dans `/backups/`
- Vérifier les logs d'erreur après chaque restauration
- Tester régulièrement la procédure de rollback

## Limitations

- **Espace disque** : Nécessite l'espace pour une backup temporaire supplémentaire
- **Temps d'exécution** : Plus lent que la restauration simple
- **Accès concurrent** : La base est inaccessible pendant la restauration

## Dépannage

### Backup temporaire non supprimée
Les backups temporaires sont nommées : `temp_backup_before_restore_YYYY-MM-DD_HH-MM-SS.sql`

Si une backup temporaire reste après une erreur, elle peut être utilisée pour une restauration manuelle.

### Base corrompue après erreur critique
En cas d'erreur critique de rollback :
1. Identifier la backup temporaire la plus récente
2. Utiliser la restauration simple avec cette backup
3. Vérifier l'intégrité des données

### Performance lente
- Vérifier l'espace disque disponible
- Optimiser la taille des backups
- Utiliser la restauration simple si la sécurité n'est pas critique
