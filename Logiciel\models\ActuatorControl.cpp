#include "ActuatorControl.h"
#include <QDebug>
#include <QtMath>

// Default actuator parameters
const double ActuatorControl::DEFAULT_STROKE_LENGTH = 500.0; // mm
const double ActuatorControl::DEFAULT_CYLINDER_DIAMETER = 80.0; // mm
const double ActuatorControl::DEFAULT_MAX_VELOCITY = 200.0; // mm/s
const double ActuatorControl::DEFAULT_MAX_FORCE = 50000.0; // N
const double ActuatorControl::UPDATE_INTERVAL_MS = 50.0; // ms

ActuatorControl::ActuatorControl(QObject *parent)
    : HardwareInterface(parent)
    , m_updateTimer(new QTimer(this))
    , m_testTimer(new QTimer(this))
    , m_controlMode(ControlMode::Disabled)
    , m_actuatorState(ActuatorState::Stopped)
    , m_currentPosition(0.0)
    , m_currentVelocity(0.0)
    , m_currentForce(0.0)
    , m_targetPosition(0.0)
    , m_targetVelocity(0.0)
    , m_targetForce(0.0)
    , m_minPosition(0.0)
    , m_maxPosition(DEFAULT_STROKE_LENGTH)
    , m_maxVelocity(DEFAULT_MAX_VELOCITY)
    , m_maxForce(DEFAULT_MAX_FORCE)
    , m_positionTolerance(1.0) // mm
    , m_velocityTolerance(5.0) // mm/s
    , m_forceTolerance(100.0) // N
    , m_currentTestMode(TestMode::None)
    , m_testStartPos(0.0)
    , m_testEndPos(0.0)
    , m_testVelocity(50.0)
    , m_testForce(0.0)
    , m_testCycles(1)
    , m_currentTestCycle(0)
    , m_testDuration(0)
    , m_testDirection(true)
    , m_emergencyStop(false)
    , m_actuatorLength(DEFAULT_STROKE_LENGTH)
    , m_actuatorDiameter(DEFAULT_CYLINDER_DIAMETER)
    , m_systemPressure(200.0) // bar
{
    // Setup update timer
    m_updateTimer->setSingleShot(false);
    m_updateTimer->setInterval(UPDATE_INTERVAL_MS);
    QObject::connect(m_updateTimer, &QTimer::timeout, this, &ActuatorControl::updateActuator);

    // Setup test timer
    m_testTimer->setSingleShot(true);
    QObject::connect(m_testTimer, &QTimer::timeout, this, &ActuatorControl::onTestTimer);

    // Initialize calibration
    m_isCalibrated = true;
    m_lastCalibration = QDateTime::currentDateTime();
}

bool ActuatorControl::connect()
{
    setConnectionStatus(ConnectionStatus::Connecting);

    // Simulate connection delay
    QTimer::singleShot(1000, this, [this]() {
        setConnectionStatus(ConnectionStatus::Connected);
        m_updateTimer->start();
        setControlMode(ControlMode::Manual);
    });

    return true;
}

void ActuatorControl::disconnect()
{
    m_updateTimer->stop();
    setControlMode(ControlMode::Disabled);
    setConnectionStatus(ConnectionStatus::Disconnected);
}

bool ActuatorControl::isConnected() const
{
    return m_connectionStatus == ConnectionStatus::Connected;
}

HardwareInterface::ConnectionStatus ActuatorControl::connectionStatus() const
{
    return m_connectionStatus;
}

QString ActuatorControl::deviceName() const
{
    return "Hydraulic Actuator Control";
}

QString ActuatorControl::deviceVersion() const
{
    return "Simulated v1.0";
}

HardwareInterface::DeviceType ActuatorControl::deviceType() const
{
    return DeviceType::ActuatorControl;
}

QVariant ActuatorControl::readValue(const QString& parameter)
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return QVariant();
    }

    if (parameter == "position") {
        return m_currentPosition;
    } else if (parameter == "velocity") {
        return m_currentVelocity;
    } else if (parameter == "force") {
        return m_currentForce;
    } else if (parameter == "state") {
        return static_cast<int>(m_actuatorState);
    } else if (parameter == "control_mode") {
        return static_cast<int>(m_controlMode);
    } else if (parameter == "emergency_stop") {
        return m_emergencyStop;
    }

    setLastError(QString("Unknown parameter: %1").arg(parameter));
    return QVariant();
}

bool ActuatorControl::writeValue(const QString& parameter, const QVariant& value)
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }

    if (m_emergencyStop && parameter != "emergency_reset") {
        setLastError("Emergency stop active - reset required");
        return false;
    }

    bool ok;
    double doubleValue = value.toDouble(&ok);

    if (parameter == "target_position") {
        if (!ok) return false;
        m_targetPosition = qBound(m_minPosition, doubleValue, m_maxPosition);
        if (m_controlMode == ControlMode::Position) {
            setControlMode(ControlMode::Position);
        }
        return true;
    } else if (parameter == "target_velocity") {
        if (!ok) return false;
        m_targetVelocity = qBound(-m_maxVelocity, doubleValue, m_maxVelocity);
        if (m_controlMode == ControlMode::Velocity) {
            setControlMode(ControlMode::Velocity);
        }
        return true;
    } else if (parameter == "target_force") {
        if (!ok) return false;
        m_targetForce = qBound(-m_maxForce, doubleValue, m_maxForce);
        if (m_controlMode == ControlMode::Force) {
            setControlMode(ControlMode::Force);
        }
        return true;
    } else if (parameter == "control_mode") {
        int mode = value.toInt(&ok);
        if (!ok || mode < 0 || mode > 4) return false;
        setControlMode(static_cast<ControlMode>(mode));
        return true;
    } else if (parameter == "emergency_reset") {
        resetEmergency();
        return true;
    }

    setLastError(QString("Parameter %1 is read-only or invalid").arg(parameter));
    return false;
}

QMap<QString, QVariant> ActuatorControl::readAllValues()
{
    QMap<QString, QVariant> result;

    if (!isConnected()) {
        setLastError("Device not connected");
        return result;
    }

    result["position"] = m_currentPosition;
    result["velocity"] = m_currentVelocity;
    result["force"] = m_currentForce;
    result["target_position"] = m_targetPosition;
    result["target_velocity"] = m_targetVelocity;
    result["target_force"] = m_targetForce;
    result["state"] = static_cast<int>(m_actuatorState);
    result["control_mode"] = static_cast<int>(m_controlMode);
    result["emergency_stop"] = m_emergencyStop;
    result["min_position"] = m_minPosition;
    result["max_position"] = m_maxPosition;
    result["max_velocity"] = m_maxVelocity;
    result["max_force"] = m_maxForce;

    return result;
}

bool ActuatorControl::configure(const QMap<QString, QVariant>& config)
{
    m_configuration = config;

    if (config.contains("stroke_length")) {
        m_actuatorLength = config.value("stroke_length").toDouble();
        m_maxPosition = m_actuatorLength;
    }
    if (config.contains("cylinder_diameter")) {
        m_actuatorDiameter = config.value("cylinder_diameter").toDouble();
    }
    if (config.contains("max_velocity")) {
        m_maxVelocity = config.value("max_velocity").toDouble();
    }
    if (config.contains("max_force")) {
        m_maxForce = config.value("max_force").toDouble();
    }
    if (config.contains("system_pressure")) {
        m_systemPressure = config.value("system_pressure").toDouble();
    }

    return true;
}

QMap<QString, QVariant> ActuatorControl::getConfiguration() const
{
    QMap<QString, QVariant> config = m_configuration;

    config["stroke_length"] = m_actuatorLength;
    config["cylinder_diameter"] = m_actuatorDiameter;
    config["max_velocity"] = m_maxVelocity;
    config["max_force"] = m_maxForce;
    config["system_pressure"] = m_systemPressure;
    config["position_tolerance"] = m_positionTolerance;
    config["velocity_tolerance"] = m_velocityTolerance;
    config["force_tolerance"] = m_forceTolerance;

    return config;
}

bool ActuatorControl::calibrate()
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }

    // Simulate calibration process
    m_currentPosition = 0.0;
    m_targetPosition = 0.0;
    m_lastCalibration = QDateTime::currentDateTime();
    m_isCalibrated = true;

    return true;
}

bool ActuatorControl::isCalibrated() const
{
    return m_isCalibrated;
}

QDateTime ActuatorControl::lastCalibrationDate() const
{
    return m_lastCalibration;
}

bool ActuatorControl::performSelfTest()
{
    if (!isConnected()) {
        setLastError("Device not connected");
        return false;
    }

    // Simulate self-test
    bool success = true; // Assume success for simulation

    if (!success) {
        setLastError("Self-test failed: Simulated actuator error");
    }

    emit selfTestCompleted(success);
    return success;
}

QString ActuatorControl::getLastError() const
{
    return m_lastError;
}

QMap<QString, QVariant> ActuatorControl::getDiagnosticInfo() const
{
    QMap<QString, QVariant> info;

    info["deviceType"] = "Simulated Actuator Control";
    info["connectionStatus"] = static_cast<int>(m_connectionStatus);
    info["isCalibrated"] = m_isCalibrated;
    info["lastCalibration"] = m_lastCalibration;
    info["controlMode"] = static_cast<int>(m_controlMode);
    info["actuatorState"] = static_cast<int>(m_actuatorState);
    info["emergencyStop"] = m_emergencyStop;
    info["currentTestMode"] = static_cast<int>(m_currentTestMode);

    return info;
}

void ActuatorControl::setControlMode(ControlMode mode)
{
    if (m_emergencyStop && mode != ControlMode::Disabled) {
        setLastError("Cannot change control mode - emergency stop active");
        return;
    }

    if (m_controlMode != mode) {
        m_controlMode = mode;
        emit controlModeChanged(mode);

        // Reset targets when changing modes
        if (mode == ControlMode::Position) {
            m_targetPosition = m_currentPosition;
        } else if (mode == ControlMode::Velocity) {
            m_targetVelocity = 0.0;
        } else if (mode == ControlMode::Force) {
            m_targetForce = 0.0;
        }
    }
}

void ActuatorControl::moveToPosition(double position, double velocity)
{
    if (m_emergencyStop) return;

    setControlMode(ControlMode::Position);
    m_targetPosition = qBound(m_minPosition, position, m_maxPosition);
    m_targetVelocity = qAbs(velocity);
}

void ActuatorControl::moveRelative(double distance, double velocity)
{
    moveToPosition(m_currentPosition + distance, velocity);
}

void ActuatorControl::setPositionLimits(double minPosition, double maxPosition)
{
    m_minPosition = qMax(0.0, minPosition);
    m_maxPosition = qMin(m_actuatorLength, maxPosition);
}

void ActuatorControl::setVelocity(double velocity)
{
    if (m_emergencyStop) return;

    setControlMode(ControlMode::Velocity);
    m_targetVelocity = qBound(-m_maxVelocity, velocity, m_maxVelocity);
}

void ActuatorControl::setVelocityLimits(double maxVelocity)
{
    m_maxVelocity = qMax(1.0, maxVelocity);
}

void ActuatorControl::setForce(double force)
{
    if (m_emergencyStop) return;

    setControlMode(ControlMode::Force);
    m_targetForce = qBound(-m_maxForce, force, m_maxForce);
}

void ActuatorControl::setForceLimits(double maxForce)
{
    m_maxForce = qMax(100.0, maxForce);
}

void ActuatorControl::extend(double velocity)
{
    if (m_emergencyStop) return;

    setControlMode(ControlMode::Manual);
    m_targetVelocity = qAbs(velocity);
}

void ActuatorControl::retract(double velocity)
{
    if (m_emergencyStop) return;

    setControlMode(ControlMode::Manual);
    m_targetVelocity = -qAbs(velocity);
}

void ActuatorControl::stop()
{
    m_targetVelocity = 0.0;
}

void ActuatorControl::hold()
{
    setControlMode(ControlMode::Position);
    m_targetPosition = m_currentPosition;
}

void ActuatorControl::emergencyStop()
{
    m_emergencyStop = true;
    m_targetVelocity = 0.0;
    setControlMode(ControlMode::Disabled);
    emit emergencyStopActivated();
}

void ActuatorControl::resetEmergency()
{
    m_emergencyStop = false;
    setControlMode(ControlMode::Manual);
}

void ActuatorControl::runPositionTest(double startPos, double endPos, int cycles)
{
    m_currentTestMode = TestMode::PositionTest;
    m_testStartPos = qBound(m_minPosition, startPos, m_maxPosition);
    m_testEndPos = qBound(m_minPosition, endPos, m_maxPosition);
    m_testCycles = cycles;
    m_currentTestCycle = 0;
    m_testDirection = true;
    m_testStartTime = QDateTime::currentDateTime();

    moveToPosition(m_testStartPos);

    qDebug() << "Starting position test:" << startPos << "to" << endPos << "for" << cycles << "cycles";
}

void ActuatorControl::runVelocityTest(double velocity, double distance)
{
    m_currentTestMode = TestMode::VelocityTest;
    m_testVelocity = velocity;
    m_testStartPos = m_currentPosition;
    m_testEndPos = m_currentPosition + distance;
    m_testStartTime = QDateTime::currentDateTime();

    setVelocity(velocity);

    qDebug() << "Starting velocity test:" << velocity << "mm/s for" << distance << "mm";
}

void ActuatorControl::runForceTest(double targetForce, int durationMs)
{
    m_currentTestMode = TestMode::ForceTest;
    m_testForce = targetForce;
    m_testDuration = durationMs;
    m_testStartTime = QDateTime::currentDateTime();

    setForce(targetForce);
    m_testTimer->start(durationMs);

    qDebug() << "Starting force test:" << targetForce << "N for" << durationMs << "ms";
}

void ActuatorControl::runCycleTest(double minPos, double maxPos, int cycles, double velocity)
{
    m_currentTestMode = TestMode::CycleTest;
    m_testStartPos = qBound(m_minPosition, minPos, m_maxPosition);
    m_testEndPos = qBound(m_minPosition, maxPos, m_maxPosition);
    m_testCycles = cycles;
    m_currentTestCycle = 0;
    m_testVelocity = velocity;
    m_testDirection = true;
    m_testStartTime = QDateTime::currentDateTime();

    moveToPosition(m_testStartPos, velocity);

    qDebug() << "Starting cycle test:" << minPos << "to" << maxPos << "for" << cycles << "cycles at" << velocity << "mm/s";
}

void ActuatorControl::updateActuator()
{
    if (m_emergencyStop) {
        m_currentVelocity = 0.0;
        m_actuatorState = ActuatorState::Stopped;
        return;
    }

    updatePosition();
    updateVelocity();
    updateForce();
    updateState();

    checkLimits();
    checkPositionReached();

    // Execute current test
    switch (m_currentTestMode) {
        case TestMode::PositionTest:
            executePositionTest();
            break;
        case TestMode::VelocityTest:
            executeVelocityTest();
            break;
        case TestMode::CycleTest:
            executeCycleTest();
            break;
        default:
            break;
    }

    // Emit signals for value changes
    emit positionChanged(m_currentPosition);
    emit velocityChanged(m_currentVelocity);
    emit forceChanged(m_currentForce);
}

void ActuatorControl::onTestTimer()
{
    switch (m_currentTestMode) {
        case TestMode::ForceTest:
            emit testCompleted("Force Test");
            m_currentTestMode = TestMode::None;
            setControlMode(ControlMode::Manual);
            break;
        default:
            break;
    }
}

void ActuatorControl::updatePosition()
{
    double deltaTime = UPDATE_INTERVAL_MS / 1000.0; // Convert to seconds

    // Update position based on current velocity
    double newPosition = m_currentPosition + (m_currentVelocity * deltaTime);

    // Apply position limits
    newPosition = qBound(m_minPosition, newPosition, m_maxPosition);

    m_currentPosition = newPosition;
}

void ActuatorControl::updateVelocity()
{
    double timeConstant = 0.2; // seconds for velocity response

    switch (m_controlMode) {
        case ControlMode::Position: {
            // Position control - calculate velocity to reach target
            double positionError = m_targetPosition - m_currentPosition;
            double maxVelForError = qMin(m_targetVelocity, qAbs(positionError) * 2.0);
            double targetVel = (positionError > 0) ? maxVelForError : -maxVelForError;

            if (qAbs(positionError) < m_positionTolerance) {
                targetVel = 0.0;
            }

            m_currentVelocity = simulateInertia(m_currentVelocity, targetVel, timeConstant);
            break;
        }
        case ControlMode::Velocity:
        case ControlMode::Manual:
            // Direct velocity control
            m_currentVelocity = simulateInertia(m_currentVelocity, m_targetVelocity, timeConstant);
            break;
        case ControlMode::Force:
        {
            // Force control - velocity depends on load
            double forceError = m_targetForce - m_currentForce;
            double targetVel = forceError * 0.01; // Simple force-to-velocity mapping
            m_currentVelocity = simulateInertia(m_currentVelocity, targetVel, timeConstant);
            break;
        }
        case ControlMode::Disabled:
            m_currentVelocity = simulateInertia(m_currentVelocity, 0.0, timeConstant * 0.5);
            break;
    }

    // Apply velocity limits
    m_currentVelocity = qBound(-m_maxVelocity, m_currentVelocity, m_maxVelocity);
}

void ActuatorControl::updateForce()
{
    // Simulate force based on pressure and cylinder area
    double cylinderArea = M_PI * qPow(m_actuatorDiameter / 2000.0, 2); // Convert mm to m
    double pressureForce = calculateForceFromPressure(m_systemPressure);

    // Add load-dependent force variation
    double loadForce = m_currentVelocity * 50.0; // Friction/load force

    m_currentForce = pressureForce - loadForce;

    // Apply force limits
    m_currentForce = qBound(-m_maxForce, m_currentForce, m_maxForce);
}

void ActuatorControl::updateState()
{
    ActuatorState newState;

    if (m_emergencyStop) {
        newState = ActuatorState::Error;
    } else if (qAbs(m_currentVelocity) < m_velocityTolerance) {
        if (m_controlMode == ControlMode::Position &&
            qAbs(m_currentPosition - m_targetPosition) < m_positionTolerance) {
            newState = ActuatorState::Holding;
        } else {
            newState = ActuatorState::Stopped;
        }
    } else if (m_currentVelocity > m_velocityTolerance) {
        newState = ActuatorState::Extending;
    } else {
        newState = ActuatorState::Retracting;
    }

    if (m_actuatorState != newState) {
        m_actuatorState = newState;
        emit actuatorStateChanged(newState);
    }
}

void ActuatorControl::checkLimits()
{
    if (m_currentPosition <= m_minPosition && m_currentVelocity < 0) {
        m_currentVelocity = 0.0;
        m_currentPosition = m_minPosition;
        emit limitReached("Minimum Position");
    } else if (m_currentPosition >= m_maxPosition && m_currentVelocity > 0) {
        m_currentVelocity = 0.0;
        m_currentPosition = m_maxPosition;
        emit limitReached("Maximum Position");
    }
}

void ActuatorControl::checkPositionReached()
{
    if (m_controlMode == ControlMode::Position) {
        if (qAbs(m_currentPosition - m_targetPosition) < m_positionTolerance &&
            qAbs(m_currentVelocity) < m_velocityTolerance) {
            emit positionReached(m_currentPosition);
        }
    }
}

void ActuatorControl::executePositionTest()
{
    // Check if position reached
    if (qAbs(m_currentPosition - m_targetPosition) < m_positionTolerance) {
        if (m_testDirection) {
            // Move to end position
            moveToPosition(m_testEndPos);
            m_testDirection = false;
        } else {
            // Move to start position and increment cycle
            moveToPosition(m_testStartPos);
            m_testDirection = true;
            m_currentTestCycle++;

            if (m_currentTestCycle >= m_testCycles) {
                emit testCompleted("Position Test");
                m_currentTestMode = TestMode::None;
            }
        }
    }
}

void ActuatorControl::executeVelocityTest()
{
    // Check if distance covered
    if ((m_testVelocity > 0 && m_currentPosition >= m_testEndPos) ||
        (m_testVelocity < 0 && m_currentPosition <= m_testEndPos)) {
        stop();
        emit testCompleted("Velocity Test");
        m_currentTestMode = TestMode::None;
    }
}

void ActuatorControl::executeForceTest()
{
    // Force test is handled by timer
}

void ActuatorControl::executeCycleTest()
{
    // Check if position reached
    if (qAbs(m_currentPosition - m_targetPosition) < m_positionTolerance) {
        if (m_testDirection) {
            // Move to end position
            moveToPosition(m_testEndPos, m_testVelocity);
            m_testDirection = false;
        } else {
            // Move to start position and increment cycle
            moveToPosition(m_testStartPos, m_testVelocity);
            m_testDirection = true;
            m_currentTestCycle++;

            if (m_currentTestCycle >= m_testCycles) {
                emit testCompleted("Cycle Test");
                m_currentTestMode = TestMode::None;
            }
        }
    }
}

double ActuatorControl::calculateForceFromPressure(double pressure)
{
    // Convert pressure from bar to Pa and calculate force
    double pressurePa = pressure * 100000.0;
    double cylinderArea = M_PI * qPow(m_actuatorDiameter / 2000.0, 2); // Convert mm to m
    return pressurePa * cylinderArea;
}

double ActuatorControl::calculateVelocityFromFlow(double flow)
{
    // Convert flow from L/min to m³/s and calculate velocity
    double flowMs = flow / 60000.0;
    double cylinderArea = M_PI * qPow(m_actuatorDiameter / 2000.0, 2);
    return (flowMs / cylinderArea) * 1000.0; // Convert to mm/s
}

double ActuatorControl::simulateInertia(double currentValue, double targetValue, double timeConstant)
{
    double deltaTime = UPDATE_INTERVAL_MS / 1000.0;
    double alpha = 1.0 - qExp(-deltaTime / timeConstant);
    return currentValue + alpha * (targetValue - currentValue);
}
