<?php

/**
 * Classe pour gérer la politique de mots de passe
 * Définit et valide les exigences minimales pour les mots de passe
 */
class PasswordPolicy
{
    // Configuration des exigences par défaut
    private static $requirements = [
        'min_length' => 8,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_special_chars' => false,
        'min_special_chars' => 1,
        'forbidden_patterns' => ['password', 'admin', '123456', 'azerty'],
        'max_consecutive_chars' => 3
    ];

    /**
     * Obtenir les exigences actuelles
     */
    public static function getRequirements()
    {
        return self::$requirements;
    }

    /**
     * Valider un mot de passe selon les exigences
     * @param string $password Le mot de passe à valider
     * @return array Résultat de la validation avec détails
     */
    public static function validate($password)
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'score' => 0,
            'strength' => 'weak'
        ];

        $requirements = self::$requirements;

        // Vérifier la longueur minimale
        if (strlen($password) < $requirements['min_length']) {
            $result['valid'] = false;
            $result['errors'][] = "Le mot de passe doit contenir au moins {$requirements['min_length']} caractères";
        } else {
            $result['score'] += 20;
        }

        // Vérifier les majuscules
        if ($requirements['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $result['valid'] = false;
            $result['errors'][] = "Le mot de passe doit contenir au moins une lettre majuscule";
        } else if ($requirements['require_uppercase']) {
            $result['score'] += 15;
        }

        // Vérifier les minuscules
        if ($requirements['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $result['valid'] = false;
            $result['errors'][] = "Le mot de passe doit contenir au moins une lettre minuscule";
        } else if ($requirements['require_lowercase']) {
            $result['score'] += 15;
        }

        // Vérifier les chiffres
        if ($requirements['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            $result['valid'] = false;
            $result['errors'][] = "Le mot de passe doit contenir au moins un chiffre";
        } else if ($requirements['require_numbers']) {
            $result['score'] += 15;
        }

        // Vérifier les caractères spéciaux
        if ($requirements['require_special_chars']) {
            $specialCharsCount = preg_match_all('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password);
            if ($specialCharsCount < $requirements['min_special_chars']) {
                $result['valid'] = false;
                $result['errors'][] = "Le mot de passe doit contenir au moins {$requirements['min_special_chars']} caractère(s) spécial(aux)";
            } else {
                $result['score'] += 20;
            }
        }

        // Vérifier les motifs interdits
        foreach ($requirements['forbidden_patterns'] as $pattern) {
            if (stripos($password, $pattern) !== false) {
                $result['valid'] = false;
                $result['errors'][] = "Le mot de passe ne doit pas contenir des motifs courants comme '{$pattern}'";
                break;
            }
        }

        // Vérifier les caractères consécutifs
        if (self::hasConsecutiveChars($password, $requirements['max_consecutive_chars'])) {
            $result['valid'] = false;
            $result['errors'][] = "Le mot de passe ne doit pas contenir plus de {$requirements['max_consecutive_chars']} caractères identiques consécutifs";
        }

        // Bonus pour la longueur
        if (strlen($password) >= 12) {
            $result['score'] += 10;
        }
        if (strlen($password) >= 16) {
            $result['score'] += 5;
        }

        // Déterminer la force du mot de passe
        if ($result['score'] >= 80) {
            $result['strength'] = 'very_strong';
        } elseif ($result['score'] >= 60) {
            $result['strength'] = 'strong';
        } elseif ($result['score'] >= 40) {
            $result['strength'] = 'medium';
        } elseif ($result['score'] >= 20) {
            $result['strength'] = 'weak';
        } else {
            $result['strength'] = 'very_weak';
        }

        return $result;
    }

    /**
     * Vérifier s'il y a des caractères consécutifs
     */
    private static function hasConsecutiveChars($password, $maxConsecutive)
    {
        $length = strlen($password);
        $count = 1;
        
        for ($i = 1; $i < $length; $i++) {
            if ($password[$i] === $password[$i - 1]) {
                $count++;
                if ($count > $maxConsecutive) {
                    return true;
                }
            } else {
                $count = 1;
            }
        }
        
        return false;
    }

    /**
     * Générer un mot de passe sécurisé
     */
    public static function generateSecurePassword($length = 12)
    {
        $requirements = self::$requirements;
        $chars = '';
        
        // Construire le jeu de caractères selon les exigences
        if ($requirements['require_lowercase']) {
            $chars .= 'abcdefghijklmnopqrstuvwxyz';
        }
        if ($requirements['require_uppercase']) {
            $chars .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        }
        if ($requirements['require_numbers']) {
            $chars .= '0123456789';
        }
        if ($requirements['require_special_chars']) {
            $chars .= '!@#$%^&*()_+-=[]{}|;:,.<>?';
        }
        
        if (empty($chars)) {
            $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        }
        
        do {
            $password = '';
            for ($i = 0; $i < $length; $i++) {
                $password .= $chars[random_int(0, strlen($chars) - 1)];
            }
            $validation = self::validate($password);
        } while (!$validation['valid']);
        
        return $password;
    }

    /**
     * Obtenir les exigences sous forme de texte lisible
     */
    public static function getRequirementsText()
    {
        $requirements = self::$requirements;
        $text = [];
        
        $text[] = "Au moins {$requirements['min_length']} caractères";
        
        if ($requirements['require_uppercase']) {
            $text[] = "Au moins une lettre majuscule (A-Z)";
        }
        
        if ($requirements['require_lowercase']) {
            $text[] = "Au moins une lettre minuscule (a-z)";
        }
        
        if ($requirements['require_numbers']) {
            $text[] = "Au moins un chiffre (0-9)";
        }
        
        if ($requirements['require_special_chars']) {
            $text[] = "Au moins {$requirements['min_special_chars']} caractère(s) spécial(aux) (!@#$%^&*...)";
        }
        
        if (!empty($requirements['forbidden_patterns'])) {
            $text[] = "Ne doit pas contenir de motifs courants (" . implode(', ', $requirements['forbidden_patterns']) . ")";
        }
        
        if ($requirements['max_consecutive_chars'] > 0) {
            $text[] = "Pas plus de {$requirements['max_consecutive_chars']} caractères identiques consécutifs";
        }
        
        return $text;
    }

    /**
     * Obtenir la couleur CSS selon la force du mot de passe
     */
    public static function getStrengthColor($strength)
    {
        switch ($strength) {
            case 'very_strong':
                return 'text-green-600 dark:text-green-400';
            case 'strong':
                return 'text-green-500 dark:text-green-300';
            case 'medium':
                return 'text-yellow-500 dark:text-yellow-400';
            case 'weak':
                return 'text-orange-500 dark:text-orange-400';
            case 'very_weak':
            default:
                return 'text-red-600 dark:text-red-400';
        }
    }

    /**
     * Obtenir le texte de force du mot de passe
     */
    public static function getStrengthText($strength)
    {
        switch ($strength) {
            case 'very_strong':
                return 'Très fort';
            case 'strong':
                return 'Fort';
            case 'medium':
                return 'Moyen';
            case 'weak':
                return 'Faible';
            case 'very_weak':
            default:
                return 'Très faible';
        }
    }

    /**
     * Mettre à jour les exigences (pour une future interface d'administration)
     */
    public static function updateRequirements($newRequirements)
    {
        self::$requirements = array_merge(self::$requirements, $newRequirements);
        // TODO: Sauvegarder en base de données ou fichier de configuration
    }
}
