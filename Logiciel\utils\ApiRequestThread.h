#ifndef APIREQUESTTHREAD_H
#define APIREQUESTTHREAD_H

#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QQueue>
#include <QLoggingCategory>
#include <memory>

#include "network/ApiClient.h"
#include "models/ApiResponse.h"

Q_DECLARE_LOGGING_CATEGORY(apiRequestThreadLog)

/**
 * @brief Non-blocking API request thread for hydraulic test bench system
 * 
 * This class provides a background thread for executing API requests without
 * blocking the main UI thread. It queues requests and processes them sequentially
 * while providing progress feedback and error handling.
 */
class ApiRequestThread : public QThread
{
    Q_OBJECT
    
public:
    /**
     * @brief API request structure
     */
    struct ApiRequest {
        int requestId;                  ///< Unique request identifier
        QString method;                 ///< HTTP method (GET, POST, PUT, DELETE)
        QString endpoint;               ///< API endpoint
        QJsonObject data;               ///< Request data
        QMap<QString, QString> headers; ///< Additional headers
        int priority = 0;               ///< Request priority (higher = more urgent)
        int timeoutMs = 30000;          ///< Request timeout in milliseconds
        int retryCount = 0;             ///< Number of retry attempts
        int maxRetries = 3;             ///< Maximum retry attempts
        QString description;            ///< Human-readable description
        QDateTime createdAt;            ///< Request creation time
    };
    
    /**
     * @brief Request status
     */
    enum class RequestStatus {
        Pending,        ///< Request is queued
        Processing,     ///< Request is being processed
        Completed,      ///< Request completed successfully
        Failed,         ///< Request failed
        Cancelled,      ///< Request was cancelled
        Timeout         ///< Request timed out
    };
    Q_ENUM(RequestStatus)
    
    explicit ApiRequestThread(QObject *parent = nullptr);
    virtual ~ApiRequestThread();
    
    // Thread control
    void startProcessing();
    void stopProcessing();
    void pauseProcessing();
    void resumeProcessing();
    
    bool isProcessing() const { return m_isProcessing; }
    bool isPaused() const { return m_isPaused; }
    
    // API client management
    void setApiClient(std::shared_ptr<ApiClient> client);
    std::shared_ptr<ApiClient> apiClient() const;
    
    // Request management
    int queueRequest(const QString& method, const QString& endpoint, 
                    const QJsonObject& data = QJsonObject(),
                    int priority = 0, const QString& description = QString());
    int queueGetRequest(const QString& endpoint, int priority = 0);
    int queuePostRequest(const QString& endpoint, const QJsonObject& data, int priority = 0);
    int queuePutRequest(const QString& endpoint, const QJsonObject& data, int priority = 0);
    int queueDeleteRequest(const QString& endpoint, int priority = 0);
    
    bool cancelRequest(int requestId);
    void cancelAllRequests();
    void clearCompletedRequests();
    
    // Request status
    RequestStatus getRequestStatus(int requestId) const;
    ApiRequest getRequest(int requestId) const;
    QList<ApiRequest> getPendingRequests() const;
    QList<ApiRequest> getCompletedRequests() const;
    int pendingRequestCount() const;
    int completedRequestCount() const;
    
    // Configuration
    void setMaxConcurrentRequests(int maxRequests);
    int maxConcurrentRequests() const;
    void setDefaultTimeout(int timeoutMs);
    int defaultTimeout() const;
    void setDefaultRetries(int maxRetries);
    int defaultRetries() const;
    
    // Statistics
    int totalRequestsProcessed() const;
    int successfulRequests() const;
    int failedRequests() const;
    double averageRequestTime() const;
    void resetStatistics();
    
signals:
    void requestQueued(int requestId, const QString& description);
    void requestStarted(int requestId);
    void requestCompleted(int requestId, const ApiResponse<QJsonObject>& response);
    void requestFailed(int requestId, const QString& error);
    void requestCancelled(int requestId);
    void requestTimeout(int requestId);
    void requestProgress(int requestId, int percentage);
    void processingStarted();
    void processingStopped();
    void processingPaused();
    void processingResumed();
    void queueEmpty();
    void statisticsUpdated();
    
protected:
    void run() override;
    
private slots:
    void onApiResponse(const QString& endpoint, const JsonResponse& response);
    void onApiError(const QString& endpoint, const NetworkError& error);
    
private:
    mutable QMutex m_mutex;
    QWaitCondition m_pauseCondition;
    QWaitCondition m_requestCondition;
    
    // Thread control
    volatile bool m_isProcessing;
    volatile bool m_isPaused;
    volatile bool m_stopRequested;
    
    // API client
    std::shared_ptr<ApiClient> m_apiClient;
    
    // Request management
    QQueue<ApiRequest> m_pendingRequests;
    QMap<int, ApiRequest> m_processingRequests;
    QMap<int, ApiRequest> m_completedRequests;
    QMap<int, RequestStatus> m_requestStatuses;
    QMap<int, ApiRequest> m_activeRequests;
    QMap<int, QElapsedTimer> m_requestTimers;
    
    int m_nextRequestId;
    int m_maxConcurrentRequests;
    int m_defaultTimeoutMs;
    int m_defaultMaxRetries;
    
    // Statistics
    int m_totalRequestsProcessed;
    int m_successfulRequests;
    int m_failedRequests;
    QList<double> m_requestTimes;
    
    // Request processing
    void processNextRequest();
    void executeRequest(const ApiRequest& request);
    void handleRequestCompletion(int requestId, bool success, const QString& error = QString());
    void retryRequest(const ApiRequest& request);
    
    // Request management helpers
    ApiRequest createRequest(const QString& method, const QString& endpoint, 
                           const QJsonObject& data, int priority, const QString& description);
    void updateRequestStatus(int requestId, RequestStatus status);
    void sortRequestsByPriority();
    
    // Statistics helpers
    void updateStatistics(double requestTimeMs, bool success);
    void cleanupOldRequests();
    
    // Constants
    static const int MAX_COMPLETED_REQUESTS = 1000;
    static const int MAX_REQUEST_TIME_HISTORY = 100;
    static const int REQUEST_CLEANUP_INTERVAL = 10000; // ms
};

#endif // APIREQUESTTHREAD_H
