<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/backup.php';

class BackupController extends BaseController
{
    public function handle()
    {
        // Vérifier que l'utilisateur est un contrôleur
        if (!$this->user || $this->user['role'] !== 'controleur') {
            return $this->error('Accès refusé. Seuls les contrôleurs peuvent gérer les sauvegardes.', 403);
        }

        switch ($this->method) {
            case 'GET':
                return $this->handleGet();
            case 'POST':
                return $this->handlePost();
            case 'DELETE':
                return $this->handleDelete();
            default:
                return $this->error('Méthode non autorisée', 405);
        }
    }

    private function handleGet()
    {
        $action = $_GET['action'] ?? 'list';

        switch ($action) {
            case 'list':
                return $this->listBackups();
            case 'disk_space':
                return $this->checkDiskSpace();
            case 'download':
                return $this->downloadBackup();
            default:
                return $this->error('Action non reconnue', 400);
        }
    }

    /**
     * Lister toutes les sauvegardes disponibles
     */
    private function listBackups()
    {
        try {
            $backups = Backup::listBackups();

            return $this->json([
                'success' => true,
                'backups' => $backups,
                'count' => count($backups)
            ]);
        } catch (Exception $e) {
            return $this->error('Erreur lors de la récupération des sauvegardes: ' . $e->getMessage());
        }
    }

    /**
     * Vérifier l'espace disque disponible
     */
    private function checkDiskSpace()
    {
        try {
            $path = $_GET['path'] ?? __DIR__ . '/../../backups';
            $result = Backup::checkDiskSpace($path);

            return $this->json($result);
        } catch (Exception $e) {
            return $this->error('Erreur lors de la vérification de l\'espace disque: ' . $e->getMessage());
        }
    }

    /**
     * Télécharger un fichier de sauvegarde
     */
    private function downloadBackup()
    {
        try {
            $filename = $_GET['filename'] ?? '';

            if (empty($filename)) {
                return $this->error('Nom de fichier requis', 400);
            }

            $backupPath = __DIR__ . '/../../backups/' . basename($filename);

            if (!file_exists($backupPath)) {
                return $this->error('Fichier de sauvegarde introuvable', 404);
            }

            // Définir les en-têtes pour le téléchargement
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
            header('Content-Length: ' . filesize($backupPath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            // Lire et envoyer le fichier
            readfile($backupPath);
            exit;

        } catch (Exception $e) {
            return $this->error('Erreur lors du téléchargement: ' . $e->getMessage());
        }
    }

    private function handlePost()
    {
        $action = $this->data['action'] ?? '';

        switch ($action) {
            case 'create':
                return $this->createBackup();
            case 'restore':
                return $this->restoreBackup();
            case 'restore_safe':
                return $this->restoreBackupSafe();
            case 'initialize':
                return $this->initializeDatabase();
            case 'upload':
                return $this->uploadBackup();
            default:
                return $this->error('Action non reconnue', 400);
        }
    }

    /**
     * Créer une nouvelle sauvegarde
     */
    private function createBackup()
    {
        try {
            $customPath = $this->data['path'] ?? null;
            $result = Backup::createBackup($customPath);

            if ($result['success']) {
                return $this->json($result, 201);
            } else {
                return $this->error($result['message'], 500);
            }
        } catch (Exception $e) {
            return $this->error('Erreur lors de la création de la sauvegarde: ' . $e->getMessage());
        }
    }

    /**
     * Restaurer la base de données à partir d'une sauvegarde (méthode simple)
     */
    private function restoreBackup()
    {
        try {
            $backupFile = $this->data['backup_file'] ?? '';
            $confirmed = $this->data['confirmed'] ?? false;

            if (empty($backupFile)) {
                return $this->error('Fichier de sauvegarde requis', 400);
            }

            if (!$confirmed) {
                return $this->error('Confirmation requise pour la restauration', 400);
            }

            $result = Backup::restoreBackup($backupFile, $confirmed);

            if ($result['success']) {
                return $this->json($result);
            } else {
                return $this->error($result['message'], 500);
            }
        } catch (Exception $e) {
            return $this->error('Erreur lors de la restauration: ' . $e->getMessage());
        }
    }

    /**
     * Restaurer la base de données de manière sécurisée avec backup temporaire
     */
    private function restoreBackupSafe()
    {
        try {
            $backupFile = $this->data['backup_file'] ?? '';
            $confirmed = $this->data['confirmed'] ?? false;

            if (empty($backupFile)) {
                return $this->error('Fichier de sauvegarde requis', 400);
            }

            if (!$confirmed) {
                return $this->error('Confirmation requise pour la restauration sécurisée', 400);
            }

            $result = Backup::restoreBackupSafe($backupFile, $confirmed);

            if ($result['success']) {
                return $this->json($result);
            } else {
                return $this->error($result['message'], 500);
            }
        } catch (Exception $e) {
            return $this->error('Erreur lors de la restauration sécurisée: ' . $e->getMessage());
        }
    }

    /**
     * Initialiser une base de données vierge
     */
    private function initializeDatabase()
    {
        try {
            $confirmed = $this->data['confirmed'] ?? false;

            if (!$confirmed) {
                return $this->error('Confirmation requise pour l\'initialisation', 400);
            }

            $result = Backup::initializeCleanDatabase($confirmed);

            if ($result['success']) {
                return $this->json($result);
            } else {
                return $this->error($result['message'], 500);
            }
        } catch (Exception $e) {
            return $this->error('Erreur lors de l\'initialisation: ' . $e->getMessage());
        }
    }

    /**
     * Gérer l'upload d'un fichier de sauvegarde
     */
    private function uploadBackup()
    {
        try {
            if (!isset($_FILES['backup_file'])) {
                return $this->error('Aucun fichier uploadé', 400);
            }

            $uploadedFile = $_FILES['backup_file'];

            if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
                return $this->error('Erreur lors de l\'upload du fichier', 400);
            }

            // Vérifier l'extension du fichier
            $allowedExtensions = ['sql', 'sql.gz'];
            $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

            if (!in_array($fileExtension, $allowedExtensions)) {
                return $this->error('Type de fichier non autorisé. Seuls les fichiers .sql sont acceptés.', 400);
            }

            // Créer le dossier de destination s'il n'existe pas
            $uploadDir = __DIR__ . '/../../backups/uploaded/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Générer un nom de fichier unique
            $timestamp = date('Y-m-d_H-i-s');
            $filename = 'uploaded_backup_' . $timestamp . '.' . $fileExtension;
            $destinationPath = $uploadDir . $filename;

            // Déplacer le fichier uploadé
            if (!move_uploaded_file($uploadedFile['tmp_name'], $destinationPath)) {
                return $this->error('Erreur lors de la sauvegarde du fichier', 500);
            }

            return $this->json([
                'success' => true,
                'message' => 'Fichier uploadé avec succès',
                'filename' => $filename,
                'path' => $destinationPath,
                'size' => filesize($destinationPath)
            ], 201);

        } catch (Exception $e) {
            return $this->error('Erreur lors de l\'upload: ' . $e->getMessage());
        }
    }

    private function handleDelete()
    {
        return $this->deleteBackup();
    }

    /**
     * Supprimer un fichier de sauvegarde
     */
    private function deleteBackup()
    {
        try {
            $filename = $this->data['filename'] ?? '';

            if (empty($filename)) {
                return $this->error('Nom de fichier requis', 400);
            }

            $backupPath = __DIR__ . '/../../backups/' . basename($filename);

            if (!file_exists($backupPath)) {
                return $this->error('Fichier de sauvegarde introuvable', 404);
            }

            if (!unlink($backupPath)) {
                return $this->error('Erreur lors de la suppression du fichier', 500);
            }

            return $this->json([
                'success' => true,
                'message' => 'Fichier de sauvegarde supprimé avec succès'
            ]);

        } catch (Exception $e) {
            return $this->error('Erreur lors de la suppression: ' . $e->getMessage());
        }
    }
}
