#ifndef TESTSESSIONMANAGER_H
#define TESTSESSIONMANAGER_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include <QLoggingCategory>
#include <memory>

#include "models/TestData.h"
#include "models/TestConfiguration.h"
#include "models/Essai.h"
#include "DataAcquisitionThread.h"
#include "DataProcessor.h"
#include "DataValidator.h"

Q_DECLARE_LOGGING_CATEGORY(testSessionLog)

/**
 * @brief Test session coordination and management
 * 
 * This class coordinates the entire test execution process, managing the
 * interaction between data acquisition, processing, validation, and UI
 * components. It provides a high-level interface for test control and
 * session management.
 */
class TestSessionManager : public QObject
{
    Q_OBJECT
    
public:
    /**
     * @brief Test session status
     */
    enum class SessionStatus {
        Idle,           ///< No active session
        Preparing,      ///< Preparing for test execution
        Running,        ///< Test is running
        Paused,         ///< Test is paused
        Stopping,       ///< Test is being stopped
        Completed,      ///< Test completed successfully
        Failed,         ///< Test failed due to error
        Cancelled       ///< Test was cancelled by user
    };
    Q_ENUM(SessionStatus)
    
    /**
     * @brief Session configuration
     */
    struct SessionConfig {
        bool autoSaveResults = true;        ///< Automatically save results
        bool autoGenerateReport = true;     ///< Generate report on completion
        bool pauseOnError = true;           ///< Pause session on validation error
        bool stopOnCriticalError = true;    ///< Stop session on critical error
        int maxRetries = 3;                 ///< Maximum retry attempts
        int retryDelayMs = 5000;            ///< Delay between retries
        QString sessionName;                ///< Session name
        QString sessionDescription;         ///< Session description
        QString outputDirectory;            ///< Output directory for results
    };
    
    /**
     * @brief Session statistics
     */
    struct SessionStatistics {
        QDateTime startTime;                ///< Session start time
        QDateTime endTime;                  ///< Session end time
        int totalDataPoints = 0;            ///< Total data points collected
        int validDataPoints = 0;            ///< Valid data points
        int invalidDataPoints = 0;          ///< Invalid data points
        int validationErrors = 0;           ///< Total validation errors
        int criticalErrors = 0;             ///< Critical errors
        double averageDataRate = 0.0;       ///< Average data acquisition rate
        double sessionDuration = 0.0;       ///< Session duration in seconds
        double progressPercentage = 0.0;    ///< Current progress percentage
        QString currentPhase;               ///< Current test phase
    };
    
    explicit TestSessionManager(QObject *parent = nullptr);
    virtual ~TestSessionManager();
    
    // Session control
    bool startSession(std::shared_ptr<TestConfiguration> config);
    bool startSession(std::shared_ptr<TestConfiguration> config, std::shared_ptr<Essai> essai);
    void pauseSession();
    void resumeSession();
    void stopSession();
    void cancelSession();
    
    // Session state
    SessionStatus sessionStatus() const;
    bool isSessionActive() const;
    bool isSessionRunning() const;
    bool isSessionPaused() const;
    SessionStatistics getSessionStatistics() const;
    
    // Configuration
    void setSessionConfig(const SessionConfig& config);
    SessionConfig sessionConfig() const;
    
    void setTestConfiguration(std::shared_ptr<TestConfiguration> config);
    std::shared_ptr<TestConfiguration> testConfiguration() const;
    
    void setCurrentEssai(std::shared_ptr<Essai> essai);
    std::shared_ptr<Essai> currentEssai() const;
    
    // Component management
    void setDataAcquisitionThread(std::shared_ptr<DataAcquisitionThread> thread);
    void setDataProcessor(std::shared_ptr<DataProcessor> processor);
    void setDataValidator(std::shared_ptr<DataValidator> validator);
    
    // Data access
    TestDataSet getSessionData() const;
    QList<TestData> getRecentData(int maxSamples = 100) const;
    TestData getLatestData() const;
    
    // Results management
    bool saveSessionResults(const QString& filename = QString()) const;
    bool loadSessionResults(const QString& filename);
    QString generateSessionReport() const;
    
    // Error handling
    QString getLastError() const;
    QStringList getSessionErrors() const;
    void clearSessionErrors();
    
signals:
    void sessionStarted();
    void sessionPaused();
    void sessionResumed();
    void sessionStopped();
    void sessionCompleted();
    void sessionFailed(const QString& error);
    void sessionCancelled();
    void sessionStatusChanged(SessionStatus status);
    void sessionProgressChanged(double percentage);
    void sessionPhaseChanged(const QString& phase);
    void sessionStatisticsUpdated(const SessionStatistics& stats);
    void sessionDataReceived(const TestData& data);
    void sessionError(const QString& error);
    void criticalSessionError(const QString& error);
    
private slots:
    void onDataReceived(const TestData& data);
    void onDataProcessed(const TestData& processedData);
    void onValidationFailed(const TestData& data, const QStringList& errors);
    void onCriticalValidationFailure(const TestData& data, const QStringList& criticalErrors);
    void onSafetyViolation(const TestData& data, const QStringList& violations);
    void onAcquisitionError(const QString& error);
    void onProcessingError(const QString& error);
    void onSessionTimer();
    void onRetryTimer();
    
private:
    mutable QMutex m_mutex;
    SessionStatus m_status;
    SessionConfig m_config;
    SessionStatistics m_statistics;
    
    // Configuration and test data
    std::shared_ptr<TestConfiguration> m_testConfig;
    std::shared_ptr<Essai> m_currentEssai;
    TestDataSet m_sessionData;
    
    // Component references
    std::shared_ptr<DataAcquisitionThread> m_acquisitionThread;
    std::shared_ptr<DataProcessor> m_dataProcessor;
    std::shared_ptr<DataValidator> m_dataValidator;
    
    // Timers and monitoring
    QTimer* m_sessionTimer;
    QTimer* m_retryTimer;
    QTimer* m_statisticsTimer;
    
    // Error handling
    QString m_lastError;
    QStringList m_sessionErrors;
    int m_retryCount;
    
    // Session management
    bool initializeSession();
    bool startDataAcquisition();
    void stopDataAcquisition();
    void updateSessionStatistics();
    void updateSessionProgress();
    void finalizeSession();
    
    // Error handling
    void handleSessionError(const QString& error, bool isCritical = false);
    void handleValidationError(const TestData& data, const QStringList& errors);
    void handleCriticalError(const QString& error);
    void retryOperation();
    
    // Status management
    void setSessionStatus(SessionStatus status);
    void setSessionPhase(const QString& phase);
    
    // Data management
    void addDataToSession(const TestData& data);
    void validateSessionData(const TestData& data);
    
    // Utility methods
    QString formatSessionSummary() const;
    QString generateTimestamp() const;
    bool isRetryableError(const QString& error) const;
    
    // Constants
    static const int STATISTICS_UPDATE_INTERVAL_MS = 1000;
    static const int SESSION_TIMEOUT_MS = 3600000; // 1 hour
    static const int MAX_SESSION_ERRORS = 100;
};

#endif // TESTSESSIONMANAGER_H
