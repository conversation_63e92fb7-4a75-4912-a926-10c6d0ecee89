<?php
session_start();
require_once(__DIR__ . '/lib/backup.php');

if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

// Vérifier que l'utilisateur est un contrôleur
if ($_SESSION['user']['role'] !== 'controleur') {
    header('Location: /index.php');
    exit;
}

$success = '';
$error = '';

// Traitement des actions POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_backup':
                $result = Backup::createBackup();
                if ($result['success']) {
                    $success = $result['message'] . ' (Taille: ' . round($result['file_size'] / 1024 / 1024, 2) . ' MB)';
                } else {
                    $error = $result['message'];
                }
                break;

            case 'restore_backup':
                if (isset($_POST['confirmed']) && $_POST['confirmed'] === 'yes') {
                    $backupFile = $_POST['backup_file'] ?? '';
                    if (!empty($backupFile)) {
                        $result = Backup::restoreBackup($backupFile, true);
                        if ($result['success']) {
                            $success = $result['message'];
                        } else {
                            $error = $result['message'];
                        }
                    } else {
                        $error = 'Fichier de sauvegarde requis';
                    }
                } else {
                    $error = 'Confirmation requise pour la restauration';
                }
                break;

            case 'restore_backup_safe':
                if (isset($_POST['confirmed']) && $_POST['confirmed'] === 'yes') {
                    $backupFile = $_POST['backup_file'] ?? '';
                    if (!empty($backupFile)) {
                        $result = Backup::restoreBackupSafe($backupFile, true);
                        if ($result['success']) {
                            $success = $result['message'];
                        } else {
                            $error = $result['message'];
                        }
                    } else {
                        $error = 'Fichier de sauvegarde requis';
                    }
                } else {
                    $error = 'Confirmation requise pour la restauration sécurisée';
                }
                break;

            case 'initialize_db':
                if (isset($_POST['confirmed']) && $_POST['confirmed'] === 'yes') {
                    $result = Backup::initializeCleanDatabase(true);
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'Confirmation requise pour l\'initialisation';
                }
                break;

            case 'upload_backup':
                if (isset($_FILES['backup_file'])) {
                    $uploadedFile = $_FILES['backup_file'];

                    if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
                        // Vérifier l'extension du fichier
                        $allowedExtensions = ['sql'];
                        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

                        if (in_array($fileExtension, $allowedExtensions)) {
                            // Créer le dossier de destination s'il n'existe pas
                            $uploadDir = __DIR__ . '/backups/uploaded/';
                            if (!is_dir($uploadDir)) {
                                mkdir($uploadDir, 0755, true);
                            }

                            // Générer un nom de fichier unique
                            $timestamp = date('Y-m-d_H-i-s');
                            $filename = 'uploaded_backup_' . $timestamp . '.' . $fileExtension;
                            $destinationPath = $uploadDir . $filename;

                            // Déplacer le fichier uploadé
                            if (move_uploaded_file($uploadedFile['tmp_name'], $destinationPath)) {
                                $success = 'Fichier uploadé avec succès: ' . $filename;
                            } else {
                                $error = 'Erreur lors de la sauvegarde du fichier';
                            }
                        } else {
                            $error = 'Type de fichier non autorisé. Seuls les fichiers .sql sont acceptés.';
                        }
                    } else {
                        $error = 'Erreur lors de l\'upload du fichier';
                    }
                } else {
                    $error = 'Aucun fichier uploadé';
                }
                break;

            case 'delete_backup':
                $filename = $_POST['filename'] ?? '';
                if (!empty($filename)) {
                    $backupPath = __DIR__ . '/backups/' . basename($filename);
                    if (file_exists($backupPath)) {
                        if (unlink($backupPath)) {
                            $success = 'Fichier de sauvegarde supprimé avec succès';
                        } else {
                            $error = 'Erreur lors de la suppression du fichier';
                        }
                    } else {
                        $error = 'Fichier de sauvegarde introuvable';
                    }
                } else {
                    $error = 'Nom de fichier requis';
                }
                break;
        }
    }
}

// Traitement des téléchargements
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'download') {
    $filename = $_GET['filename'] ?? '';
    if (!empty($filename)) {
        $backupPath = __DIR__ . '/backups/' . basename($filename);
        if (file_exists($backupPath)) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
            header('Content-Length: ' . filesize($backupPath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');
            readfile($backupPath);
            exit;
        }
    }
}

// Récupérer la liste des sauvegardes
$backups = Backup::listBackups();

// Vérifier l'espace disque
$diskSpace = Backup::checkDiskSpace(__DIR__ . '/backups');

ob_start();
?>

<div class="p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold dark:text-white">Gestion des Sauvegardes</h2>
        <div class="flex space-x-2">
            <button id="createBackupBtn"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                Créer une Sauvegarde
            </button>
            <button data-modal-target="uploadModal" data-modal-toggle="uploadModal"
                    class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">
                Importer une Sauvegarde
            </button>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400">
            <?php echo htmlspecialchars($success); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Informations sur l'espace disque -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow mb-6">
        <h3 class="text-xl font-bold dark:text-white mb-4">Espace Disque</h3>
        <?php if ($diskSpace['success']): ?>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Espace Libre</p>
                    <p class="text-2xl font-bold text-green-600"><?php echo $diskSpace['free_space_gb']; ?> GB</p>
                </div>
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Espace Total</p>
                    <p class="text-2xl font-bold text-blue-600"><?php echo $diskSpace['total_space_gb']; ?> GB</p>
                </div>
            </div>
        <?php else: ?>
            <p class="text-red-600"><?php echo htmlspecialchars($diskSpace['message']); ?></p>
        <?php endif; ?>
    </div>

    <!-- Liste des sauvegardes -->
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 class="text-xl font-bold dark:text-white mb-4">Sauvegardes Disponibles</h3>

        <?php if (empty($backups)): ?>
            <p class="text-gray-600 dark:text-gray-400">Aucune sauvegarde disponible.</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">Nom du Fichier</th>
                        <th scope="col" class="px-6 py-3">Date de Création</th>
                        <th scope="col" class="px-6 py-3">Taille</th>
                        <th scope="col" class="px-6 py-3">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($backups as $backup): ?>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                <?php echo htmlspecialchars($backup['filename']); ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo htmlspecialchars($backup['created_formatted']); ?>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo round($backup['size'] / 1024 / 1024, 2); ?> MB
                            </td>
                            <td class="px-6 py-4">
                                <a href="?action=download&filename=<?php echo urlencode($backup['filename']); ?>"
                                   class="font-medium text-blue-600 dark:text-blue-500 hover:underline mr-3">
                                    Télécharger
                                </a>
                                <button onclick="confirmRestore('<?php echo htmlspecialchars($backup['path']); ?>')"
                                        class="font-medium text-green-600 dark:text-green-500 hover:underline mr-3">
                                    Restaurer
                                </button>
                                <button onclick="confirmRestoreSafe('<?php echo htmlspecialchars($backup['path']); ?>')"
                                        class="font-medium text-blue-600 dark:text-blue-500 hover:underline mr-3">
                                    Restaurer (Sécurisé)
                                </button>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="delete_backup">
                                    <input type="hidden" name="filename"
                                           value="<?php echo htmlspecialchars($backup['filename']); ?>">
                                    <button type="submit"
                                            onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')"
                                            class="font-medium text-red-600 dark:text-red-500 hover:underline">
                                        Supprimer
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Actions dangereuses -->
    <div class="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg shadow mt-6">
        <h3 class="text-xl font-bold text-red-800 dark:text-red-400 mb-4">Actions Dangereuses</h3>
        <p class="text-sm text-red-700 dark:text-red-300 mb-4">
            Ces actions sont irréversibles et peuvent entraîner une perte de données.
        </p>
        <button onclick="confirmInitialize()"
                class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">
            Initialiser Base de Données Vierge
        </button>
    </div>
</div>

<!-- Modal de confirmation pour la restauration -->
<div id="restoreModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="p-6 text-center">
                <svg class="mx-auto mb-4 text-red-600 w-12 h-12 dark:text-red-400" aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                </svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                    Êtes-vous sûr de vouloir restaurer cette sauvegarde ? Cette action remplacera toutes les données
                    actuelles.
                </h3>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="restore_backup">
                    <input type="hidden" name="backup_file" id="restoreBackupFile">
                    <input type="hidden" name="confirmed" value="yes">
                    <button type="submit"
                            class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2">
                        Oui, restaurer (Simple)
                    </button>
                </form>
                <button data-modal-hide="restoreModal" type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                    Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation pour la restauration sécurisée -->
<div id="restoreSafeModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="p-6 text-center">
                <svg class="mx-auto mb-4 text-blue-600 w-12 h-12 dark:text-blue-400" aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                </svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                    Restauration sécurisée : Une backup temporaire sera créée avant la restauration.
                    En cas d'erreur, la base sera automatiquement restaurée à son état précédent.
                </h3>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="restore_backup_safe">
                    <input type="hidden" name="backup_file" id="restoreSafeBackupFile">
                    <input type="hidden" name="confirmed" value="yes">
                    <button type="submit"
                            class="text-white bg-blue-600 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2">
                        Oui, restaurer (Sécurisé)
                    </button>
                </form>
                <button data-modal-hide="restoreSafeModal" type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                    Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'upload de sauvegarde -->
<div id="uploadModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Importer une Sauvegarde
                </h3>
                <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="uploadModal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                         viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                </button>
            </div>
            <form method="POST" enctype="multipart/form-data" class="p-4 md:p-5">
                <input type="hidden" name="action" value="upload_backup">
                <div class="mb-4">
                    <label for="backup_file" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                        Fichier de sauvegarde (.sql)
                    </label>
                    <input type="file" id="backup_file" name="backup_file" accept=".sql" required
                           class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400">
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-300">Seuls les fichiers .sql sont acceptés.</p>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" data-modal-hide="uploadModal"
                            class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                        Annuler
                    </button>
                    <button type="submit"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Importer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de confirmation pour l'initialisation -->
<div id="initializeModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="p-6 text-center">
                <svg class="mx-auto mb-4 text-red-600 w-12 h-12 dark:text-red-400" aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                </svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                    Êtes-vous sûr de vouloir initialiser une base de données vierge ? Toutes les données actuelles
                    seront perdues définitivement.
                </h3>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="initialize_db">
                    <input type="hidden" name="confirmed" value="yes">
                    <button type="submit"
                            class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2">
                        Oui, initialiser
                    </button>
                </form>
                <button data-modal-hide="initializeModal" type="button"
                        class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                    Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Créer une sauvegarde
    document.getElementById('createBackupBtn').addEventListener('click', function () {
        if (confirm('Créer une nouvelle sauvegarde de la base de données ?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type="hidden" name="action" value="create_backup">';
            document.body.appendChild(form);
            form.submit();
        }
    });

    // Confirmer la restauration
    function confirmRestore(backupFile) {
        document.getElementById('restoreBackupFile').value = backupFile;
        const modal = new Modal(document.getElementById('restoreModal'));
        modal.show();
    }

    // Confirmer la restauration sécurisée
    function confirmRestoreSafe(backupFile) {
        document.getElementById('restoreSafeBackupFile').value = backupFile;
        const modal = new Modal(document.getElementById('restoreSafeModal'));
        modal.show();
    }

    // Confirmer l'initialisation
    function confirmInitialize() {
        const modal = new Modal(document.getElementById('initializeModal'));
        modal.show();
    }
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
