#ifndef USER_H
#define USER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(userModelLog)

/**
 * @brief User authentication model for hydraulic test bench system
 * 
 * This class represents a user in the system with authentication credentials,
 * role-based permissions, and audit trail information. It provides JSON
 * serialization for API communication and validation for data integrity.
 */
class User : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(QString username READ username WRITE setUsername NOTIFY usernameChanged)
    Q_PROPERTY(QString email READ email WRITE setEmail NOTIFY emailChanged)
    Q_PROPERTY(QString role READ role WRITE setRole NOTIFY roleChanged)
    Q_PROPERTY(QDateTime createdAt READ createdAt WRITE setCreatedAt NOTIFY createdAtChanged)
    
public:
    /**
     * @brief User roles in the hydraulic test bench system
     */
    enum class Role {
        Guest,          ///< Read-only access to test results
        Operator,       ///< Can run tests and view results
        Technician,     ///< Can configure tests and manage equipment
        Engineer,       ///< Can create test procedures and analyze data
        Administrator   ///< Full system access and user management
    };
    Q_ENUM(Role)
    
    explicit User(QObject *parent = nullptr);
    User(const User& other);
    User& operator=(const User& other);
    virtual ~User() = default;
    
    // Property accessors
    int id() const { return m_id; }
    void setId(int id);
    
    QString username() const { return m_username; }
    void setUsername(const QString& username);
    
    QString email() const { return m_email; }
    void setEmail(const QString& email);
    
    QString role() const { return m_role; }
    void setRole(const QString& role);
    
    QDateTime createdAt() const { return m_createdAt; }
    void setCreatedAt(const QDateTime& createdAt);
    
    // Role management
    Role userRole() const;
    void setUserRole(Role role);
    QString roleDisplayName() const;
    static QString roleToString(Role role);
    static Role roleFromString(const QString& roleStr);
    
    // Permissions
    bool canRunTests() const;
    bool canConfigureTests() const;
    bool canManageUsers() const;
    bool canAccessAdminFeatures() const;
    bool hasPermission(const QString& permission) const;
    
    // Validation
    bool isValid() const;
    QStringList validate() const;
    bool isUsernameValid() const;
    bool isEmailValid() const;
    bool isRoleValid() const;
    
    // JSON serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    QJsonObject toJsonPartial() const; // For API updates
    
    // Utility methods
    QString displayName() const;
    QString initials() const;
    bool isActive() const;
    int daysSinceCreation() const;
    
    // Operators
    bool operator==(const User& other) const;
    bool operator!=(const User& other) const { return !(*this == other); }
    
    // Static factory methods
    static User createFromJson(const QJsonObject& json);
    static User createGuest();
    static User createOperator(const QString& username, const QString& email);
    static QStringList getAllRoles();
    
signals:
    void idChanged(int id);
    void usernameChanged(const QString& username);
    void emailChanged(const QString& email);
    void roleChanged(const QString& role);
    void createdAtChanged(const QDateTime& createdAt);
    void userDataChanged();
    void validationChanged(bool isValid);
    
private:
    int m_id;
    QString m_username;
    QString m_email;
    QString m_role;
    QDateTime m_createdAt;
    
    void connectSignals();
    void validateAndEmitSignals();
    bool isValidEmail(const QString& email) const;
    bool isValidUsername(const QString& username) const;
    
    // Constants
    static const int MIN_USERNAME_LENGTH = 3;
    static const int MAX_USERNAME_LENGTH = 50;
    static const int MAX_EMAIL_LENGTH = 255;
};

#endif // USER_H
