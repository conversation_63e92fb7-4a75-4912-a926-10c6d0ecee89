#include "ApiEndpoints.h"
#include <QUrl>
#include <QUrlQuery>

// Base configuration - these should match your PHP server setup
const QString ApiEndpoints::BASE_URL = "https://bts.frflo.ovh"; // Adjust for your Docker setup
const QString ApiEndpoints::API_BASE = "/api/";

// Authentication endpoints
const QString ApiEndpoints::AUTH_LOGIN = "/auth";

// User management endpoints
const QString ApiEndpoints::USERS = "/users";
const QString ApiEndpoints::USER_BY_ID = "/users";

// Business case (affaires) endpoints
const QString ApiEndpoints::AFFAIRES = "/affaires";
const QString ApiEndpoints::AFFAIRE_BY_ID = "/affaires";
const QString ApiEndpoints::AFFAIRES_BY_STATUS = "/affaires";

// Test case (essais) endpoints
const QString ApiEndpoints::ESSAIS = "/essais";
const QString ApiEndpoints::ESSAI_BY_ID = "/essais";
const QString ApiEndpoints::ESSAIS_BY_AFFAIRE = "/essais";
const QString ApiEndpoints::ESSAIS_BY_STATUS = "/essais";

// Curve data endpoints
const QString ApiEndpoints::COURBES = "/courbes";
const QString ApiEndpoints::COURBE_BY_ID = "/courbes";
const QString ApiEndpoints::COURBES_BY_ESSAI = "/courbes";
const QString ApiEndpoints::COURBES_BY_TYPE = "/courbes";

// Process verbal (PV) endpoints
const QString ApiEndpoints::PV = "/pv";
const QString ApiEndpoints::PV_BY_ID = "/pv";
const QString ApiEndpoints::PV_BY_AFFAIRE = "/pv";

// Performance/efficiency endpoints
const QString ApiEndpoints::RENDEMENT = "/rendement";
const QString ApiEndpoints::RENDEMENT_BY_ESSAI = "/rendement";

// Backup endpoints
const QString ApiEndpoints::BACKUP = "/backup";
const QString ApiEndpoints::BACKUP_CREATE = "/backup";

// PDF generation endpoints
const QString ApiEndpoints::PDF = "/pdf";
const QString ApiEndpoints::PDF_GENERATE = "/pdf";

QString ApiEndpoints::buildUrl(const QString& endpoint)
{
    return BASE_URL + API_BASE + endpoint;
}

QString ApiEndpoints::buildUrlWithId(const QString& endpoint, int id)
{
    QUrl url(buildUrl(endpoint));
    QUrlQuery query;
    query.addQueryItem("id", QString::number(id));
    url.setQuery(query);
    return url.toString();
}

QString ApiEndpoints::buildUrlWithParam(const QString& endpoint, const QString& param, const QString& value)
{
    QUrl url(buildUrl(endpoint));
    QUrlQuery query;
    query.addQueryItem(param, value);
    url.setQuery(query);
    return url.toString();
}
