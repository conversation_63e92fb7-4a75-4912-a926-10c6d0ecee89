#ifndef DATABUFFER_H
#define DATABUFFER_H

#include <QObject>
#include <QMutex>
#include <QWaitCondition>
#include <QLoggingCategory>
#include <QDateTime>
#include <QTimer>

Q_DECLARE_LOGGING_CATEGORY(dataBufferLog)

/**
 * @brief Thread-safe circular buffer template for real-time data storage
 * 
 * This template class provides a high-performance circular buffer implementation
 * specifically designed for real-time data acquisition systems. It supports
 * thread-safe operations, automatic overflow handling, and efficient memory usage.
 */
template<typename T>
class DataBuffer : public QObject
{
public:
    /**
     * @brief Buffer statistics
     */
    struct BufferStatistics {
        int capacity = 0;               ///< Buffer capacity
        int size = 0;                   ///< Current number of items
        int totalAdded = 0;             ///< Total items added since creation
        int totalRemoved = 0;           ///< Total items removed since creation
        int overflowCount = 0;          ///< Number of overflow events
        double fillPercentage = 0.0;    ///< Current fill percentage
        double averageAddRate = 0.0;    ///< Average add rate (items/second)
        double averageRemoveRate = 0.0; ///< Average remove rate (items/second)
        QDateTime lastAdd;              ///< Last add timestamp
        QDateTime lastRemove;           ///< Last remove timestamp
        QDateTime creationTime;         ///< Buffer creation time
    };
    
    /**
     * @brief Buffer overflow behavior
     */
    enum class OverflowBehavior {
        DropOldest,     ///< Drop oldest items when buffer is full
        DropNewest,     ///< Drop newest items when buffer is full
        Block,          ///< Block until space is available
        Signal          ///< Emit signal but don't add item
    };
    
    explicit DataBuffer(int capacity = 1000, QObject *parent = nullptr);
    virtual ~DataBuffer() = default;
    
    // Non-copyable
    DataBuffer(const DataBuffer&) = delete;
    DataBuffer& operator=(const DataBuffer&) = delete;
    
    // Basic operations
    bool add(const T& item);
    bool addBatch(const QList<T>& items);
    bool remove(T& item);
    QList<T> removeBatch(int count);
    QList<T> removeAll();
    
    // Non-blocking operations
    bool tryAdd(const T& item, int timeoutMs = 0);
    bool tryRemove(T& item, int timeoutMs = 0);
    
    // Peek operations (non-destructive)
    bool peek(T& item) const;
    bool peekAt(int index, T& item) const;
    QList<T> peekRange(int startIndex, int count) const;
    QList<T> peekAll() const;
    
    // Buffer state
    int size() const;
    int capacity() const;
    bool isEmpty() const;
    bool isFull() const;
    double fillPercentage() const;
    
    // Configuration
    void setCapacity(int newCapacity);
    void setOverflowBehavior(OverflowBehavior behavior);
    OverflowBehavior overflowBehavior() const;
    
    // Buffer management
    void clear();
    void reserve(int capacity);
    void shrinkToFit();
    
    // Statistics
    BufferStatistics getStatistics() const;
    void resetStatistics();
    
    // Search and filtering
    QList<T> findItems(std::function<bool(const T&)> predicate) const;
    int findIndex(std::function<bool(const T&)> predicate) const;
    bool contains(const T& item) const;
    
    // Thread safety
    void lock() const;
    void unlock() const;
    bool tryLock(int timeoutMs = 1000) const;
    
    // Signals (note: template classes can't use Q_OBJECT, so signals are implemented differently)
    void setOnItemAdded(std::function<void(const T&)> callback) { m_onItemAdded = callback; }
    void setOnItemRemoved(std::function<void(const T&)> callback) { m_onItemRemoved = callback; }
    void setOnBufferFull(std::function<void()> callback) { m_onBufferFull = callback; }
    void setOnBufferEmpty(std::function<void()> callback) { m_onBufferEmpty = callback; }
    void setOnOverflow(std::function<void(int)> callback) { m_onOverflow = callback; }
    
private:
    mutable QMutex m_mutex;
    QWaitCondition m_notFull;
    QWaitCondition m_notEmpty;
    
    QVector<T> m_buffer;
    int m_capacity;
    int m_head;         // Index of first item
    int m_tail;         // Index of next insertion point
    int m_size;         // Current number of items
    
    OverflowBehavior m_overflowBehavior;
    BufferStatistics m_statistics;
    
    // Callback functions (替代信号)
    std::function<void(const T&)> m_onItemAdded;
    std::function<void(const T&)> m_onItemRemoved;
    std::function<void()> m_onBufferFull;
    std::function<void()> m_onBufferEmpty;
    std::function<void(int)> m_onOverflow;
    
    // Rate calculation
    QList<QDateTime> m_addTimes;
    QList<QDateTime> m_removeTimes;
    static const int RATE_CALCULATION_WINDOW = 100;
    
    // Internal methods
    void updateStatistics();
    void calculateRates();
    void handleOverflow();
    bool addInternal(const T& item);
    bool removeInternal(T& item);
    void emitItemAdded(const T& item);
    void emitItemRemoved(const T& item);
    void emitBufferFull();
    void emitBufferEmpty();
    void emitOverflow(int droppedCount);
};

// Template implementation
template<typename T>
DataBuffer<T>::DataBuffer(int capacity, QObject *parent)
    : QObject(parent)
    , m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
    , m_overflowBehavior(OverflowBehavior::DropOldest)
{
    m_buffer.resize(m_capacity);
    m_statistics.capacity = m_capacity;
    m_statistics.creationTime = QDateTime::currentDateTime();
    
    qCDebug(dataBufferLog) << "DataBuffer created with capacity" << capacity;
}

template<typename T>
bool DataBuffer<T>::add(const T& item)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_size >= m_capacity) {
        handleOverflow();
        
        if (m_overflowBehavior == OverflowBehavior::Block) {
            while (m_size >= m_capacity) {
                m_notFull.wait(&m_mutex);
            }
        } else if (m_overflowBehavior == OverflowBehavior::DropNewest) {
            return false; // Don't add the new item
        } else if (m_overflowBehavior == OverflowBehavior::Signal) {
            emitOverflow(1);
            return false;
        }
        // DropOldest is handled in handleOverflow()
    }
    
    bool result = addInternal(item);
    if (result) {
        updateStatistics();
        emitItemAdded(item);
        m_notEmpty.wakeOne();
    }
    
    return result;
}

template<typename T>
bool DataBuffer<T>::addBatch(const QList<T>& items)
{
    QMutexLocker locker(&m_mutex);
    
    bool allAdded = true;
    for (const T& item : items) {
        if (!addInternal(item)) {
            allAdded = false;
            if (m_overflowBehavior == OverflowBehavior::Block || 
                m_overflowBehavior == OverflowBehavior::Signal) {
                break; // Stop adding on first failure
            }
        }
    }
    
    updateStatistics();
    m_notEmpty.wakeAll();
    
    return allAdded;
}

template<typename T>
bool DataBuffer<T>::remove(T& item)
{
    QMutexLocker locker(&m_mutex);
    
    while (m_size == 0) {
        m_notEmpty.wait(&m_mutex);
    }
    
    bool result = removeInternal(item);
    if (result) {
        updateStatistics();
        emitItemRemoved(item);
        m_notFull.wakeOne();
        
        if (m_size == 0) {
            emitBufferEmpty();
        }
    }
    
    return result;
}

template<typename T>
bool DataBuffer<T>::tryAdd(const T& item, int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_size >= m_capacity) {
        if (timeoutMs > 0) {
            if (!m_notFull.wait(&m_mutex, timeoutMs)) {
                return false; // Timeout
            }
        } else {
            return false; // No timeout, immediate failure
        }
    }
    
    return addInternal(item);
}

template<typename T>
bool DataBuffer<T>::tryRemove(T& item, int timeoutMs)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_size == 0) {
        if (timeoutMs > 0) {
            if (!m_notEmpty.wait(&m_mutex, timeoutMs)) {
                return false; // Timeout
            }
        } else {
            return false; // No timeout, immediate failure
        }
    }
    
    return removeInternal(item);
}

template<typename T>
bool DataBuffer<T>::peek(T& item) const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_size == 0) {
        return false;
    }
    
    item = m_buffer[m_head];
    return true;
}

template<typename T>
bool DataBuffer<T>::peekAt(int index, T& item) const
{
    QMutexLocker locker(&m_mutex);
    
    if (index < 0 || index >= m_size) {
        return false;
    }
    
    int actualIndex = (m_head + index) % m_capacity;
    item = m_buffer[actualIndex];
    return true;
}

template<typename T>
QList<T> DataBuffer<T>::peekAll() const
{
    QMutexLocker locker(&m_mutex);
    
    QList<T> result;
    result.reserve(m_size);
    
    for (int i = 0; i < m_size; ++i) {
        int index = (m_head + i) % m_capacity;
        result.append(m_buffer[index]);
    }
    
    return result;
}

template<typename T>
int DataBuffer<T>::size() const
{
    QMutexLocker locker(&m_mutex);
    return m_size;
}

template<typename T>
int DataBuffer<T>::capacity() const
{
    QMutexLocker locker(&m_mutex);
    return m_capacity;
}

template<typename T>
bool DataBuffer<T>::isEmpty() const
{
    QMutexLocker locker(&m_mutex);
    return m_size == 0;
}

template<typename T>
bool DataBuffer<T>::isFull() const
{
    QMutexLocker locker(&m_mutex);
    return m_size >= m_capacity;
}

template<typename T>
double DataBuffer<T>::fillPercentage() const
{
    QMutexLocker locker(&m_mutex);
    return m_capacity > 0 ? (double(m_size) / m_capacity) * 100.0 : 0.0;
}

template<typename T>
void DataBuffer<T>::clear()
{
    QMutexLocker locker(&m_mutex);
    
    m_head = 0;
    m_tail = 0;
    m_size = 0;
    
    m_notFull.wakeAll();
    emitBufferEmpty();
    
    qCDebug(dataBufferLog) << "Buffer cleared";
}

template<typename T>
typename DataBuffer<T>::BufferStatistics DataBuffer<T>::getStatistics() const
{
    QMutexLocker locker(&m_mutex);
    
    BufferStatistics stats = m_statistics;
    stats.size = m_size;
    stats.fillPercentage = fillPercentage();
    
    return stats;
}

template<typename T>
void DataBuffer<T>::resetStatistics()
{
    QMutexLocker locker(&m_mutex);
    
    m_statistics = BufferStatistics();
    m_statistics.capacity = m_capacity;
    m_statistics.creationTime = QDateTime::currentDateTime();
    m_addTimes.clear();
    m_removeTimes.clear();
    
    qCDebug(dataBufferLog) << "Statistics reset";
}

template<typename T>
bool DataBuffer<T>::addInternal(const T& item)
{
    if (m_size >= m_capacity) {
        return false;
    }
    
    m_buffer[m_tail] = item;
    m_tail = (m_tail + 1) % m_capacity;
    m_size++;
    
    m_statistics.totalAdded++;
    m_statistics.lastAdd = QDateTime::currentDateTime();
    m_addTimes.append(m_statistics.lastAdd);
    
    if (m_size == m_capacity) {
        emitBufferFull();
    }
    
    return true;
}

template<typename T>
bool DataBuffer<T>::removeInternal(T& item)
{
    if (m_size == 0) {
        return false;
    }
    
    item = m_buffer[m_head];
    m_head = (m_head + 1) % m_capacity;
    m_size--;
    
    m_statistics.totalRemoved++;
    m_statistics.lastRemove = QDateTime::currentDateTime();
    m_removeTimes.append(m_statistics.lastRemove);
    
    return true;
}

template<typename T>
void DataBuffer<T>::handleOverflow()
{
    if (m_overflowBehavior == OverflowBehavior::DropOldest && m_size >= m_capacity) {
        // Remove oldest item to make space
        T dummy;
        removeInternal(dummy);
        m_statistics.overflowCount++;
        emitOverflow(1);
    }
}

template<typename T>
void DataBuffer<T>::updateStatistics()
{
    calculateRates();
}

template<typename T>
void DataBuffer<T>::calculateRates()
{
    QDateTime now = QDateTime::currentDateTime();
    
    // Calculate add rate
    while (m_addTimes.size() > RATE_CALCULATION_WINDOW) {
        m_addTimes.removeFirst();
    }
    
    if (m_addTimes.size() >= 2) {
        qint64 timeSpan = m_addTimes.first().msecsTo(m_addTimes.last());
        if (timeSpan > 0) {
            m_statistics.averageAddRate = (m_addTimes.size() - 1) * 1000.0 / timeSpan;
        }
    }
    
    // Calculate remove rate
    while (m_removeTimes.size() > RATE_CALCULATION_WINDOW) {
        m_removeTimes.removeFirst();
    }
    
    if (m_removeTimes.size() >= 2) {
        qint64 timeSpan = m_removeTimes.first().msecsTo(m_removeTimes.last());
        if (timeSpan > 0) {
            m_statistics.averageRemoveRate = (m_removeTimes.size() - 1) * 1000.0 / timeSpan;
        }
    }
}

// Callback emission methods
template<typename T>
void DataBuffer<T>::emitItemAdded(const T& item)
{
    if (m_onItemAdded) {
        m_onItemAdded(item);
    }
}

template<typename T>
void DataBuffer<T>::emitItemRemoved(const T& item)
{
    if (m_onItemRemoved) {
        m_onItemRemoved(item);
    }
}

template<typename T>
void DataBuffer<T>::emitBufferFull()
{
    if (m_onBufferFull) {
        m_onBufferFull();
    }
}

template<typename T>
void DataBuffer<T>::emitBufferEmpty()
{
    if (m_onBufferEmpty) {
        m_onBufferEmpty();
    }
}

template<typename T>
void DataBuffer<T>::emitOverflow(int droppedCount)
{
    if (m_onOverflow) {
        m_onOverflow(droppedCount);
    }
}

// Explicit template instantiation for common types
extern template class DataBuffer<int>;
extern template class DataBuffer<double>;
extern template class DataBuffer<QString>;

// Forward declaration for TestData
class TestData;
extern template class DataBuffer<TestData>;

#endif // DATABUFFER_H
