#ifndef STATUSINDICATORWIDGET_H
#define STATUSINDICATORWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QProgressBar>
#include <QPushButton>
#include <QGroupBox>
#include <QTimer>
#include <QLoggingCategory>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>

Q_DECLARE_LOGGING_CATEGORY(statusIndicatorLog)

/**
 * @brief Status indicator widget for hydraulic test bench system
 * 
 * This widget provides visual status indicators for various system components
 * including connection status, hardware status, data acquisition status,
 * and alarm conditions with animated indicators and color coding.
 */
class StatusIndicatorWidget : public QWidget
{
    Q_OBJECT
    
public:
    enum class IndicatorStatus {
        Unknown,    // Gray
        OK,         // Green
        Warning,    // Yellow/Orange
        Error,      // Red
        Offline     // Dark gray
    };
    
    enum class IndicatorType {
        Connection,
        Hardware,
        DataAcquisition,
        Safety,
        Communication,
        Power,
        Temperature,
        Pressure
    };
    
    explicit StatusIndicatorWidget(QWidget *parent = nullptr);
    ~StatusIndicatorWidget() = default;
    
    // Status management
    void setIndicatorStatus(IndicatorType type, IndicatorStatus status, const QString& message = QString());
    IndicatorStatus getIndicatorStatus(IndicatorType type) const;
    QString getIndicatorMessage(IndicatorType type) const;
    
    // Configuration
    void setAnimationEnabled(bool enabled);
    bool isAnimationEnabled() const { return m_animationEnabled; }
    
    void setCompactMode(bool compact);
    bool isCompactMode() const { return m_compactMode; }
    
    // Overall status
    IndicatorStatus getOverallStatus() const;
    QStringList getActiveAlarms() const;
    int getErrorCount() const;
    int getWarningCount() const;
    
public slots:
    void updateConnectionStatus(bool connected);
    void updateHardwareStatus(bool operational);
    void updateDataAcquisitionStatus(bool active, double rate = 0.0);
    void updateSafetyStatus(bool safe, const QStringList& violations = QStringList());
    void updateCommunicationStatus(bool communicating);
    void updatePowerStatus(bool powered);
    void updateTemperatureStatus(bool normal, double temperature = 0.0);
    void updatePressureStatus(bool normal, double pressure = 0.0);
    void resetAllStatus();
    void acknowledgeAlarms();
    
signals:
    void statusChanged(IndicatorType type, IndicatorStatus status);
    void overallStatusChanged(IndicatorStatus status);
    void alarmTriggered(IndicatorType type, const QString& message);
    void alarmAcknowledged(IndicatorType type);
    void indicatorClicked(IndicatorType type);
    
private slots:
    void onIndicatorClicked();
    void onBlinkTimer();
    void onAcknowledgeClicked();
    
private:
    // UI setup
    void setupUI();
    void setupIndicators();
    void setupOverallStatus();
    void setupControlButtons();
    
    // Indicator management
    QWidget* createIndicator(IndicatorType type, const QString& label);
    void updateIndicatorAppearance(IndicatorType type);
    void startBlinkAnimation(IndicatorType type);
    void stopBlinkAnimation(IndicatorType type);
    
    // Utility methods
    QString getStatusText(IndicatorStatus status) const;
    QString getStatusColor(IndicatorStatus status) const;
    QString getIndicatorTypeText(IndicatorType type) const;
    void updateOverallStatus();
    
    // Layout
    QVBoxLayout* m_mainLayout;
    QGridLayout* m_indicatorsLayout;
    QGroupBox* m_indicatorsGroup;
    QGroupBox* m_overallStatusGroup;
    QGroupBox* m_controlGroup;
    
    // Overall status display
    QLabel* m_overallStatusLabel;
    QLabel* m_overallStatusIcon;
    QLabel* m_errorCountLabel;
    QLabel* m_warningCountLabel;
    QProgressBar* m_systemHealthBar;
    
    // Control buttons
    QPushButton* m_acknowledgeButton;
    QPushButton* m_resetButton;
    QPushButton* m_testButton;
    
    // Individual indicators
    struct IndicatorData {
        QWidget* widget;
        QLabel* icon;
        QLabel* label;
        QLabel* status;
        QLabel* message;
        IndicatorStatus currentStatus;
        QString currentMessage;
        QPropertyAnimation* blinkAnimation;
        QGraphicsOpacityEffect* opacityEffect;
        bool isBlinking;
    };
    
    QMap<IndicatorType, IndicatorData> m_indicators;
    
    // Configuration
    bool m_animationEnabled;
    bool m_compactMode;
    
    // Animation
    QTimer* m_blinkTimer;
    
    // Status tracking
    IndicatorStatus m_overallStatus;
    QStringList m_activeAlarms;
    
    // Constants
    static const int BLINK_INTERVAL_MS = 500;
    static const int INDICATOR_ICON_SIZE = 24;
    static const int OVERALL_ICON_SIZE = 32;
};

#endif // STATUSINDICATORWIDGET_H
