#ifndef REALTIMECHART_H
#define REALTIMECHART_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QPushButton>
#include <QComboBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QLabel>
#include <QTimer>
#include <QLoggingCategory>
#include <QtCharts/QChart>
#include <QtCharts>
#include <QtCharts/QLineSeries>
#include <QtCharts/QValueAxis>
#include <QtCharts/QDateTimeAxis>
#include <QtCharts/QLegend>
#include <memory>

#include "models/TestData.h"
#include "models/Courbe.h"
#include "utils/DataBuffer.h"

Q_DECLARE_LOGGING_CATEGORY(realTimeChartLog)

/**
 * @brief Real-time chart widget for hydraulic test bench system
 * 
 * This widget provides real-time visualization of hydraulic test data using
 * Qt Charts. It supports multiple data series, configurable time windows,
 * auto-scaling, and interactive features for data analysis.
 */
class RealTimeChart : public QWidget
{
    Q_OBJECT
    
public:
    /**
     * @brief Chart configuration
     */
    struct ChartConfig {
        QString title = "Real-Time Data";
        QString xAxisTitle = "Time";
        QString yAxisTitle = "Value";
        int maxDataPoints = 1000;          ///< Maximum points to display
        double timeWindowSeconds = 60.0;   ///< Time window in seconds
        bool autoScale = true;              ///< Auto-scale Y axis
        double yAxisMin = 0.0;              ///< Manual Y axis minimum
        double yAxisMax = 100.0;            ///< Manual Y axis maximum
        bool showGrid = true;               ///< Show grid lines
        bool showLegend = true;             ///< Show legend
        bool enableZoom = true;             ///< Enable zoom functionality
        bool enablePan = true;              ///< Enable pan functionality
        int updateIntervalMs = 100;         ///< Chart update interval
    };
    
    /**
     * @brief Data series configuration
     */
    struct SeriesConfig {
        QString name;                       ///< Series name
        QString parameterName;              ///< Parameter to plot
        QColor color = Qt::blue;            ///< Series color
        int lineWidth = 2;                  ///< Line width
        bool visible = true;                ///< Series visibility
        bool smoothing = false;             ///< Apply smoothing
        QString unit;                       ///< Unit of measurement
        double scaleFactor = 1.0;           ///< Scale factor for display
        double offset = 0.0;                ///< Offset for display
    };
    
    explicit RealTimeChart(QWidget *parent = nullptr);
    virtual ~RealTimeChart() = default;
    
    // Configuration
    void setChartConfig(const ChartConfig& config);
    ChartConfig chartConfig() const;
    
    // Series management
    void addSeries(const SeriesConfig& config);
    void removeSeries(const QString& seriesName);
    void clearSeries();
    QStringList getSeriesNames() const;
    void setSeriesVisible(const QString& seriesName, bool visible);
    void setSeriesColor(const QString& seriesName, const QColor& color);
    
    // Data management
    void addDataPoint(const TestData& data);
    void addDataPoints(const QList<TestData>& dataList);
    void clearData();
    void setDataBuffer(std::shared_ptr<DataBuffer<TestData>> buffer);
    
    // Chart control
    void startRealTimeUpdate();
    void stopRealTimeUpdate();
    void pauseRealTimeUpdate();
    void resumeRealTimeUpdate();
    bool isRealTimeUpdateActive() const;
    
    // View control
    void resetZoom();
    void zoomToFit();
    void setTimeWindow(double seconds);
    void setYAxisRange(double min, double max);
    void enableAutoScale(bool enabled);
    
    // Export functionality
    QPixmap exportChart() const;
    bool saveChartImage(const QString& filename) const;
    bool exportDataToCsv(const QString& filename) const;
    
signals:
    void dataPointAdded(const TestData& data);
    void seriesAdded(const QString& seriesName);
    void seriesRemoved(const QString& seriesName);
    void chartConfigChanged();
    void zoomChanged();
    void timeWindowChanged(double seconds);
    
private slots:
    void onUpdateTimer();
    void onSeriesVisibilityChanged();
    void onTimeWindowChanged();
    void onAutoScaleToggled(bool enabled);
    void onYAxisRangeChanged();
    void onResetZoomClicked();
    void onExportClicked();
    void onClearDataClicked();
    
private:
    // UI setup
    void setupUI();
    void setupChart();
    void setupControls();
    void setupToolbar();
    
    // Chart management
    void updateChart();
    void updateSeries();
    void updateAxes();
    void calculateAutoScale();
    void applyChartConfig();
    
    // Data processing
    void processNewData();
    QPointF extractDataPoint(const TestData& data, const QString& parameterName) const;
    QList<QPointF> getSeriesData(const QString& seriesName) const;
    void limitSeriesData();
    
    // Utility methods
    QString formatValue(double value, const QString& unit) const;
    QColor generateSeriesColor(int index) const;
    void updateSeriesStatistics();
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    
    // Chart components
    QChart* m_chart;
    QChartView* m_chartView;
    QDateTimeAxis* m_xAxis;
    QValueAxis* m_yAxis;
    
    // Control widgets
    QGroupBox* m_controlGroup;
    QComboBox* m_timeWindowComboBox;
    QCheckBox* m_autoScaleCheckBox;
    QDoubleSpinBox* m_yMinSpinBox;
    QDoubleSpinBox* m_yMaxSpinBox;
    QPushButton* m_resetZoomButton;
    QPushButton* m_exportButton;
    QPushButton* m_clearDataButton;
    QPushButton* m_pauseResumeButton;
    
    // Series management
    QMap<QString, QLineSeries*> m_series;
    QMap<QString, SeriesConfig> m_seriesConfigs;
    QMap<QString, QList<QPointF>> m_seriesData;
    
    // Configuration
    ChartConfig m_config;
    
    // Data management
    std::shared_ptr<DataBuffer<TestData>> m_dataBuffer;
    QList<TestData> m_recentData;
    QDateTime m_startTime;
    
    // Update control
    QTimer* m_updateTimer;
    bool m_realTimeActive;
    bool m_isPaused;
    
    // Statistics
    int m_totalDataPoints;
    QDateTime m_lastUpdateTime;
    
    // Constants
    static const int DEFAULT_MAX_POINTS = 1000;
    static const int DEFAULT_UPDATE_INTERVAL_MS = 100;
    static constexpr double DEFAULT_TIME_WINDOW_SECONDS = 60.0;
    static const QStringList DEFAULT_PARAMETERS;
};

#endif // REALTIMECHART_H
