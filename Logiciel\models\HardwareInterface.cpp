#include "HardwareInterface.h"
#include <QDateTime>

HardwareInterface::HardwareInterface(QObject *parent)
    : QObject(parent)
    , m_connectionStatus(ConnectionStatus::Disconnected)
    , m_isCalibrated(false)
{
}

void HardwareInterface::setConnectionStatus(ConnectionStatus status)
{
    if (m_connectionStatus != status) {
        m_connectionStatus = status;
        emit connectionStatusChanged(status);
    }
}

void HardwareInterface::setLastError(const QString& error)
{
    m_lastError = error;
    if (!error.isEmpty()) {
        emit errorOccurred(error);
    }
}

void HardwareInterface::emitDataReceived(const QString& parameter, const QVariant& value)
{
    emit dataReceived(parameter, value);
}
