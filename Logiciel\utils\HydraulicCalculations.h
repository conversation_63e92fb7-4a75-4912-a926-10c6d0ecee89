#ifndef HYDRAULICCALCULATIONS_H
#define HYDRAULICCALCULATIONS_H

#include <QObject>
#include <QtMath>
#include "models/TestData.h"

// Forward declarations
class TestDataSet;

/**
 * @brief Hydraulic system parameters for calculations
 */
struct HydraulicSystemParameters {
    double cylinderDiameter = 80.0;     // mm
    double rodDiameter = 40.0;          // mm
    double strokeLength = 500.0;        // mm
    double fluidDensity = 850.0;        // kg/m³
    double fluidViscosity = 32.0;       // cSt at 40°C
    double bulkModulus = 1700.0;        // MPa
    double systemPressure = 200.0;      // bar
    double returnPressure = 2.0;        // bar
    double ambientTemperature = 20.0;   // °C
    double operatingTemperature = 45.0; // °C
};

/**
 * @brief Test criteria for pass/fail evaluation
 */
struct TestCriteria {
    double minPressure = 0.0;           // bar
    double maxPressure = 250.0;         // bar
    double minFlow = 0.0;               // L/min
    double maxFlow = 100.0;             // L/min
    double maxTemperature = 80.0;       // °C
    double minEfficiency = 80.0;        // %
    double maxLeakageRate = 1.0;        // L/min
    double positionAccuracy = 1.0;      // mm
    double velocityAccuracy = 5.0;      // mm/s
    double forceAccuracy = 100.0;       // N
};

/**
 * @brief Hydraulic calculations and analysis utilities
 * 
 * This class provides static methods for performing hydraulic calculations
 * commonly used in hydraulic cylinder testing and analysis.
 */
class HydraulicCalculations
{
public:
    // Pressure calculations
    static double pressureDifferential(double pressureA, double pressureB);
    static double pressureRatio(double pressure1, double pressure2);
    static double pressureFromForce(double force, double area);
    static double forceFromPressure(double pressure, double area);
    
    // Flow calculations
    static double flowFromVelocity(double velocity, double area);
    static double velocityFromFlow(double flow, double area);
    static double reynoldsNumber(double velocity, double diameter, double kinematicViscosity);
    static double flowCoefficient(double actualFlow, double theoreticalFlow);
    
    // Power calculations
    static double hydraulicPower(double pressure, double flow);
    static double mechanicalPower(double force, double velocity);
    static double powerLoss(double hydraulicPower, double mechanicalPower);
    
    // Efficiency calculations
    static double volumetricEfficiency(double actualFlow, double theoreticalFlow);
    static double mechanicalEfficiency(double mechanicalPower, double hydraulicPower);
    static double overallEfficiency(double volumetricEff, double mechanicalEff);
    
    // Cylinder calculations
    static double cylinderArea(double diameter);
    static double cylinderVolume(double diameter, double stroke);
    static double rodArea(double rodDiameter);
    static double annularArea(double cylinderDiameter, double rodDiameter);
    
    // Velocity and acceleration
    static double averageVelocity(double distance, double time);
    static double instantaneousVelocity(double position1, double position2, double deltaTime);
    static double acceleration(double velocity1, double velocity2, double deltaTime);
    
    // Temperature effects
    static double viscosityTemperatureCorrection(double viscosity, double temp1, double temp2);
    static double densityTemperatureCorrection(double density, double temp1, double temp2);
    static double bulkModulusTemperatureCorrection(double bulkModulus, double temp1, double temp2);
    
    // Leakage calculations
    static double leakageRate(double pressure1, double pressure2, double time);
    static double leakageCoefficient(double leakageRate, double pressureDiff);
    static bool isLeakageAcceptable(double leakageRate, double maxAllowable);
    
    // Test analysis
    static TestData calculateDerivedValues(const TestData& rawData, const HydraulicSystemParameters& params);
    static double calculateTestScore(const TestDataSet& testData, const TestCriteria& criteria);
    static QMap<QString, double> performStatisticalAnalysis(const TestDataSet& testData);
    
    // Unit conversions
    static double barToPsi(double bar);
    static double psiToBar(double psi);
    static double barToPascal(double bar);
    static double pascalToBar(double pascal);
    static double lpmToGpm(double lpm);
    static double gpmToLpm(double gpm);
    static double lpmToCubicMeterPerSecond(double lpm);
    static double cubicMeterPerSecondToLpm(double cms);
    static double celsiusToFahrenheit(double celsius);
    static double fahrenheitToCelsius(double fahrenheit);
    static double mmToInch(double mm);
    static double inchToMm(double inch);
    
    // Constants
    static const double ATMOSPHERIC_PRESSURE_BAR;
    static const double WATER_DENSITY_KG_M3;
    static const double HYDRAULIC_OIL_DENSITY_KG_M3;
    static const double GRAVITY_MS2;
    static const double PI;
    
private:
    HydraulicCalculations() = default; // Static class
};

/**
 * @brief Hydraulic test result analysis
 */
class HydraulicTestAnalyzer
{
public:
    explicit HydraulicTestAnalyzer(const HydraulicSystemParameters& params);
    
    // Analysis methods
    TestData analyzeDataPoint(const TestData& rawData);
    TestDataSet analyzeTestSet(const TestDataSet& rawDataSet);
    
    // Performance metrics
    double calculateVolumetricEfficiency(const TestData& data);
    double calculateMechanicalEfficiency(const TestData& data);
    double calculateOverallEfficiency(const TestData& data);
    
    // Test validation
    bool validateTestData(const TestData& data, QStringList& errors);
    bool isTestPassing(const TestDataSet& testData, const TestCriteria& criteria);

    // Statistical analysis
    QMap<QString, double> calculateStatistics(const TestDataSet& testData, const QString& parameter);

    // System parameters
    void setSystemParameters(const HydraulicSystemParameters& params);
    HydraulicSystemParameters systemParameters() const { return m_params; }

private:
    HydraulicSystemParameters m_params;
    
    double calculateTheoreticalFlow(double velocity);
    double calculateTheoreticalPower(double pressure, double flow);
    double calculateHeatGeneration(const TestData& data);
};

#endif // HYDRAULICCALCULATIONS_H
