<?php
require_once(__DIR__ . '/../config/database.php');
require_once(__DIR__ . '/password_policy.php');

class User
{
    public static function create($username, $password, $role)
    {
        // Valider le mot de passe selon la politique
        $validation = PasswordPolicy::validate($password);
        if (!$validation['valid']) {
            throw new Exception('Mot de passe non conforme : ' . implode(', ', $validation['errors']));
        }

        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = self::getDb()->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
        return $stmt->execute([$username, $hashedPassword, $role]);
    }

    // Créer un nouvel utilisateur

    private static function getDb()
    {
        return Database::getInstance()->getConnection();
    }

    // Récupérer un utilisateur par son ID

    public static function getById($id)
    {
        $stmt = self::getDb()->prepare("SELECT id, username, role, created_at FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Récupérer un utilisateur par son nom d'utilisateur
    public static function getByUsername($username)
    {
        $stmt = self::getDb()->prepare("SELECT id, username, role, created_at FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Mettre à jour un utilisateur
    public static function update($id, $username, $role)
    {
        $stmt = self::getDb()->prepare("UPDATE users SET username = ?, role = ? WHERE id = ?");
        return $stmt->execute([$username, $role, $id]);
    }

    // Modifier le mot de passe d'un utilisateur
    public static function updatePassword($id, $newPassword)
    {
        // Valider le mot de passe selon la politique
        $validation = PasswordPolicy::validate($newPassword);
        if (!$validation['valid']) {
            throw new Exception('Mot de passe non conforme : ' . implode(', ', $validation['errors']));
        }

        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = self::getDb()->prepare("UPDATE users SET password = ? WHERE id = ?");
        return $stmt->execute([$hashedPassword, $id]);
    }

    // Supprimer un utilisateur
    public static function delete($id)
    {
        $stmt = self::getDb()->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // Vérifier les identifiants de connexion
    public static function authenticate($username, $password)
    {
        $stmt = self::getDb()->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        return false;
    }

    // Récupérer tous les utilisateurs
    public static function getAllUsers()
    {
        $stmt = self::getDb()->query("SELECT id, username, role, created_at FROM users ORDER BY created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Compter le nombre de contrôleurs
    public static function countControleurs()
    {
        $stmt = self::getDb()->prepare("SELECT COUNT(*) FROM users WHERE role = 'controleur'");
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    // Vérifier si un utilisateur est le dernier contrôleur
    public static function isLastControleur($userId)
    {
        $stmt = self::getDb()->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user || $user['role'] !== 'controleur') {
            return false;
        }

        return self::countControleurs() <= 1;
    }

    // Vérifier si le changement de rôle laisserait le système sans contrôleur
    public static function wouldLeaveNoControleur($userId, $newRole)
    {
        if ($newRole === 'controleur') {
            return false; // Promouvoir quelqu'un en contrôleur ne pose pas de problème
        }

        // Si on rétrograde un contrôleur, vérifier qu'il en reste d'autres
        $stmt = self::getDb()->prepare("SELECT role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user || $user['role'] !== 'controleur') {
            return false; // L'utilisateur n'est pas contrôleur, pas de problème
        }

        return self::countControleurs() <= 1;
    }

    // Vérifier si un nom d'utilisateur existe déjà
    public static function usernameExists($username)
    {
        $stmt = self::getDb()->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetchColumn() > 0;
    }
}
