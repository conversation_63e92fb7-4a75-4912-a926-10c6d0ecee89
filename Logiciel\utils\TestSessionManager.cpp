#include "TestSessionManager.h"
#include <QDebug>
#include <QMutexLocker>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDir>
#include <QStandardPaths>

Q_LOGGING_CATEGORY(testSessionLog, "hydraulic.utils.testsession")

TestSessionManager::TestSessionManager(QObject *parent)
    : QObject(parent)
    , m_status(SessionStatus::Idle)
    , m_retryCount(0)
{
    // Initialize default configuration
    m_config.autoSaveResults = true;
    m_config.autoGenerateReport = true;
    m_config.pauseOnError = true;
    m_config.stopOnCriticalError = true;
    m_config.maxRetries = 3;
    m_config.retryDelayMs = 5000;
    m_config.sessionName = "Hydraulic Test Session";
    m_config.outputDirectory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/HydraulicTests";
    
    // Initialize timers
    m_sessionTimer = new QTimer(this);
    m_sessionTimer->setSingleShot(false);
    connect(m_sessionTimer, &QTimer::timeout, this, &TestSessionManager::onSessionTimer);
    
    m_retryTimer = new QTimer(this);
    m_retryTimer->setSingleShot(true);
    connect(m_retryTimer, &QTimer::timeout, this, &TestSessionManager::onRetryTimer);
    
    m_statisticsTimer = new QTimer(this);
    m_statisticsTimer->setInterval(STATISTICS_UPDATE_INTERVAL_MS);
    connect(m_statisticsTimer, &QTimer::timeout, this, &TestSessionManager::updateSessionStatistics);
    
    qCDebug(testSessionLog) << "TestSessionManager initialized";
}

TestSessionManager::~TestSessionManager()
{
    if (isSessionActive()) {
        stopSession();
    }
}

bool TestSessionManager::startSession(std::shared_ptr<TestConfiguration> config)
{
    return startSession(config, nullptr);
}

bool TestSessionManager::startSession(std::shared_ptr<TestConfiguration> config, std::shared_ptr<Essai> essai)
{
    QMutexLocker locker(&m_mutex);
    
    if (isSessionActive()) {
        m_lastError = "Cannot start session: another session is already active";
        qCWarning(testSessionLog) << m_lastError;
        return false;
    }
    
    if (!config) {
        m_lastError = "Cannot start session: invalid test configuration";
        qCWarning(testSessionLog) << m_lastError;
        return false;
    }
    
    // Validate test configuration
    if (!config->isValid()) {
        QStringList errors = config->validate();
        m_lastError = QString("Invalid test configuration: %1").arg(errors.join(", "));
        qCWarning(testSessionLog) << m_lastError;
        return false;
    }
    
    // Set configuration and essai
    m_testConfig = config;
    m_currentEssai = essai;
    
    // Initialize session
    if (!initializeSession()) {
        return false;
    }
    
    // Start data acquisition
    if (!startDataAcquisition()) {
        return false;
    }
    
    // Update session status
    setSessionStatus(SessionStatus::Running);
    setSessionPhase("Data Acquisition");
    
    // Start timers
    m_sessionTimer->start(100); // Update every 100ms
    m_statisticsTimer->start();
    
    // Initialize statistics
    m_statistics.startTime = QDateTime::currentDateTime();
    m_statistics.totalDataPoints = 0;
    m_statistics.validDataPoints = 0;
    m_statistics.invalidDataPoints = 0;
    m_statistics.validationErrors = 0;
    m_statistics.criticalErrors = 0;
    m_statistics.progressPercentage = 0.0;
    
    emit sessionStarted();
    qCInfo(testSessionLog) << "Test session started:" << m_config.sessionName;
    
    return true;
}

void TestSessionManager::pauseSession()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status != SessionStatus::Running) {
        qCWarning(testSessionLog) << "Cannot pause session: not running";
        return;
    }
    
    // Pause data acquisition
    if (m_acquisitionThread) {
        m_acquisitionThread->pauseAcquisition();
    }
    
    // Pause timers
    m_sessionTimer->stop();
    
    setSessionStatus(SessionStatus::Paused);
    setSessionPhase("Paused");
    
    emit sessionPaused();
    qCInfo(testSessionLog) << "Test session paused";
}

void TestSessionManager::resumeSession()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_status != SessionStatus::Paused) {
        qCWarning(testSessionLog) << "Cannot resume session: not paused";
        return;
    }
    
    // Resume data acquisition
    if (m_acquisitionThread) {
        m_acquisitionThread->resumeAcquisition();
    }
    
    // Resume timers
    m_sessionTimer->start(100);
    
    setSessionStatus(SessionStatus::Running);
    setSessionPhase("Data Acquisition");
    
    emit sessionResumed();
    qCInfo(testSessionLog) << "Test session resumed";
}

void TestSessionManager::stopSession()
{
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionActive()) {
        qCWarning(testSessionLog) << "Cannot stop session: no active session";
        return;
    }
    
    setSessionStatus(SessionStatus::Stopping);
    setSessionPhase("Stopping");
    
    // Stop data acquisition
    stopDataAcquisition();
    
    // Stop timers
    m_sessionTimer->stop();
    m_statisticsTimer->stop();
    m_retryTimer->stop();
    
    // Finalize session
    finalizeSession();
    
    setSessionStatus(SessionStatus::Completed);
    
    emit sessionStopped();
    emit sessionCompleted();
    qCInfo(testSessionLog) << "Test session stopped";
}

void TestSessionManager::cancelSession()
{
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionActive()) {
        qCWarning(testSessionLog) << "Cannot cancel session: no active session";
        return;
    }
    
    setSessionStatus(SessionStatus::Stopping);
    setSessionPhase("Cancelling");
    
    // Stop data acquisition immediately
    stopDataAcquisition();
    
    // Stop all timers
    m_sessionTimer->stop();
    m_statisticsTimer->stop();
    m_retryTimer->stop();
    
    // Clear session data
    m_sessionData.clear();
    
    setSessionStatus(SessionStatus::Cancelled);
    
    emit sessionCancelled();
    qCInfo(testSessionLog) << "Test session cancelled";
}

TestSessionManager::SessionStatus TestSessionManager::sessionStatus() const
{
    QMutexLocker locker(&m_mutex);
    return m_status;
}

bool TestSessionManager::isSessionActive() const
{
    QMutexLocker locker(&m_mutex);
    return m_status != SessionStatus::Idle && 
           m_status != SessionStatus::Completed && 
           m_status != SessionStatus::Failed && 
           m_status != SessionStatus::Cancelled;
}

bool TestSessionManager::isSessionRunning() const
{
    QMutexLocker locker(&m_mutex);
    return m_status == SessionStatus::Running;
}

bool TestSessionManager::isSessionPaused() const
{
    QMutexLocker locker(&m_mutex);
    return m_status == SessionStatus::Paused;
}

TestSessionManager::SessionStatistics TestSessionManager::getSessionStatistics() const
{
    QMutexLocker locker(&m_mutex);
    return m_statistics;
}

void TestSessionManager::setSessionConfig(const SessionConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_config = config;
    
    qCDebug(testSessionLog) << "Session configuration updated";
}

TestSessionManager::SessionConfig TestSessionManager::sessionConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void TestSessionManager::setDataAcquisitionThread(std::shared_ptr<DataAcquisitionThread> thread)
{
    QMutexLocker locker(&m_mutex);
    
    // Disconnect previous thread
    if (m_acquisitionThread) {
        disconnect(m_acquisitionThread.get(), nullptr, this, nullptr);
    }
    
    m_acquisitionThread = thread;
    
    // Connect new thread signals
    if (m_acquisitionThread) {
        connect(m_acquisitionThread.get(), &DataAcquisitionThread::dataReady,
                this, &TestSessionManager::onDataReceived);
        connect(m_acquisitionThread.get(), &DataAcquisitionThread::errorOccurred,
                this, &TestSessionManager::onAcquisitionError);
    }
    
    qCDebug(testSessionLog) << "Data acquisition thread set";
}

void TestSessionManager::setDataProcessor(std::shared_ptr<DataProcessor> processor)
{
    QMutexLocker locker(&m_mutex);
    
    // Disconnect previous processor
    if (m_dataProcessor) {
        disconnect(m_dataProcessor.get(), nullptr, this, nullptr);
    }
    
    m_dataProcessor = processor;
    
    // Connect new processor signals
    if (m_dataProcessor) {
        connect(m_dataProcessor.get(), &DataProcessor::dataProcessed,
                this, &TestSessionManager::onDataProcessed);
        connect(m_dataProcessor.get(), &DataProcessor::processingError,
                this, &TestSessionManager::onProcessingError);
    }
    
    qCDebug(testSessionLog) << "Data processor set";
}

void TestSessionManager::setDataValidator(std::shared_ptr<DataValidator> validator)
{
    QMutexLocker locker(&m_mutex);
    
    // Disconnect previous validator
    if (m_dataValidator) {
        disconnect(m_dataValidator.get(), nullptr, this, nullptr);
    }
    
    m_dataValidator = validator;
    
    // Connect new validator signals
    if (m_dataValidator) {
        connect(m_dataValidator.get(), &DataValidator::validationFailed,
                this, &TestSessionManager::onValidationFailed);
        connect(m_dataValidator.get(), &DataValidator::criticalValidationFailure,
                this, &TestSessionManager::onCriticalValidationFailure);
        connect(m_dataValidator.get(), &DataValidator::safetyLimitViolation,
                this, &TestSessionManager::onSafetyViolation);
    }
    
    qCDebug(testSessionLog) << "Data validator set";
}

TestDataSet TestSessionManager::getSessionData() const
{
    QMutexLocker locker(&m_mutex);
    return m_sessionData;
}

QList<TestData> TestSessionManager::getRecentData(int maxSamples) const
{
    QMutexLocker locker(&m_mutex);
    
    int totalSamples = m_sessionData.size();
    if (totalSamples <= maxSamples) {
        QList<TestData> allData;
        for (int i = 0; i < totalSamples; ++i) {
            allData.append(m_sessionData[i]);
        }
        return allData;
    }
    
    // Return the most recent samples
    QList<TestData> recentData;
    int startIndex = totalSamples - maxSamples;
    for (int i = startIndex; i < totalSamples; ++i) {
        recentData.append(m_sessionData[i]);
    }
    
    return recentData;
}

TestData TestSessionManager::getLatestData() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_sessionData.isEmpty()) {
        return TestData();
    }
    
    return m_sessionData[m_sessionData.size() - 1];
}

QString TestSessionManager::getLastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

void TestSessionManager::onDataReceived(const TestData& data)
{
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionRunning()) {
        return; // Ignore data if session is not running
    }
    
    // Process data if processor is available
    if (m_dataProcessor) {
        // Process in a separate thread to avoid blocking
        TestData processedData = m_dataProcessor->processRawData(data);
        addDataToSession(processedData);
    } else {
        // Add raw data directly
        addDataToSession(data);
    }
    
    emit sessionDataReceived(data);
}

void TestSessionManager::onDataProcessed(const TestData& processedData)
{
    // Data is already added in onDataReceived, just validate if needed
    if (m_dataValidator) {
        validateSessionData(processedData);
    }
}

void TestSessionManager::onValidationFailed(const TestData& data, const QStringList& errors)
{
    QMutexLocker locker(&m_mutex);
    
    m_statistics.validationErrors += errors.size();
    m_sessionErrors.append(errors);
    
    // Limit error list size
    while (m_sessionErrors.size() > MAX_SESSION_ERRORS) {
        m_sessionErrors.removeFirst();
    }
    
    if (m_config.pauseOnError) {
        locker.unlock();
        pauseSession();
        locker.relock();
    }
    
    QString errorSummary = QString("Validation failed: %1").arg(errors.join(", "));
    emit sessionError(errorSummary);
    
    qCWarning(testSessionLog) << "Validation failed:" << errors;
}

void TestSessionManager::onCriticalValidationFailure(const TestData& data, const QStringList& criticalErrors)
{
    QMutexLocker locker(&m_mutex);
    
    m_statistics.criticalErrors += criticalErrors.size();
    
    if (m_config.stopOnCriticalError) {
        QString error = QString("Critical validation failure: %1").arg(criticalErrors.join(", "));
        handleCriticalError(error);
    }
}

void TestSessionManager::onSafetyViolation(const TestData& data, const QStringList& violations)
{
    QMutexLocker locker(&m_mutex);
    
    QString error = QString("Safety violation detected: %1").arg(violations.join(", "));
    handleCriticalError(error);
}

void TestSessionManager::onAcquisitionError(const QString& error)
{
    handleSessionError(QString("Data acquisition error: %1").arg(error));
}

void TestSessionManager::onProcessingError(const QString& error)
{
    handleSessionError(QString("Data processing error: %1").arg(error));
}

void TestSessionManager::onSessionTimer()
{
    updateSessionProgress();
}

void TestSessionManager::onRetryTimer()
{
    retryOperation();
}

bool TestSessionManager::initializeSession()
{
    // Clear previous session data
    m_sessionData.clear();
    m_sessionErrors.clear();
    m_retryCount = 0;
    m_lastError.clear();
    
    // Set session name in data set
    if (!m_config.sessionName.isEmpty()) {
        m_sessionData.setTestName(m_config.sessionName);
    }
    
    // Create output directory if needed
    if (!m_config.outputDirectory.isEmpty()) {
        QDir dir;
        if (!dir.mkpath(m_config.outputDirectory)) {
            m_lastError = QString("Failed to create output directory: %1").arg(m_config.outputDirectory);
            qCWarning(testSessionLog) << m_lastError;
            return false;
        }
    }
    
    // Configure components
    if (m_dataProcessor && m_testConfig) {
        m_dataProcessor->setTestConfiguration(m_testConfig);
    }
    
    if (m_dataValidator && m_testConfig) {
        m_dataValidator->setTestConfiguration(m_testConfig);
    }
    
    qCDebug(testSessionLog) << "Session initialized successfully";
    return true;
}

bool TestSessionManager::startDataAcquisition()
{
    if (!m_acquisitionThread) {
        m_lastError = "Data acquisition thread not configured";
        qCWarning(testSessionLog) << m_lastError;
        return false;
    }
    
    // Configure acquisition rate from test configuration
    if (m_testConfig) {
        m_acquisitionThread->setAcquisitionRate(m_testConfig->acquisitionRate());
    }
    
    // Start acquisition
    m_acquisitionThread->startAcquisition();
    
    if (!m_acquisitionThread->isAcquisitionRunning()) {
        m_lastError = "Failed to start data acquisition";
        qCWarning(testSessionLog) << m_lastError;
        return false;
    }
    
    qCDebug(testSessionLog) << "Data acquisition started";
    return true;
}

void TestSessionManager::stopDataAcquisition()
{
    if (m_acquisitionThread && m_acquisitionThread->isAcquisitionRunning()) {
        m_acquisitionThread->stopAcquisition();
        qCDebug(testSessionLog) << "Data acquisition stopped";
    }
}

void TestSessionManager::updateSessionStatistics()
{
    QMutexLocker locker(&m_mutex);
    
    if (!isSessionActive()) {
        return;
    }
    
    // Update duration
    if (m_statistics.startTime.isValid()) {
        m_statistics.sessionDuration = m_statistics.startTime.msecsTo(QDateTime::currentDateTime()) / 1000.0;
    }
    
    // Update data rate
    if (m_statistics.sessionDuration > 0) {
        m_statistics.averageDataRate = m_statistics.totalDataPoints / m_statistics.sessionDuration;
    }
    
    emit sessionStatisticsUpdated(m_statistics);
}

void TestSessionManager::updateSessionProgress()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_testConfig) {
        return;
    }
    
    // Calculate progress based on test duration
    double testDurationMs = m_testConfig->testDuration();
    if (testDurationMs > 0 && m_statistics.startTime.isValid()) {
        double elapsedMs = m_statistics.startTime.msecsTo(QDateTime::currentDateTime());
        double progress = qMin(100.0, (elapsedMs / testDurationMs) * 100.0);
        
        if (qAbs(progress - m_statistics.progressPercentage) > 0.1) {
            m_statistics.progressPercentage = progress;
            emit sessionProgressChanged(progress);
        }
        
        // Auto-stop when test duration is reached
        if (progress >= 100.0 && isSessionRunning()) {
            locker.unlock();
            stopSession();
            return;
        }
    }
}

void TestSessionManager::finalizeSession()
{
    m_statistics.endTime = QDateTime::currentDateTime();
    
    // Save results if configured
    if (m_config.autoSaveResults) {
        QString filename = generateTimestamp() + "_session_results.json";
        QString fullPath = QDir(m_config.outputDirectory).filePath(filename);
        saveSessionResults(fullPath);
    }
    
    // Generate report if configured
    if (m_config.autoGenerateReport) {
        QString report = generateSessionReport();
        QString filename = generateTimestamp() + "_session_report.txt";
        QString fullPath = QDir(m_config.outputDirectory).filePath(filename);
        
        QFile file(fullPath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            file.write(report.toUtf8());
            qCDebug(testSessionLog) << "Session report saved to" << fullPath;
        }
    }
    
    qCDebug(testSessionLog) << "Session finalized";
}

void TestSessionManager::setSessionStatus(SessionStatus status)
{
    if (m_status != status) {
        m_status = status;
        emit sessionStatusChanged(status);
    }
}

void TestSessionManager::setSessionPhase(const QString& phase)
{
    if (m_statistics.currentPhase != phase) {
        m_statistics.currentPhase = phase;
        emit sessionPhaseChanged(phase);
    }
}

void TestSessionManager::addDataToSession(const TestData& data)
{
    m_sessionData.addDataPoint(data);
    m_statistics.totalDataPoints++;
    
    if (data.isValid()) {
        m_statistics.validDataPoints++;
    } else {
        m_statistics.invalidDataPoints++;
    }
}

void TestSessionManager::validateSessionData(const TestData& data)
{
    if (m_dataValidator) {
        auto result = m_dataValidator->validateTestData(data);
        if (!result.isValid) {
            onValidationFailed(data, result.errors);
        }
        if (!result.criticalErrors.isEmpty()) {
            onCriticalValidationFailure(data, result.criticalErrors);
        }
    }
}

void TestSessionManager::handleSessionError(const QString& error, bool isCritical)
{
    QMutexLocker locker(&m_mutex);
    
    m_lastError = error;
    m_sessionErrors.append(error);
    
    if (isCritical) {
        handleCriticalError(error);
    } else {
        emit sessionError(error);
        
        // Attempt retry if configured
        if (m_retryCount < m_config.maxRetries && isRetryableError(error)) {
            m_retryCount++;
            m_retryTimer->start(m_config.retryDelayMs);
            qCInfo(testSessionLog) << "Retrying operation" << m_retryCount << "of" << m_config.maxRetries;
        }
    }
    
    qCWarning(testSessionLog) << "Session error:" << error;
}

void TestSessionManager::handleCriticalError(const QString& error)
{
    m_lastError = error;
    
    setSessionStatus(SessionStatus::Failed);
    setSessionPhase("Failed");
    
    // Stop everything immediately
    stopDataAcquisition();
    m_sessionTimer->stop();
    m_statisticsTimer->stop();
    
    emit criticalSessionError(error);
    emit sessionFailed(error);
    
    qCCritical(testSessionLog) << "Critical session error:" << error;
}

void TestSessionManager::retryOperation()
{
    // Implementation depends on what operation failed
    // For now, just log the retry attempt
    qCInfo(testSessionLog) << "Retry operation executed";
}

QString TestSessionManager::generateSessionReport() const
{
    QString report;
    report += QString("=== Hydraulic Test Session Report ===\n");
    report += QString("Session Name: %1\n").arg(m_config.sessionName);
    report += QString("Start Time: %1\n").arg(m_statistics.startTime.toString());
    report += QString("End Time: %1\n").arg(m_statistics.endTime.toString());
    report += QString("Duration: %1 seconds\n").arg(m_statistics.sessionDuration);
    report += QString("Total Data Points: %1\n").arg(m_statistics.totalDataPoints);
    report += QString("Valid Data Points: %1\n").arg(m_statistics.validDataPoints);
    report += QString("Invalid Data Points: %1\n").arg(m_statistics.invalidDataPoints);
    report += QString("Validation Errors: %1\n").arg(m_statistics.validationErrors);
    report += QString("Critical Errors: %1\n").arg(m_statistics.criticalErrors);
    report += QString("Average Data Rate: %1 Hz\n").arg(m_statistics.averageDataRate);
    report += QString("Final Progress: %1%\n").arg(m_statistics.progressPercentage);
    
    if (!m_sessionErrors.isEmpty()) {
        report += QString("\n=== Session Errors ===\n");
        for (const QString& error : m_sessionErrors) {
            report += QString("- %1\n").arg(error);
        }
    }
    
    return report;
}

QString TestSessionManager::generateTimestamp() const
{
    return QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
}

bool TestSessionManager::isRetryableError(const QString& error) const
{
    // Define which errors are retryable
    return error.contains("timeout") ||
           error.contains("connection") ||
           error.contains("temporary");
}

bool TestSessionManager::saveSessionResults(const QString& filename) const
{
    QJsonObject sessionJson;

    // Session metadata
    sessionJson["sessionName"] = m_config.sessionName;
    sessionJson["sessionDescription"] = m_config.sessionDescription;
    sessionJson["startTime"] = m_statistics.startTime.toString(Qt::ISODate);
    sessionJson["endTime"] = m_statistics.endTime.toString(Qt::ISODate);
    sessionJson["duration"] = m_statistics.sessionDuration;
    sessionJson["status"] = static_cast<int>(m_status);
    sessionJson["currentPhase"] = m_statistics.currentPhase;

    // Session statistics
    QJsonObject statsJson;
    statsJson["totalDataPoints"] = m_statistics.totalDataPoints;
    statsJson["validDataPoints"] = m_statistics.validDataPoints;
    statsJson["invalidDataPoints"] = m_statistics.invalidDataPoints;
    statsJson["validationErrors"] = m_statistics.validationErrors;
    statsJson["criticalErrors"] = m_statistics.criticalErrors;
    statsJson["averageDataRate"] = m_statistics.averageDataRate;
    statsJson["progressPercentage"] = m_statistics.progressPercentage;
    sessionJson["statistics"] = statsJson;

    // Session configuration
    QJsonObject configJson;
    configJson["autoSaveResults"] = m_config.autoSaveResults;
    configJson["autoGenerateReport"] = m_config.autoGenerateReport;
    configJson["pauseOnError"] = m_config.pauseOnError;
    configJson["stopOnCriticalError"] = m_config.stopOnCriticalError;
    configJson["outputDirectory"] = m_config.outputDirectory;
    configJson["maxRetries"] = m_config.maxRetries;
    configJson["retryDelayMs"] = m_config.retryDelayMs;
    sessionJson["configuration"] = configJson;

    // Session errors
    QJsonArray errorsArray;
    for (const QString& error : m_sessionErrors) {
        errorsArray.append(error);
    }
    sessionJson["errors"] = errorsArray;

    // Session data summary (not full data to keep file size manageable)
    QJsonObject dataSummary;
    dataSummary["dataPointCount"] = m_sessionData.size();
    dataSummary["timeRange"] = QString("%1 to %2")
                                  .arg(m_sessionData.startTime().toString(Qt::ISODate))
                                  .arg(m_sessionData.endTime().toString(Qt::ISODate));

    // Add basic statistics
    if (m_sessionData.size() > 0) {
        TestData avgData = m_sessionData.averageValues();
        TestData minData = m_sessionData.minimumValues();
        TestData maxData = m_sessionData.maximumValues();

        QJsonObject basicStats;
        basicStats["averagePressureCPA"] = avgData.pressureCPA();
        basicStats["averagePressureCPB"] = avgData.pressureCPB();
        basicStats["averageFlowRate"] = avgData.flowRate();
        basicStats["averageTemperature"] = avgData.temperatureFluid();
        basicStats["minPressureCPA"] = minData.pressureCPA();
        basicStats["maxPressureCPA"] = maxData.pressureCPA();
        basicStats["minPressureCPB"] = minData.pressureCPB();
        basicStats["maxPressureCPB"] = maxData.pressureCPB();
        basicStats["duration"] = m_sessionData.duration();

        dataSummary["basicStatistics"] = basicStats;
    }
    sessionJson["dataSummary"] = dataSummary;

    // Write to file
    QJsonDocument doc(sessionJson);
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        qCWarning(testSessionLog) << "Failed to open file for writing:" << filename;
        return false;
    }

    qint64 bytesWritten = file.write(doc.toJson());
    if (bytesWritten == -1) {
        qCWarning(testSessionLog) << "Failed to write session results to file:" << filename;
        return false;
    }

    qCDebug(testSessionLog) << "Session results saved to" << filename << "(" << bytesWritten << "bytes)";
    return true;
}
