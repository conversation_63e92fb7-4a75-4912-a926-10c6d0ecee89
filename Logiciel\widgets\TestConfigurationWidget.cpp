#include "TestConfigurationWidget.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

Q_LOGGING_CATEGORY(testConfigWidgetLog, "hydraulic.widgets.testconfig")

// Static constants
const QString TestConfigurationWidget::PRESETS_FILE_NAME = "test_presets.json";
const QStringList TestConfigurationWidget::DEFAULT_PRESET_NAMES = {
    "Default Configuration",
    "Pressure Test",
    "Flow Test", 
    "Temperature Test",
    "Cycle Test"
};

TestConfigurationWidget::TestConfigurationWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_tabWidget(nullptr)
    , m_testConfig(std::make_shared<TestConfiguration>())
    , m_updating(false)
{
    setupUI();
    loadPresetsFromFile();
    updateFromConfiguration();
    
    qCDebug(testConfigWidgetLog) << "TestConfigurationWidget created";
}

void TestConfigurationWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // Create tab widget
    m_tabWidget = new QTabWidget(this);
    m_mainLayout->addWidget(m_tabWidget);
    
    // Setup tabs
    setupBasicTab();
    setupSafetyTab();
    setupAcquisitionTab();
    setupExecutionTab();
    setupPresetTab();
    
    // Setup button panel
    setupButtonPanel();
    
    // Connect signals
    connect(m_testConfig.get(), &TestConfiguration::configurationChanged,
            this, &TestConfigurationWidget::configurationChanged);
}

void TestConfigurationWidget::setupBasicTab()
{
    m_basicTab = new QWidget();
    QFormLayout* layout = new QFormLayout(m_basicTab);
    
    // Name and description
    m_nameEdit = new QLineEdit();
    m_nameEdit->setPlaceholderText("Enter configuration name");
    connect(m_nameEdit, &QLineEdit::textChanged, this, &TestConfigurationWidget::onNameChanged);
    layout->addRow("Name:", m_nameEdit);
    
    m_descriptionEdit = new QTextEdit();
    m_descriptionEdit->setMaximumHeight(80);
    m_descriptionEdit->setPlaceholderText("Enter configuration description");
    connect(m_descriptionEdit, &QTextEdit::textChanged, this, &TestConfigurationWidget::onDescriptionChanged);
    layout->addRow("Description:", m_descriptionEdit);
    
    // Pressure range
    QHBoxLayout* pressureLayout = new QHBoxLayout();
    m_pressureMinSpinBox = new QDoubleSpinBox();
    m_pressureMinSpinBox->setRange(0.0, 500.0);
    m_pressureMinSpinBox->setSuffix(" bar");
    m_pressureMinSpinBox->setDecimals(1);
    connect(m_pressureMinSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onPressureRangeChanged);
    
    m_pressureMaxSpinBox = new QDoubleSpinBox();
    m_pressureMaxSpinBox->setRange(0.0, 500.0);
    m_pressureMaxSpinBox->setSuffix(" bar");
    m_pressureMaxSpinBox->setDecimals(1);
    connect(m_pressureMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onPressureRangeChanged);
    
    pressureLayout->addWidget(new QLabel("Min:"));
    pressureLayout->addWidget(m_pressureMinSpinBox);
    pressureLayout->addWidget(new QLabel("Max:"));
    pressureLayout->addWidget(m_pressureMaxSpinBox);
    layout->addRow("Pressure Range:", pressureLayout);
    
    // Flow range
    QHBoxLayout* flowLayout = new QHBoxLayout();
    m_flowMinSpinBox = new QDoubleSpinBox();
    m_flowMinSpinBox->setRange(-200.0, 200.0);
    m_flowMinSpinBox->setSuffix(" L/min");
    m_flowMinSpinBox->setDecimals(1);
    connect(m_flowMinSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onFlowRangeChanged);
    
    m_flowMaxSpinBox = new QDoubleSpinBox();
    m_flowMaxSpinBox->setRange(-200.0, 200.0);
    m_flowMaxSpinBox->setSuffix(" L/min");
    m_flowMaxSpinBox->setDecimals(1);
    connect(m_flowMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onFlowRangeChanged);
    
    flowLayout->addWidget(new QLabel("Min:"));
    flowLayout->addWidget(m_flowMinSpinBox);
    flowLayout->addWidget(new QLabel("Max:"));
    flowLayout->addWidget(m_flowMaxSpinBox);
    layout->addRow("Flow Range:", flowLayout);
    
    // Temperature max
    m_temperatureMaxSpinBox = new QDoubleSpinBox();
    m_temperatureMaxSpinBox->setRange(0.0, 150.0);
    m_temperatureMaxSpinBox->setSuffix(" °C");
    m_temperatureMaxSpinBox->setDecimals(1);
    connect(m_temperatureMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onTemperatureMaxChanged);
    layout->addRow("Max Temperature:", m_temperatureMaxSpinBox);
    
    // Acquisition rate
    m_acquisitionRateSpinBox = new QSpinBox();
    m_acquisitionRateSpinBox->setRange(1, 10000);
    m_acquisitionRateSpinBox->setSuffix(" Hz");
    connect(m_acquisitionRateSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onAcquisitionRateChanged);
    layout->addRow("Acquisition Rate:", m_acquisitionRateSpinBox);
    
    // Test duration
    m_testDurationSpinBox = new QSpinBox();
    m_testDurationSpinBox->setRange(1000, 3600000);
    m_testDurationSpinBox->setSuffix(" ms");
    connect(m_testDurationSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onTestDurationChanged);
    layout->addRow("Test Duration:", m_testDurationSpinBox);
    
    m_tabWidget->addTab(m_basicTab, "Basic Parameters");
}

void TestConfigurationWidget::setupSafetyTab()
{
    m_safetyTab = new QWidget();
    QFormLayout* layout = new QFormLayout(m_safetyTab);
    
    // Safety thresholds
    m_maxPressureSpinBox = new QDoubleSpinBox();
    m_maxPressureSpinBox->setRange(0.0, 500.0);
    m_maxPressureSpinBox->setSuffix(" bar");
    m_maxPressureSpinBox->setDecimals(1);
    connect(m_maxPressureSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Max Pressure:", m_maxPressureSpinBox);
    
    m_maxTemperatureSpinBox = new QDoubleSpinBox();
    m_maxTemperatureSpinBox->setRange(0.0, 150.0);
    m_maxTemperatureSpinBox->setSuffix(" °C");
    m_maxTemperatureSpinBox->setDecimals(1);
    connect(m_maxTemperatureSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Max Temperature:", m_maxTemperatureSpinBox);
    
    m_maxFlowSpinBox = new QDoubleSpinBox();
    m_maxFlowSpinBox->setRange(0.0, 200.0);
    m_maxFlowSpinBox->setSuffix(" L/min");
    m_maxFlowSpinBox->setDecimals(1);
    connect(m_maxFlowSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Max Flow:", m_maxFlowSpinBox);
    
    m_maxVelocitySpinBox = new QDoubleSpinBox();
    m_maxVelocitySpinBox->setRange(0.0, 500.0);
    m_maxVelocitySpinBox->setSuffix(" mm/s");
    m_maxVelocitySpinBox->setDecimals(1);
    connect(m_maxVelocitySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Max Velocity:", m_maxVelocitySpinBox);
    
    m_maxForceSpinBox = new QDoubleSpinBox();
    m_maxForceSpinBox->setRange(0.0, 100000.0);
    m_maxForceSpinBox->setSuffix(" N");
    m_maxForceSpinBox->setDecimals(0);
    connect(m_maxForceSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Max Force:", m_maxForceSpinBox);
    
    m_minPressureSpinBox = new QDoubleSpinBox();
    m_minPressureSpinBox->setRange(0.0, 100.0);
    m_minPressureSpinBox->setSuffix(" bar");
    m_minPressureSpinBox->setDecimals(1);
    connect(m_minPressureSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Min Pressure:", m_minPressureSpinBox);
    
    m_leakageThresholdSpinBox = new QDoubleSpinBox();
    m_leakageThresholdSpinBox->setRange(0.0, 10.0);
    m_leakageThresholdSpinBox->setSuffix(" L/min");
    m_leakageThresholdSpinBox->setDecimals(2);
    connect(m_leakageThresholdSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Leakage Threshold:", m_leakageThresholdSpinBox);
    
    m_emergencyStopDelaySpinBox = new QSpinBox();
    m_emergencyStopDelaySpinBox->setRange(100, 5000);
    m_emergencyStopDelaySpinBox->setSuffix(" ms");
    connect(m_emergencyStopDelaySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onSafetyThresholdChanged);
    layout->addRow("Emergency Stop Delay:", m_emergencyStopDelaySpinBox);
    
    m_tabWidget->addTab(m_safetyTab, "Safety Thresholds");
}

void TestConfigurationWidget::setupAcquisitionTab()
{
    m_acquisitionTab = new QWidget();
    QFormLayout* layout = new QFormLayout(m_acquisitionTab);
    
    // Acquisition configuration
    m_sampleRateSpinBox = new QSpinBox();
    m_sampleRateSpinBox->setRange(1, 10000);
    m_sampleRateSpinBox->setSuffix(" Hz");
    connect(m_sampleRateSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Sample Rate:", m_sampleRateSpinBox);
    
    m_bufferSizeSpinBox = new QSpinBox();
    m_bufferSizeSpinBox->setRange(100, 100000);
    m_bufferSizeSpinBox->setSuffix(" samples");
    connect(m_bufferSizeSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Buffer Size:", m_bufferSizeSpinBox);
    
    m_enableFilteringCheckBox = new QCheckBox();
    connect(m_enableFilteringCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Enable Filtering:", m_enableFilteringCheckBox);
    
    m_filterCutoffSpinBox = new QDoubleSpinBox();
    m_filterCutoffSpinBox->setRange(0.1, 1000.0);
    m_filterCutoffSpinBox->setSuffix(" Hz");
    m_filterCutoffSpinBox->setDecimals(1);
    connect(m_filterCutoffSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Filter Cutoff:", m_filterCutoffSpinBox);
    
    m_enableValidationCheckBox = new QCheckBox();
    connect(m_enableValidationCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Enable Validation:", m_enableValidationCheckBox);
    
    m_enableLoggingCheckBox = new QCheckBox();
    connect(m_enableLoggingCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Enable Logging:", m_enableLoggingCheckBox);
    
    m_logFormatComboBox = new QComboBox();
    m_logFormatComboBox->addItems({"CSV", "JSON", "Binary"});
    connect(m_logFormatComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TestConfigurationWidget::onAcquisitionConfigChanged);
    layout->addRow("Log Format:", m_logFormatComboBox);
    
    m_tabWidget->addTab(m_acquisitionTab, "Data Acquisition");
}

void TestConfigurationWidget::setupExecutionTab()
{
    m_executionTab = new QWidget();
    QFormLayout* layout = new QFormLayout(m_executionTab);

    // Execution configuration
    m_autoStartCheckBox = new QCheckBox();
    connect(m_autoStartCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Auto Start:", m_autoStartCheckBox);

    m_autoStopCheckBox = new QCheckBox();
    connect(m_autoStopCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Auto Stop:", m_autoStopCheckBox);

    m_pauseOnErrorCheckBox = new QCheckBox();
    connect(m_pauseOnErrorCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Pause on Error:", m_pauseOnErrorCheckBox);

    m_saveResultsCheckBox = new QCheckBox();
    connect(m_saveResultsCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Save Results:", m_saveResultsCheckBox);

    m_generateReportCheckBox = new QCheckBox();
    connect(m_generateReportCheckBox, &QCheckBox::toggled,
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Generate Report:", m_generateReportCheckBox);

    m_reportFormatComboBox = new QComboBox();
    m_reportFormatComboBox->addItems({"PDF", "HTML", "CSV"});
    connect(m_reportFormatComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Report Format:", m_reportFormatComboBox);

    m_maxRetriesSpinBox = new QSpinBox();
    m_maxRetriesSpinBox->setRange(0, 10);
    connect(m_maxRetriesSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Max Retries:", m_maxRetriesSpinBox);

    m_retryDelaySpinBox = new QSpinBox();
    m_retryDelaySpinBox->setRange(100, 10000);
    m_retryDelaySpinBox->setSuffix(" ms");
    connect(m_retryDelaySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &TestConfigurationWidget::onExecutionConfigChanged);
    layout->addRow("Retry Delay:", m_retryDelaySpinBox);

    m_tabWidget->addTab(m_executionTab, "Execution");
}

void TestConfigurationWidget::setupPresetTab()
{
    m_presetTab = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(m_presetTab);

    // Preset selection
    QFormLayout* presetLayout = new QFormLayout();

    m_presetComboBox = new QComboBox();
    presetLayout->addRow("Available Presets:", m_presetComboBox);

    m_newPresetNameEdit = new QLineEdit();
    m_newPresetNameEdit->setPlaceholderText("Enter new preset name");
    presetLayout->addRow("New Preset Name:", m_newPresetNameEdit);

    layout->addLayout(presetLayout);

    // Preset buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    m_loadPresetButton = new QPushButton("Load Preset");
    connect(m_loadPresetButton, &QPushButton::clicked, this, &TestConfigurationWidget::onLoadPresetClicked);
    buttonLayout->addWidget(m_loadPresetButton);

    m_savePresetButton = new QPushButton("Save Preset");
    connect(m_savePresetButton, &QPushButton::clicked, this, &TestConfigurationWidget::onSavePresetClicked);
    buttonLayout->addWidget(m_savePresetButton);

    m_createPresetButton = new QPushButton("Create Preset");
    connect(m_createPresetButton, &QPushButton::clicked, this, &TestConfigurationWidget::onCreatePresetClicked);
    buttonLayout->addWidget(m_createPresetButton);

    m_deletePresetButton = new QPushButton("Delete Preset");
    connect(m_deletePresetButton, &QPushButton::clicked, this, [this]() {
        QString presetName = m_presetComboBox->currentText();
        if (!presetName.isEmpty() && m_presets.contains(presetName)) {
            m_presets.remove(presetName);
            m_presetComboBox->removeItem(m_presetComboBox->currentIndex());
            savePresetsToFile();
        }
    });
    buttonLayout->addWidget(m_deletePresetButton);

    layout->addLayout(buttonLayout);

    m_tabWidget->addTab(m_presetTab, "Presets");
}

void TestConfigurationWidget::setupButtonPanel()
{
    m_buttonPanel = new QWidget();
    QHBoxLayout* layout = new QHBoxLayout(m_buttonPanel);

    m_applyButton = new QPushButton("Apply");
    m_applyButton->setStyleSheet("QPushButton { background-color: green; color: white; font-weight: bold; }");
    connect(m_applyButton, &QPushButton::clicked, this, &TestConfigurationWidget::onApplyClicked);
    layout->addWidget(m_applyButton);

    m_resetButton = new QPushButton("Reset");
    connect(m_resetButton, &QPushButton::clicked, this, &TestConfigurationWidget::onResetClicked);
    layout->addWidget(m_resetButton);

    m_validateButton = new QPushButton("Validate");
    connect(m_validateButton, &QPushButton::clicked, this, &TestConfigurationWidget::onValidateClicked);
    layout->addWidget(m_validateButton);

    layout->addStretch();

    m_validationStatusLabel = new QLabel("Ready");
    layout->addWidget(m_validationStatusLabel);

    m_validationProgressBar = new QProgressBar();
    m_validationProgressBar->setVisible(false);
    layout->addWidget(m_validationProgressBar);

    m_mainLayout->addWidget(m_buttonPanel);
}

// Slot implementations
void TestConfigurationWidget::onNameChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setName(m_nameEdit->text());
    }
}

void TestConfigurationWidget::onDescriptionChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setDescription(m_descriptionEdit->toPlainText());
    }
}

void TestConfigurationWidget::onPressureRangeChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setPressureMin(m_pressureMinSpinBox->value());
        m_testConfig->setPressureMax(m_pressureMaxSpinBox->value());
    }
}

void TestConfigurationWidget::onFlowRangeChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setFlowMin(m_flowMinSpinBox->value());
        m_testConfig->setFlowMax(m_flowMaxSpinBox->value());
    }
}

void TestConfigurationWidget::onTemperatureMaxChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setTemperatureMax(m_temperatureMaxSpinBox->value());
    }
}

void TestConfigurationWidget::onAcquisitionRateChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setAcquisitionRate(m_acquisitionRateSpinBox->value());
    }
}

void TestConfigurationWidget::onTestDurationChanged()
{
    if (!m_updating && m_testConfig) {
        m_testConfig->setTestDuration(m_testDurationSpinBox->value());
    }
}

void TestConfigurationWidget::onSafetyThresholdChanged()
{
    if (!m_updating && m_testConfig) {
        TestConfiguration::SafetyThresholds thresholds = m_testConfig->safetyThresholds();
        thresholds.maxPressure = m_maxPressureSpinBox->value();
        thresholds.maxTemperature = m_maxTemperatureSpinBox->value();
        thresholds.maxFlow = m_maxFlowSpinBox->value();
        thresholds.maxVelocity = m_maxVelocitySpinBox->value();
        thresholds.maxForce = m_maxForceSpinBox->value();
        thresholds.minPressure = m_minPressureSpinBox->value();
        thresholds.leakageThreshold = m_leakageThresholdSpinBox->value();
        thresholds.emergencyStopDelay = m_emergencyStopDelaySpinBox->value();
        m_testConfig->setSafetyThresholds(thresholds);
    }
}

void TestConfigurationWidget::onAcquisitionConfigChanged()
{
    if (!m_updating && m_testConfig) {
        TestConfiguration::AcquisitionConfig config = m_testConfig->acquisitionConfig();
        config.sampleRate = m_sampleRateSpinBox->value();
        config.bufferSize = m_bufferSizeSpinBox->value();
        config.enableFiltering = m_enableFilteringCheckBox->isChecked();
        config.filterCutoff = m_filterCutoffSpinBox->value();
        config.enableValidation = m_enableValidationCheckBox->isChecked();
        config.enableLogging = m_enableLoggingCheckBox->isChecked();
        config.logFormat = m_logFormatComboBox->currentText();
        m_testConfig->setAcquisitionConfig(config);
    }
}

void TestConfigurationWidget::onExecutionConfigChanged()
{
    if (!m_updating && m_testConfig) {
        TestConfiguration::ExecutionConfig config = m_testConfig->executionConfig();
        config.autoStart = m_autoStartCheckBox->isChecked();
        config.autoStop = m_autoStopCheckBox->isChecked();
        config.pauseOnError = m_pauseOnErrorCheckBox->isChecked();
        config.saveResults = m_saveResultsCheckBox->isChecked();
        config.generateReport = m_generateReportCheckBox->isChecked();
        config.reportFormat = m_reportFormatComboBox->currentText();
        config.maxRetries = m_maxRetriesSpinBox->value();
        config.retryDelay = m_retryDelaySpinBox->value();
        m_testConfig->setExecutionConfig(config);
    }
}

void TestConfigurationWidget::onApplyClicked()
{
    validateConfiguration();
    if (m_validationErrors.isEmpty()) {
        emit configurationChanged();
        m_validationStatusLabel->setText("Configuration applied successfully");
        m_validationStatusLabel->setStyleSheet("color: green;");
    } else {
        m_validationStatusLabel->setText("Configuration validation failed");
        m_validationStatusLabel->setStyleSheet("color: red;");
    }
}

void TestConfigurationWidget::onResetClicked()
{
    if (QMessageBox::question(this, "Reset Configuration",
                             "Are you sure you want to reset to default values?") == QMessageBox::Yes) {
        m_testConfig = std::make_shared<TestConfiguration>();
        updateFromConfiguration();
        m_validationStatusLabel->setText("Configuration reset to defaults");
        m_validationStatusLabel->setStyleSheet("color: blue;");
    }
}

void TestConfigurationWidget::onValidateClicked()
{
    validateConfiguration();
}

void TestConfigurationWidget::onLoadPresetClicked()
{
    QString presetName = m_presetComboBox->currentText();
    if (!presetName.isEmpty() && m_presets.contains(presetName)) {
        *m_testConfig = m_presets[presetName];
        updateFromConfiguration();
        m_validationStatusLabel->setText(QString("Loaded preset: %1").arg(presetName));
        m_validationStatusLabel->setStyleSheet("color: blue;");
    }
}

void TestConfigurationWidget::onSavePresetClicked()
{
    QString presetName = m_presetComboBox->currentText();
    if (!presetName.isEmpty()) {
        TestConfiguration presetConfig = *m_testConfig;
        m_presets[presetName] = presetConfig;
        savePresetsToFile();
        m_validationStatusLabel->setText(QString("Saved preset: %1").arg(presetName));
        m_validationStatusLabel->setStyleSheet("color: green;");
    }
}

void TestConfigurationWidget::onCreatePresetClicked()
{
    QString presetName = m_newPresetNameEdit->text().trimmed();
    if (!presetName.isEmpty()) {
        TestConfiguration presetConfig = *m_testConfig;
        m_presets[presetName] = presetConfig;
        m_presetComboBox->addItem(presetName);
        m_presetComboBox->setCurrentText(presetName);
        m_newPresetNameEdit->clear();
        savePresetsToFile();
        m_validationStatusLabel->setText(QString("Created preset: %1").arg(presetName));
        m_validationStatusLabel->setStyleSheet("color: green;");
    }
}

// Public methods
void TestConfigurationWidget::setTestConfiguration(std::shared_ptr<TestConfiguration> config)
{
    if (config) {
        m_testConfig = config;
        updateFromConfiguration();
    }
}

std::shared_ptr<TestConfiguration> TestConfigurationWidget::testConfiguration() const
{
    return m_testConfig;
}

void TestConfigurationWidget::validateConfiguration()
{
    if (!m_testConfig) {
        m_validationErrors = QStringList() << "No configuration loaded";
        emit configurationValidated(false, m_validationErrors);
        return;
    }

    m_validationErrors = m_testConfig->validate();

    if (m_validationErrors.isEmpty()) {
        m_validationStatusLabel->setText("Configuration is valid");
        m_validationStatusLabel->setStyleSheet("color: green;");
        emit configurationValidated(true, m_validationErrors);
    } else {
        QString errorText = QString("Validation errors: %1").arg(m_validationErrors.join(", "));
        m_validationStatusLabel->setText(errorText);
        m_validationStatusLabel->setStyleSheet("color: red;");
        emit configurationValidated(false, m_validationErrors);

        QMessageBox::warning(this, "Configuration Validation",
                           QString("Configuration validation failed:\n\n%1").arg(m_validationErrors.join("\n")));
    }
}

void TestConfigurationWidget::updateFromConfiguration()
{
    if (!m_testConfig) {
        return;
    }

    m_updating = true;

    // Basic parameters
    m_nameEdit->setText(m_testConfig->name());
    m_descriptionEdit->setPlainText(m_testConfig->description());
    m_pressureMinSpinBox->setValue(m_testConfig->pressureMin());
    m_pressureMaxSpinBox->setValue(m_testConfig->pressureMax());
    m_flowMinSpinBox->setValue(m_testConfig->flowMin());
    m_flowMaxSpinBox->setValue(m_testConfig->flowMax());
    m_temperatureMaxSpinBox->setValue(m_testConfig->temperatureMax());
    m_acquisitionRateSpinBox->setValue(m_testConfig->acquisitionRate());
    m_testDurationSpinBox->setValue(m_testConfig->testDuration());

    // Safety thresholds
    const auto& safety = m_testConfig->safetyThresholds();
    m_maxPressureSpinBox->setValue(safety.maxPressure);
    m_maxTemperatureSpinBox->setValue(safety.maxTemperature);
    m_maxFlowSpinBox->setValue(safety.maxFlow);
    m_maxVelocitySpinBox->setValue(safety.maxVelocity);
    m_maxForceSpinBox->setValue(safety.maxForce);
    m_minPressureSpinBox->setValue(safety.minPressure);
    m_leakageThresholdSpinBox->setValue(safety.leakageThreshold);
    m_emergencyStopDelaySpinBox->setValue(safety.emergencyStopDelay);

    // Acquisition config
    const auto& acquisition = m_testConfig->acquisitionConfig();
    m_sampleRateSpinBox->setValue(acquisition.sampleRate);
    m_bufferSizeSpinBox->setValue(acquisition.bufferSize);
    m_enableFilteringCheckBox->setChecked(acquisition.enableFiltering);
    m_filterCutoffSpinBox->setValue(acquisition.filterCutoff);
    m_enableValidationCheckBox->setChecked(acquisition.enableValidation);
    m_enableLoggingCheckBox->setChecked(acquisition.enableLogging);
    m_logFormatComboBox->setCurrentText(acquisition.logFormat);

    // Execution config
    const auto& execution = m_testConfig->executionConfig();
    m_autoStartCheckBox->setChecked(execution.autoStart);
    m_autoStopCheckBox->setChecked(execution.autoStop);
    m_pauseOnErrorCheckBox->setChecked(execution.pauseOnError);
    m_saveResultsCheckBox->setChecked(execution.saveResults);
    m_generateReportCheckBox->setChecked(execution.generateReport);
    m_reportFormatComboBox->setCurrentText(execution.reportFormat);
    m_maxRetriesSpinBox->setValue(execution.maxRetries);
    m_retryDelaySpinBox->setValue(execution.retryDelay);

    m_updating = false;
}

void TestConfigurationWidget::loadPresetsFromFile()
{
    QString presetsPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(presetsPath);
    QString filename = QDir(presetsPath).filePath(PRESETS_FILE_NAME);

    QFile file(filename);
    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        if (doc.isObject()) {
            QJsonObject presetsObj = doc.object();
            for (auto it = presetsObj.begin(); it != presetsObj.end(); ++it) {
                TestConfiguration config;
                config.fromJson(it.value().toObject());
                m_presets[it.key()] = config;
            }
        }
    }

    // Add default presets if none exist
    if (m_presets.isEmpty()) {
        createDefaultPresets();
    }

    // Update combo box
    m_presetComboBox->clear();
    m_presetComboBox->addItems(m_presets.keys());
}

void TestConfigurationWidget::savePresetsToFile()
{
    QString presetsPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(presetsPath);
    QString filename = QDir(presetsPath).filePath(PRESETS_FILE_NAME);

    QJsonObject presetsObj;
    for (auto it = m_presets.begin(); it != m_presets.end(); ++it) {
        presetsObj[it.key()] = it.value().toJson();
    }

    QFile file(filename);
    if (file.open(QIODevice::WriteOnly)) {
        QJsonDocument doc(presetsObj);
        file.write(doc.toJson());
    }
}

void TestConfigurationWidget::createDefaultPresets()
{
    // Create default presets
    m_presets["Default"] = TestConfiguration::createDefault();
    m_presets["Pressure Test"] = TestConfiguration::createPressureTest();
    m_presets["Flow Test"] = TestConfiguration::createFlowTest();
    m_presets["Leakage Test"] = TestConfiguration::createLeakageTest();
    m_presets["Cycle Test"] = TestConfiguration::createCycleTest();
}
