#ifndef FLOWMETER_H
#define FLOWMETER_H

#include "SimulatedSensor.h"

/**
 * @brief Simulated hydraulic flow meter
 * 
 * This class simulates a hydraulic flow meter with realistic
 * flow readings for hydraulic system testing.
 */
class FlowMeter : public SimulatedSensor
{
    Q_OBJECT
    
public:
    enum class FlowDirection {
        Bidirectional,
        Forward,
        Reverse
    };
    
    explicit FlowMeter(QObject *parent = nullptr);
    
    // HardwareInterface implementation
    QString deviceName() const override;
    QString deviceVersion() const override;
    DeviceType deviceType() const override;
    
    // SimulatedSensor implementation
    double generateRealisticValue() override;
    QString getPrimaryParameter() const override;
    QStringList getAllParameters() const override;
    
    // Flow-specific methods
    void setFlowDirection(FlowDirection direction);
    FlowDirection flowDirection() const { return m_flowDirection; }
    
    // Flow test scenarios
    void simulateFlowTest(double targetFlow, int durationMs = 5000);
    void simulateFlowRamp(double startFlow, double endFlow, int rampTimeMs = 3000);
    void simulateFlowStep(double stepFlow, int stepDurationMs = 1000);
    void simulateFlowPulse(double baseFlow, double pulseAmplitude, int pulseFrequencyHz = 1);
    
    // Flow unit conversion
    static double lpmToGpm(double lpm);  // Liters per minute to Gallons per minute
    static double gpmToLpm(double gpm);
    static double lpmToCms(double lpm);  // Liters per minute to Cubic meters per second
    static double cmsToLpm(double cms);
    
    // Flow calculations
    double calculateVolumetricEfficiency(double theoreticalFlow) const;
    double calculateReynoldsNumber(double pipeDiameter, double kinematicViscosity) const;
    
signals:
    void flowTestCompleted();
    void flowRampCompleted();
    void flowStepCompleted();
    void flowDirectionChanged(FlowDirection direction);
    void cavitationDetected();
    
private slots:
    void onFlowTestTimer();
    void onFlowRampTimer();
    void onFlowStepTimer();
    void onFlowPulseTimer();
    
private:
    FlowDirection m_flowDirection;
    
    // Test simulation
    QTimer* m_flowTestTimer;
    QTimer* m_flowRampTimer;
    QTimer* m_flowStepTimer;
    QTimer* m_flowPulseTimer;
    
    enum class FlowTestMode {
        Normal,
        FlowTest,
        FlowRamp,
        FlowStep,
        FlowPulse
    };
    
    FlowTestMode m_currentFlowTestMode;
    double m_testTargetFlow;
    double m_testStartFlow;
    double m_testEndFlow;
    double m_testBaseFlow;
    double m_testPulseAmplitude;
    int m_testDuration;
    int m_rampTime;
    int m_stepDuration;
    int m_pulseFrequency;
    QDateTime m_flowTestStartTime;
    
    void updateFlowTestMode();
    double generateFlowTestValue();
    double generateFlowRampValue();
    double generateFlowStepValue();
    double generateFlowPulseValue();
    double generateNormalFlowValue();
    
    // Realistic flow patterns
    double generateTurbulentFlow(double baseFlow);
    double generateLaminarFlow(double baseFlow);
    double addFlowNoise(double baseFlow);
    double simulateCavitation(double baseFlow);
    
    // Flow characteristics
    bool isCavitationCondition() const;
    double getFlowCoefficient() const;
    
    static const double DEFAULT_MAX_FLOW; // L/min
    static const double DEFAULT_MIN_FLOW; // L/min
    static const double CAVITATION_THRESHOLD; // Pressure threshold for cavitation
};

#endif // FLOWMETER_H
