# Sauvegarde et Restauration

<div className="role-controleur">
<strong>🔒 Fonctionnalité Contrôleur Uniquement</strong><br/>
Cette section est réservée aux utilisateurs ayant le rôle "Contrôleur". Les opérateurs n'ont pas accès à ces fonctionnalités.
</div>

## Vue d'ensemble

Le module **Sauvegarde et Restauration** permet aux contrôleurs de protéger les données du système FluidMotion Labs. Il offre des outils complets pour créer, gérer et restaurer des sauvegardes de la base de données.

## Accès au Module

### Navigation
- **Menu utilisateur** (clic sur l'avatar en haut à droite)
- **"Sauvegarde & Restauration"** dans le sous-menu d'administration
- Visible uniquement pour les contrôleurs
- Accès direct via <kbd>Alt</kbd> + <kbd>S</kbd>

### Vérification des Permissions
Le système vérifie automatiquement :
- Rôle utilisateur "Contrôleur"
- Redirection automatique si accès non autorisé
- Message d'erreur explicite

## Interface du Module

### En-tête du Module

#### Titre et Actions Principales
- **Titre** : "Gestion des Sauvegardes"
- **"Créer une Sauvegarde"** (bleu) : Nouvelle sauvegarde immédiate
- **"Importer une Sauvegarde"** (vert) : Upload d'un fichier externe

### Informations sur l'Espace Disque

<div className="info-box">

**Surveillance de l'Espace**
- **Espace Libre** : Affiché en GB avec code couleur
- **Espace Total** : Capacité totale du système
- **Alertes** : Avertissement si espace insuffisant

</div>

#### Codes Couleur
- **Vert** : Espace suffisant (&gt;10% libre)
- **Orange** : Attention (5-10% libre)
- **Rouge** : Critique (&lt;5% libre)

## Création de Sauvegardes

### Sauvegarde Manuelle

<div className="step-number">1</div>
<div className="step-content">

**Initiation**
- Cliquez sur **"Créer une Sauvegarde"**
- Confirmation demandée via popup JavaScript

</div>

<div className="step-number">2</div>
<div className="step-content">

**Exécution**
- Génération automatique du fichier SQL
- Nom automatique : `backup_verins_db_YYYY-MM-DD_HH-MM-SS.sql`
- Affichage de la progression

</div>

<div className="step-number">3</div>
<div className="step-content">

**Confirmation**
- Message de succès avec taille du fichier
- Ajout automatique à la liste des sauvegardes
- Mise à jour de l'espace disque

</div>

### Sauvegarde Automatique

#### Configuration Recommandée
- **Fréquence** : Quotidienne (à programmer)
- **Heure** : En dehors des heures d'activité
- **Rétention** : Conservation de 30 jours minimum

#### Surveillance
- Vérification de l'espace disque avant sauvegarde
- Alerte en cas d'échec
- Log des opérations

## Gestion des Fichiers de Sauvegarde

### Liste des Sauvegardes

#### Informations Affichées

| Colonne | Description | Format |
|---------|-------------|--------|
| **Nom du Fichier** | Nom complet du fichier | backup_verins_db_YYYY-MM-DD_HH-MM-SS.sql |
| **Date de Création** | Horodatage de création | DD/MM/YYYY HH:MM:SS |
| **Taille** | Taille du fichier | XX.XX MB |
| **Actions** | Boutons d'action | Télécharger, Restaurer, Supprimer |

### Actions sur les Sauvegardes

#### Téléchargement

<div className="step-number">1</div>
<div className="step-content">

**Initiation**
- Cliquez sur **"Télécharger"** dans la ligne de la sauvegarde
- Téléchargement immédiat via le navigateur

</div>

<div className="step-number">2</div>
<div className="step-content">

**Fichier Téléchargé**
- Format : Fichier SQL standard
- Nom conservé : Original de la sauvegarde
- Utilisation : Archivage externe ou transfert

</div>

#### Suppression

<div className="warning-box">

**⚠️ Suppression Définitive**
- Action irréversible
- Confirmation JavaScript obligatoire
- Libération immédiate de l'espace disque

</div>

## Restauration de Données

### Processus de Restauration

<div className="warning-box">

**🚨 ATTENTION : Opération Critique**

La restauration remplace **TOUTES** les données actuelles :
- Perte définitive des données non sauvegardées
- Retour à l'état de la sauvegarde
- Impact sur tous les utilisateurs connectés

</div>

### Étapes de Restauration

<div className="step-number">1</div>
<div className="step-content">

**Sélection**
- Cliquez sur **"Restaurer"** dans la ligne de la sauvegarde
- Ouverture de la modale de confirmation

</div>

<div className="step-number">2</div>
<div className="step-content">

**Confirmation Double**
- Lecture obligatoire du message d'avertissement
- Confirmation explicite requise
- Possibilité d'annulation

</div>

<div className="step-number">3</div>
<div className="step-content">

**Exécution**
- Arrêt temporaire des connexions utilisateur
- Restauration de la base de données
- Redémarrage des services

</div>

<div className="step-number">4</div>
<div className="step-content">

**Vérification**
- Contrôle de l'intégrité des données
- Test de fonctionnement
- Reconnexion des utilisateurs

</div>

## Import de Sauvegardes Externes

### Processus d'Import

<div className="step-number">1</div>
<div className="step-content">

**Ouverture de la Modale**
- Cliquez sur **"Importer une Sauvegarde"**
- Interface de sélection de fichier

</div>

<div className="step-number">2</div>
<div className="step-content">

**Sélection du Fichier**
- **Format accepté** : Fichiers .sql uniquement
- **Taille maximale** : Selon configuration serveur
- **Validation** : Vérification automatique du format

</div>

<div className="step-number">3</div>
<div className="step-content">

**Upload et Stockage**
- Upload vers le dossier `/backups/uploaded/`
- Nom automatique : `uploaded_backup_YYYY-MM-DD_HH-MM-SS.sql`
- Disponible pour restauration immédiate

</div>

### Utilisation des Imports

#### Cas d'Usage
- **Migration** entre environnements
- **Restauration** de sauvegardes externes
- **Synchronisation** de données
- **Tests** avec données spécifiques

#### Sécurité
- Vérification de l'intégrité du fichier
- Validation du format SQL
- Contrôle de la compatibilité

## Initialisation de Base Vierge

### Fonctionnalité Critique

<div className="warning-box">

**🚨 DANGER : Perte Totale de Données**

L'initialisation supprime **TOUTES** les données :
- Affaires, essais, PV
- Utilisateurs (sauf comptes par défaut)
- Historiques et statistiques
- Configuration personnalisée

</div>

### Processus d'Initialisation

<div className="step-number">1</div>
<div className="step-content">

**Accès à la Fonction**
- Section "Actions Dangereuses" (fond rouge)
- Bouton **"Initialiser Base de Données Vierge"**

</div>

<div className="step-number">2</div>
<div className="step-content">

**Confirmations Multiples**
- Modale de confirmation avec avertissement
- Confirmation explicite obligatoire
- Dernière chance d'annulation

</div>

<div className="step-number">3</div>
<div className="step-content">

**Exécution**
- Suppression de toutes les données
- Recréation du schéma de base
- Insertion des données par défaut

</div>

### Cas d'Usage

#### Situations Appropriées
- **Nouveau déploiement** en production
- **Environnement de test** à réinitialiser
- **Formation** avec données propres
- **Démonstration** avec état initial

#### Précautions Obligatoires
- **Sauvegarde complète** avant initialisation
- **Validation** de la nécessité
- **Communication** aux utilisateurs
- **Planification** en dehors des heures d'activité

## Bonnes Pratiques

### Stratégie de Sauvegarde

<div className="success-box">

**Règle 3-2-1**
- **3** copies des données importantes
- **2** supports de stockage différents
- **1** copie hors site (cloud, site distant)

</div>

#### Fréquence Recommandée
- **Quotidienne** : Pour les données critiques
- **Hebdomadaire** : Sauvegarde de sécurité
- **Mensuelle** : Archive long terme

#### Tests de Restauration
- **Mensuel** : Test de restauration complète
- **Validation** : Vérification de l'intégrité
- **Documentation** : Procédures de récupération

### Surveillance et Maintenance

#### Monitoring
- **Espace disque** : Surveillance continue
- **Taille des sauvegardes** : Évolution dans le temps
- **Durée d'exécution** : Optimisation des performances

#### Nettoyage
- **Rotation** : Suppression des anciennes sauvegardes
- **Archivage** : Déplacement vers stockage long terme
- **Optimisation** : Compression des fichiers anciens

### Sécurité

#### Protection des Fichiers
- **Permissions** : Accès restreint aux contrôleurs
- **Chiffrement** : Protection des données sensibles
- **Audit** : Traçabilité des opérations

#### Procédures d'Urgence
- **Plan de récupération** documenté
- **Contacts d'urgence** identifiés
- **Procédures escalade** définies

---

:::tip Automatisation
Planifiez des sauvegardes automatiques quotidiennes pour garantir la protection continue de vos données.
:::

:::warning Tests Réguliers
Testez régulièrement vos procédures de restauration pour vous assurer qu'elles fonctionnent en cas d'urgence.
:::
