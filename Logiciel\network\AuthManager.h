#ifndef AUTHMANAGER_H
#define AUTHMANAGER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QTimer>
#include "models/NetworkError.h"

/**
 * @brief Authentication and session management
 *
 * This class handles user authentication, token management, and session lifecycle.
 * It provides automatic token refresh and session validation.
 */
class AuthManager : public QObject
{
    Q_OBJECT

public:
    struct UserInfo {
        int id;
        QString username;
        QString role;
        bool isValid() const { return id > 0 && !username.isEmpty(); }
    };

    explicit AuthManager(QObject *parent = nullptr);
    ~AuthManager();

    // Authentication state
    bool isAuthenticated() const { return !m_token.isEmpty() && !isTokenExpired(); }
    bool hasValidToken() const { return !m_token.isEmpty(); }
    bool isTokenExpired() const;

    // User information
    const UserInfo& currentUser() const { return m_userInfo; }
    QString currentUsername() const { return m_userInfo.username; }
    QString currentUserRole() const { return m_userInfo.role; }
    int currentUserId() const { return m_userInfo.id; }

    // Token management
    QString token() const { return m_token; }
    QString authorizationHeader() const;
    QDateTime tokenExpiry() const { return m_tokenExpiry; }

    // Session management
    void login(const QString& username, const QString& password);
    void logout();
    void refreshToken();
    void validateSession();

    // Token storage
    void saveTokenToSettings();
    void loadTokenFromSettings();
    void clearStoredToken();

signals:
    void loginSuccessful(const UserInfo& user);
    void loginFailed(const NetworkError& error);
    void loggedOut();
    void tokenRefreshed();
    void tokenExpired();
    void sessionValidated(bool isValid);
    void authenticationRequired();

private slots:
    void onTokenRefreshTimer();
    void checkTokenExpiry();

private:
    QString m_token;
    UserInfo m_userInfo;
    QDateTime m_tokenExpiry;
    QTimer* m_refreshTimer;
    QTimer* m_expiryCheckTimer;

    static const int TOKEN_REFRESH_INTERVAL_MINUTES = 30;
    static const int TOKEN_EXPIRY_CHECK_INTERVAL_SECONDS = 60;
    static const int TOKEN_VALIDITY_HOURS = 24;

    void setupTimers();
    void startRefreshTimer();
    void stopTimers();
    void clearSession();
    QDateTime calculateTokenExpiry() const;

public:
    // Public methods for ApiClient to use
    void setToken(const QString& token);
    void setUserInfo(const QJsonObject& userJson);

    // Settings keys
    static const QString SETTINGS_TOKEN_KEY;
    static const QString SETTINGS_USER_ID_KEY;
    static const QString SETTINGS_USERNAME_KEY;
    static const QString SETTINGS_USER_ROLE_KEY;
    static const QString SETTINGS_TOKEN_EXPIRY_KEY;
};

#endif // AUTHMANAGER_H
