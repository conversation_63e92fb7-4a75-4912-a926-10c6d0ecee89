#include "ApiClient.h"
#include "ApiEndpoints.h"
#include <QJsonDocument>
#include <QUrlQuery>
#include <QDebug>
#include <QTimer>

ApiClient::ApiClient(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_authManager(new AuthManager(this))
    , m_baseUrl(ApiEndpoints::BASE_URL)
    , m_timeoutMs(DEFAULT_TIMEOUT_MS)
{
    // Connect authentication manager signals
    connect(m_authManager, &AuthManager::loginSuccessful, this, &ApiClient::loginSuccessful);
    connect(m_authManager, &AuthManager::loginFailed, this, &ApiClient::loginFailed);
    connect(m_authManager, &AuthManager::loggedOut, this, &ApiClient::loggedOut);
    connect(m_authManager, &AuthManager::authenticationRequired, this, &ApiClient::onAuthenticationRequired);
}

ApiClient::~ApiClient()
{
    // Clean up any pending requests
    for (auto it = m_timeoutTimers.begin(); it != m_timeoutTimers.end(); ++it) {
        QNetworkReply* reply = it.key();
        QTimer* timer = it.value();

        reply->abort();
        timer->stop();
        timer->deleteLater();
    }
}

bool ApiClient::isAuthenticated() const
{
    return m_authManager->isAuthenticated();
}

void ApiClient::setBaseUrl(const QString& baseUrl)
{
    m_baseUrl = baseUrl;
}

// Authentication API
void ApiClient::login(const QString& username, const QString& password)
{
    QJsonObject loginData;
    loginData["username"] = username;
    loginData["password"] = password;

    QNetworkRequest request = buildRequest(ApiEndpoints::AUTH_LOGIN);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonDocument doc(loginData);
    QNetworkReply* reply = m_networkManager->post(request, doc.toJson());

    m_replyEndpoints[reply] = "auth/login";
    setupTimeoutTimer(reply);

    connect(reply, &QNetworkReply::finished, [this, reply, username]() {
        QByteArray data = reply->readAll();
        JsonResponse response = parseJsonResponse(data);

        if (response.isSuccess()) {
            // Extract token and user info
            QString token = response.value("token").toString();
            QJsonObject userObj = response.value("user").toJsonObject();

            if (!token.isEmpty() && !userObj.isEmpty()) {
                // Update auth manager with new token and user info
                m_authManager->setToken(token);
                m_authManager->setUserInfo(userObj);

                AuthManager::UserInfo userInfo;
                userInfo.id = userObj.value("id").toInt();
                userInfo.username = userObj.value("username").toString();
                userInfo.role = userObj.value("role").toString();

                emit loginSuccessful(userInfo);
            } else {
                NetworkError error(NetworkError::Type::AuthenticationError, "Invalid login response");
                emit loginFailed(error);
            }
        } else {
            emit loginFailed(response.error());
        }

        cleanupReply(reply);
    });
}

void ApiClient::logout()
{
    m_authManager->logout();
}

// User API
void ApiClient::getUsers()
{
    get(ApiEndpoints::USERS);
}

void ApiClient::getUserById(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    get(ApiEndpoints::USER_BY_ID, params);
}

// Business cases (Affaires) API
void ApiClient::getAffaires()
{
    get(ApiEndpoints::AFFAIRES);
}

void ApiClient::getAffaireById(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    get(ApiEndpoints::AFFAIRE_BY_ID, params);
}

void ApiClient::getAffairesByStatus(const QString& status)
{
    QUrlQuery params;
    params.addQueryItem("statut", status);
    get(ApiEndpoints::AFFAIRES_BY_STATUS, params);
}

void ApiClient::createAffaire(const QJsonObject& affaireData)
{
    post(ApiEndpoints::AFFAIRES, affaireData);
}

void ApiClient::updateAffaire(int id, const QJsonObject& affaireData)
{
    QJsonObject data = affaireData;
    data["id"] = id;
    put(ApiEndpoints::AFFAIRES, data);
}

void ApiClient::deleteAffaire(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    deleteResource(ApiEndpoints::AFFAIRES, params);
}

// Test cases (Essais) API
void ApiClient::getEssais()
{
    get(ApiEndpoints::ESSAIS);
}

void ApiClient::getEssaiById(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    get(ApiEndpoints::ESSAI_BY_ID, params);
}

void ApiClient::getEssaisByAffaire(int affaireId)
{
    QUrlQuery params;
    params.addQueryItem("affaire_id", QString::number(affaireId));
    get(ApiEndpoints::ESSAIS_BY_AFFAIRE, params);
}

void ApiClient::getEssaisByStatus(const QString& status)
{
    QUrlQuery params;
    params.addQueryItem("statut", status);
    get(ApiEndpoints::ESSAIS_BY_STATUS, params);
}

void ApiClient::createEssai(const QJsonObject& essaiData)
{
    post(ApiEndpoints::ESSAIS, essaiData);
}

void ApiClient::updateEssai(int id, const QJsonObject& essaiData)
{
    QJsonObject data = essaiData;
    data["id"] = id;
    put(ApiEndpoints::ESSAIS, data);
}

void ApiClient::deleteEssai(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    deleteResource(ApiEndpoints::ESSAIS, params);
}

// Curve data API
void ApiClient::getCourbes()
{
    get(ApiEndpoints::COURBES);
}

void ApiClient::getCourbeById(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    get(ApiEndpoints::COURBE_BY_ID, params);
}

void ApiClient::getCourbesByEssai(int essaiId)
{
    QUrlQuery params;
    params.addQueryItem("essai_id", QString::number(essaiId));
    get(ApiEndpoints::COURBES_BY_ESSAI, params);
}

void ApiClient::createCourbe(const QJsonObject& courbeData)
{
    post(ApiEndpoints::COURBES, courbeData);
}

void ApiClient::updateCourbe(int id, const QJsonObject& courbeData)
{
    QJsonObject data = courbeData;
    data["id"] = id;
    put(ApiEndpoints::COURBES, data);
}

void ApiClient::deleteCourbe(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    deleteResource(ApiEndpoints::COURBES, params);
}

// Process verbal (PV) API
void ApiClient::getPVs()
{
    get(ApiEndpoints::PV);
}

void ApiClient::getPVById(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    get(ApiEndpoints::PV_BY_ID, params);
}

void ApiClient::getPVsByAffaire(int affaireId)
{
    QUrlQuery params;
    params.addQueryItem("affaire_id", QString::number(affaireId));
    get(ApiEndpoints::PV_BY_AFFAIRE, params);
}

void ApiClient::createPV(const QJsonObject& pvData)
{
    post(ApiEndpoints::PV, pvData);
}

void ApiClient::updatePV(int id, const QJsonObject& pvData)
{
    QJsonObject data = pvData;
    data["id"] = id;
    put(ApiEndpoints::PV, data);
}

void ApiClient::deletePV(int id)
{
    QUrlQuery params;
    params.addQueryItem("id", QString::number(id));
    deleteResource(ApiEndpoints::PV, params);
}

// Performance/efficiency API
void ApiClient::getRendements()
{
    get(ApiEndpoints::RENDEMENT);
}

void ApiClient::getRendementByEssai(int essaiId)
{
    QUrlQuery params;
    params.addQueryItem("essai_id", QString::number(essaiId));
    get(ApiEndpoints::RENDEMENT_BY_ESSAI, params);
}

void ApiClient::createRendement(const QJsonObject& rendementData)
{
    post(ApiEndpoints::RENDEMENT, rendementData);
}

// Generic HTTP methods
void ApiClient::get(const QString& endpoint, const QUrlQuery& params)
{
    QNetworkRequest request = buildRequest(endpoint, params);
    QNetworkReply* reply = m_networkManager->get(request);

    m_replyEndpoints[reply] = endpoint;
    setupTimeoutTimer(reply);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onNetworkReplyFinished);
}

void ApiClient::post(const QString& endpoint, const QJsonObject& data)
{
    QNetworkRequest request = buildRequest(endpoint);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonDocument doc(data);
    QNetworkReply* reply = m_networkManager->post(request, doc.toJson());

    m_replyEndpoints[reply] = endpoint;
    setupTimeoutTimer(reply);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onNetworkReplyFinished);
}

void ApiClient::put(const QString& endpoint, const QJsonObject& data)
{
    QNetworkRequest request = buildRequest(endpoint);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonDocument doc(data);
    QNetworkReply* reply = m_networkManager->put(request, doc.toJson());

    m_replyEndpoints[reply] = endpoint;
    setupTimeoutTimer(reply);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onNetworkReplyFinished);
}

void ApiClient::deleteResource(const QString& endpoint, const QUrlQuery& params)
{
    QNetworkRequest request = buildRequest(endpoint, params);
    QNetworkReply* reply = m_networkManager->deleteResource(request);

    m_replyEndpoints[reply] = endpoint;
    setupTimeoutTimer(reply);
    connect(reply, &QNetworkReply::finished, this, &ApiClient::onNetworkReplyFinished);
}

// Private slots
void ApiClient::onNetworkReplyFinished()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    QString endpoint = m_replyEndpoints.value(reply, "unknown");
    handleResponse(reply, endpoint);
    cleanupReply(reply);
}

void ApiClient::onRequestTimeout()
{
    QTimer* timer = qobject_cast<QTimer*>(sender());
    if (!timer) return;

    // Find the reply associated with this timer
    for (auto it = m_timeoutTimers.begin(); it != m_timeoutTimers.end(); ++it) {
        if (it.value() == timer) {
            QNetworkReply* reply = it.key();
            QString endpoint = m_replyEndpoints.value(reply, "unknown");

            reply->abort();
            NetworkError error = NetworkError::timeout(QString("Request to %1 timed out").arg(endpoint));
            emit errorOccurred(endpoint, error);

            cleanupReply(reply);
            break;
        }
    }
}

void ApiClient::onAuthenticationRequired()
{
    qDebug() << "Authentication required - clearing session";
    // Could emit a signal here to show login dialog
}

// Private methods
QNetworkRequest ApiClient::buildRequest(const QString& endpoint, const QUrlQuery& params)
{
    QString url = buildUrl(endpoint, params);
    QNetworkRequest request(url);

    addCommonHeaders(request);
    addAuthenticationHeader(request);

    return request;
}

void ApiClient::addAuthenticationHeader(QNetworkRequest& request)
{
    if (m_authManager->isAuthenticated()) {
        QString authHeader = m_authManager->authorizationHeader();
        if (!authHeader.isEmpty()) {
            request.setRawHeader("Authorization", authHeader.toUtf8());
        }
    }
}

void ApiClient::addCommonHeaders(QNetworkRequest& request)
{
    request.setHeader(QNetworkRequest::UserAgentHeader, "HydraulicTestBench-Qt/1.0");
    request.setRawHeader("Accept", "application/json");
    request.setRawHeader("Cache-Control", "no-cache");
}

void ApiClient::handleResponse(QNetworkReply* reply, const QString& endpoint)
{
    QByteArray data = reply->readAll();

    // Check for network errors first
    if (reply->error() != QNetworkReply::NoError) {
        NetworkError error = NetworkError::fromNetworkReply(reply);
        emit errorOccurred(endpoint, error);
        return;
    }

    // First, check if the response is a JSON array or object
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        NetworkError error = NetworkError::jsonParseError(parseError.errorString());
        emit errorOccurred(endpoint, error);
        return;
    }

    // Handle array responses directly for endpoints that typically return arrays
    if (doc.isArray() && (endpoint.contains("affaires") || endpoint.contains("users") ||
                          endpoint.contains("essais") || endpoint.contains("courbes") ||
                          endpoint.contains("pv") || endpoint.contains("rendement"))) {

        qDebug() << "Handling array response for endpoint:" << endpoint;
        ArrayResponse arrayResponse = parseArrayResponse(data);

        if (arrayResponse.hasError()) {
            emit errorOccurred(endpoint, arrayResponse.error());
            return;
        }

        // Emit specific array signals
        emitSpecificArraySignals(endpoint, arrayResponse);
        return;
    }

    // Parse as JSON object response
    JsonResponse response = parseJsonResponse(data);

    if (response.hasError()) {
        emit errorOccurred(endpoint, response.error());
        return;
    }

    // Emit generic response signal
    emit responseReceived(endpoint, response);

    // Emit specific signals based on endpoint
    emitSpecificSignals(endpoint, response, data);
}

JsonResponse ApiClient::parseJsonResponse(const QByteArray& data)
{
    return JsonResponse::fromJson(data);
}

ArrayResponse ApiClient::parseArrayResponse(const QByteArray& data)
{
    return ArrayResponse::fromJson(data);
}

void ApiClient::setupTimeoutTimer(QNetworkReply* reply)
{
    QTimer* timer = new QTimer(this);
    timer->setSingleShot(true);
    timer->setInterval(m_timeoutMs);

    connect(timer, &QTimer::timeout, this, &ApiClient::onRequestTimeout);

    m_timeoutTimers[reply] = timer;
    timer->start();
}

void ApiClient::cleanupReply(QNetworkReply* reply)
{
    // Remove from tracking maps
    m_replyEndpoints.remove(reply);

    // Clean up timeout timer
    if (m_timeoutTimers.contains(reply)) {
        QTimer* timer = m_timeoutTimers.take(reply);
        timer->stop();
        timer->deleteLater();
    }

    // Clean up reply
    reply->deleteLater();
}

QString ApiClient::buildUrl(const QString& endpoint, const QUrlQuery& params)
{
    QString url = m_baseUrl + ApiEndpoints::API_BASE + endpoint;

    if (!params.isEmpty()) {
        url += "?" + params.toString();
    }

    return url;
}

void ApiClient::emitSpecificSignals(const QString& endpoint, const JsonResponse& response, const QByteArray& data)
{
    // Emit specific signals based on endpoint
    if (endpoint.contains("users")) {
        if (response.jsonObject().contains("id")) {
            emit userReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit usersReceived(arrayResp);
        }
    } else if (endpoint.contains("affaires")) {
        if (response.jsonObject().contains("id")) {
            emit affaireReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit affairesReceived(arrayResp);
        }
    } else if (endpoint.contains("essais")) {
        if (response.jsonObject().contains("id")) {
            emit essaiReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit essaisReceived(arrayResp);
        }
    } else if (endpoint.contains("courbes")) {
        if (response.jsonObject().contains("id")) {
            emit courbeReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit courbesReceived(arrayResp);
        }
    } else if (endpoint.contains("pv")) {
        if (response.jsonObject().contains("id")) {
            emit pvReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit pvsReceived(arrayResp);
        }
    } else if (endpoint.contains("rendement")) {
        if (response.jsonObject().contains("id")) {
            emit rendementReceived(response);
        } else {
            ArrayResponse arrayResp = parseArrayResponse(data);
            emit rendementsReceived(arrayResp);
        }
    }
}

void ApiClient::emitSpecificArraySignals(const QString& endpoint, const ArrayResponse& response)
{
    qDebug() << "Emitting specific array signal for endpoint:" << endpoint;

    // Emit specific array signals based on endpoint
    if (endpoint.contains("users")) {
        emit usersReceived(response);
    } else if (endpoint.contains("affaires")) {
        qDebug() << "Emitting affairesReceived signal with" << response.data().size() << "items";
        emit affairesReceived(response);
    } else if (endpoint.contains("essais")) {
        emit essaisReceived(response);
    } else if (endpoint.contains("courbes")) {
        emit courbesReceived(response);
    } else if (endpoint.contains("pv")) {
        emit pvsReceived(response);
    } else if (endpoint.contains("rendement")) {
        emit rendementsReceived(response);
    }
}
