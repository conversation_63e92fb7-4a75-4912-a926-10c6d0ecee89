<?php
require_once __DIR__ . '/../core/BaseController.php';
require_once __DIR__ . '/../../lib/user.php';

class UserController extends BaseController
{
    public function handle()
    {
        // Vérifier que l'utilisateur est un contrôleur pour toutes les opérations sauf GET
        if ($this->method !== 'GET' && $this->user['role'] !== 'controleur') {
            return $this->error('Permission refusée', 403);
        }

        switch ($this->method) {
            case 'GET':
                return $this->index();
            case 'POST':
                return $this->create();
            case 'PUT':
                return $this->update();
            case 'DELETE':
                return $this->delete();
            default:
                $this->error('Méthode non autorisée', 405);
        }
    }

    private function index()
    {
        if (isset($_GET['id'])) {
            $user = User::getById($_GET['id']);
            return $this->json($user);
        }
        return $this->json(User::getAllUsers());
    }

    private function create()
    {
        if (!isset($this->data['username']) || !isset($this->data['password'])) {
            return $this->error('Données manquantes');
        }

        if (User::create($this->data['username'], $this->data['password'], $this->data['role'])) {
            return $this->json(['message' => 'Utilisateur créé']);
        }
        return $this->error('Erreur de création', 500);
    }

    private function update()
    {
        if (!isset($this->data['id'])) {
            return $this->error('ID manquant');
        }

        // Mise à jour du mot de passe si fourni
        if (isset($this->data['password'])) {
            if (!User::updatePassword($this->data['id'], $this->data['password'])) {
                return $this->error('Erreur lors de la mise à jour du mot de passe', 500);
            }
        }

        // Mise à jour des autres informations
        if (User::update($this->data['id'], $this->data['username'], $this->data['role'])) {
            return $this->json(['message' => 'Utilisateur mis à jour']);
        }
        return $this->error('Erreur de mise à jour', 500);
    }

    private function delete()
    {
        if (!isset($_GET['id'])) {
            return $this->error('ID manquant');
        }

        // Empêcher la suppression de son propre compte
        if ($_GET['id'] == $this->user['id']) {
            return $this->error('Impossible de supprimer votre propre compte', 403);
        }

        if (User::delete($_GET['id'])) {
            return $this->json(['message' => 'Utilisateur supprimé']);
        }
        return $this->error('Erreur de suppression', 500);
    }
}
