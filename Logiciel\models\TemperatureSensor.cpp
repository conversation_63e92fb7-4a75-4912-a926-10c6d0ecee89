#include "TemperatureSensor.h"
#include <QDebug>
#include <QtMath>

// Default temperature values in Celsius
const double TemperatureSensor::DEFAULT_AMBIENT_TEMP = 20.0;
const double TemperatureSensor::DEFAULT_OPERATING_TEMP = 45.0;
const double TemperatureSensor::MAX_SAFE_TEMP = 80.0;
const double TemperatureSensor::MIN_OPERATING_TEMP = 10.0;
const double TemperatureSensor::THERMAL_TIME_CONSTANT = 300.0; // 5 minutes

TemperatureSensor::TemperatureSensor(TemperatureLocation location, QObject *parent)
    : SimulatedSensor(parent)
    , m_location(location)
    , m_thermalTimer(new QTimer(this))
    , m_cycleTimer(new QTimer(this))
    , m_currentThermalMode(ThermalMode::Normal)
    , m_thermalTargetTemp(DEFAULT_OPERATING_TEMP)
    , m_thermalStartTemp(DEFAULT_AMBIENT_TEMP)
    , m_thermalMinTemp(DEFAULT_AMBIENT_TEMP)
    , m_thermalMaxTemp(DEFAULT_OPERATING_TEMP)
    , m_alarmThreshold(MAX_SAFE_TEMP)
    , m_thermalDuration(0)
    , m_cycleTime(30000)
    , m_currentThermalCycle(0)
    , m_cycleHeating(true)
{
    setupTemperatureLocation();
    
    // Setup thermal timers
    m_thermalTimer->setSingleShot(true);
    QObject::connect(m_thermalTimer, &QTimer::timeout, this, &TemperatureSensor::onThermalTimer);

    m_cycleTimer->setSingleShot(false);
    QObject::connect(m_cycleTimer, &QTimer::timeout, this, &TemperatureSensor::onCycleTimer);
}

QString TemperatureSensor::deviceName() const
{
    return QString("Temperature Sensor (%1)").arg(m_locationString);
}

QString TemperatureSensor::deviceVersion() const
{
    return "Simulated v1.0";
}

HardwareInterface::DeviceType TemperatureSensor::deviceType() const
{
    return DeviceType::TemperatureSensor;
}

double TemperatureSensor::generateRealisticValue()
{
    switch (m_currentThermalMode) {
        case ThermalMode::Heatup:
            return generateHeatupValue();
        case ThermalMode::Cooldown:
            return generateCooldownValue();
        case ThermalMode::ThermalCycling:
            return generateThermalCyclingValue();
        case ThermalMode::Normal:
        case ThermalMode::AlarmMonitoring:
        default:
            return generateNormalTemperatureValue();
    }
}

QString TemperatureSensor::getPrimaryParameter() const
{
    return "temperature";
}

QStringList TemperatureSensor::getAllParameters() const
{
    return QStringList() << "temperature" << "temperature_f" << "temperature_k" << "viscosity_index";
}

void TemperatureSensor::setTemperatureLocation(TemperatureLocation location)
{
    m_location = location;
    setupTemperatureLocation();
}

void TemperatureSensor::simulateHeatup(double targetTemp, int heatupTimeMs)
{
    m_currentThermalMode = ThermalMode::Heatup;
    m_thermalStartTemp = m_currentValues.value("temperature", DEFAULT_AMBIENT_TEMP);
    m_thermalTargetTemp = targetTemp;
    m_thermalDuration = heatupTimeMs;
    m_thermalStartTime = QDateTime::currentDateTime();
    
    m_thermalTimer->start(heatupTimeMs);
    
    qDebug() << "Starting heatup to" << targetTemp << "°C over" << heatupTimeMs << "ms";
}

void TemperatureSensor::simulateCooldown(double targetTemp, int cooldownTimeMs)
{
    m_currentThermalMode = ThermalMode::Cooldown;
    m_thermalStartTemp = m_currentValues.value("temperature", DEFAULT_OPERATING_TEMP);
    m_thermalTargetTemp = targetTemp;
    m_thermalDuration = cooldownTimeMs;
    m_thermalStartTime = QDateTime::currentDateTime();
    
    m_thermalTimer->start(cooldownTimeMs);
    
    qDebug() << "Starting cooldown to" << targetTemp << "°C over" << cooldownTimeMs << "ms";
}

void TemperatureSensor::simulateTemperatureAlarm(double alarmThreshold)
{
    m_currentThermalMode = ThermalMode::AlarmMonitoring;
    m_alarmThreshold = alarmThreshold;
    
    qDebug() << "Temperature alarm monitoring enabled at" << alarmThreshold << "°C";
}

void TemperatureSensor::simulateThermalCycling(double minTemp, double maxTemp, int cycleTimeMs)
{
    m_currentThermalMode = ThermalMode::ThermalCycling;
    m_thermalMinTemp = minTemp;
    m_thermalMaxTemp = maxTemp;
    m_cycleTime = cycleTimeMs;
    m_currentThermalCycle = 0;
    m_cycleHeating = true;
    m_thermalStartTime = QDateTime::currentDateTime();
    
    // Start cycle timer
    m_cycleTimer->start(cycleTimeMs / 2); // Half cycle time for each direction
    
    qDebug() << "Starting thermal cycling between" << minTemp << "and" << maxTemp << "°C, cycle time:" << cycleTimeMs << "ms";
}

double TemperatureSensor::celsiusToFahrenheit(double celsius)
{
    return (celsius * 9.0 / 5.0) + 32.0;
}

double TemperatureSensor::fahrenheitToCelsius(double fahrenheit)
{
    return (fahrenheit - 32.0) * 5.0 / 9.0;
}

double TemperatureSensor::celsiusToKelvin(double celsius)
{
    return celsius + 273.15;
}

double TemperatureSensor::kelvinToCelsius(double kelvin)
{
    return kelvin - 273.15;
}

double TemperatureSensor::calculateViscosityIndex(double referenceTemp) const
{
    double currentTemp = m_currentValues.value("temperature", DEFAULT_OPERATING_TEMP);
    
    // Simplified viscosity-temperature relationship
    // Real hydraulic fluids have complex viscosity curves
    double tempRatio = currentTemp / referenceTemp;
    double viscosityIndex = 100.0 * qPow(tempRatio, -0.8); // Approximate relationship
    
    return viscosityIndex;
}

bool TemperatureSensor::isOverheating() const
{
    double currentTemp = m_currentValues.value("temperature", DEFAULT_AMBIENT_TEMP);
    return currentTemp > MAX_SAFE_TEMP;
}

bool TemperatureSensor::isWithinOperatingRange() const
{
    double currentTemp = m_currentValues.value("temperature", DEFAULT_AMBIENT_TEMP);
    return currentTemp >= MIN_OPERATING_TEMP && currentTemp <= MAX_SAFE_TEMP;
}

void TemperatureSensor::onThermalTimer()
{
    switch (m_currentThermalMode) {
        case ThermalMode::Heatup:
            emit heatupCompleted();
            m_currentThermalMode = ThermalMode::Normal;
            qDebug() << "Heatup completed";
            break;
        case ThermalMode::Cooldown:
            emit cooldownCompleted();
            m_currentThermalMode = ThermalMode::Normal;
            qDebug() << "Cooldown completed";
            break;
        default:
            break;
    }
}

void TemperatureSensor::onCycleTimer()
{
    if (m_currentThermalMode == ThermalMode::ThermalCycling) {
        m_cycleHeating = !m_cycleHeating;
        if (m_cycleHeating) {
            m_currentThermalCycle++;
            emit thermalCycleCompleted(m_currentThermalCycle);
            qDebug() << "Thermal cycle" << m_currentThermalCycle << "completed";
        }
    }
}

void TemperatureSensor::setupTemperatureLocation()
{
    switch (m_location) {
        case TemperatureLocation::FluidTank:
            m_locationString = "Fluid Tank";
            m_simConfig.minValue = DEFAULT_AMBIENT_TEMP - 5.0;
            m_simConfig.maxValue = DEFAULT_OPERATING_TEMP + 10.0;
            break;
        case TemperatureLocation::SystemReturn:
            m_locationString = "System Return";
            m_simConfig.minValue = DEFAULT_AMBIENT_TEMP;
            m_simConfig.maxValue = MAX_SAFE_TEMP;
            break;
        case TemperatureLocation::CylinderChamberA:
            m_locationString = "Cylinder Chamber A";
            m_simConfig.minValue = DEFAULT_AMBIENT_TEMP;
            m_simConfig.maxValue = MAX_SAFE_TEMP + 10.0;
            break;
        case TemperatureLocation::CylinderChamberB:
            m_locationString = "Cylinder Chamber B";
            m_simConfig.minValue = DEFAULT_AMBIENT_TEMP;
            m_simConfig.maxValue = MAX_SAFE_TEMP + 10.0;
            break;
        case TemperatureLocation::Ambient:
            m_locationString = "Ambient";
            m_simConfig.minValue = 0.0;
            m_simConfig.maxValue = 40.0;
            break;
    }
    
    // Update target values
    updateTargetValues();
}

double TemperatureSensor::generateHeatupValue()
{
    qint64 elapsedMs = m_thermalStartTime.msecsTo(QDateTime::currentDateTime());
    double progress = qMin(1.0, double(elapsedMs) / m_thermalDuration);
    
    // Exponential approach to target temperature
    double tempDiff = m_thermalTargetTemp - m_thermalStartTemp;
    double currentTemp = m_thermalStartTemp + tempDiff * (1.0 - qExp(-3.0 * progress));
    
    return addThermalNoise(currentTemp);
}

double TemperatureSensor::generateCooldownValue()
{
    qint64 elapsedMs = m_thermalStartTime.msecsTo(QDateTime::currentDateTime());
    double progress = qMin(1.0, double(elapsedMs) / m_thermalDuration);
    
    // Exponential decay to target temperature
    double tempDiff = m_thermalStartTemp - m_thermalTargetTemp;
    double currentTemp = m_thermalTargetTemp + tempDiff * qExp(-2.0 * progress);
    
    return addThermalNoise(currentTemp);
}

double TemperatureSensor::generateThermalCyclingValue()
{
    qint64 elapsedMs = m_thermalStartTime.msecsTo(QDateTime::currentDateTime());
    double cyclePosition = (elapsedMs % m_cycleTime) / double(m_cycleTime);
    
    // Generate sinusoidal temperature variation
    double tempRange = m_thermalMaxTemp - m_thermalMinTemp;
    double temperature = m_thermalMinTemp + tempRange * (0.5 + 0.5 * qSin(2 * M_PI * cyclePosition));
    
    return addThermalNoise(temperature);
}

double TemperatureSensor::generateNormalTemperatureValue()
{
    double baseTemp;
    
    switch (m_location) {
        case TemperatureLocation::FluidTank:
            baseTemp = generateFluidTankTemperature();
            break;
        case TemperatureLocation::SystemReturn:
            baseTemp = generateSystemReturnTemperature();
            break;
        case TemperatureLocation::CylinderChamberA:
        case TemperatureLocation::CylinderChamberB:
            baseTemp = generateCylinderTemperature();
            break;
        case TemperatureLocation::Ambient:
            baseTemp = generateAmbientTemperature();
            break;
        default:
            baseTemp = DEFAULT_OPERATING_TEMP;
            break;
    }
    
    // Check for alarm condition
    if (m_currentThermalMode == ThermalMode::AlarmMonitoring && baseTemp > m_alarmThreshold) {
        emit temperatureAlarmTriggered(baseTemp);
    }
    
    // Check for overheating
    if (baseTemp > MAX_SAFE_TEMP) {
        emit overheatingDetected();
    }
    
    return addThermalNoise(baseTemp);
}

double TemperatureSensor::generateFluidTankTemperature()
{
    // Tank temperature is relatively stable, influenced by ambient and system heat
    double ambientInfluence = DEFAULT_AMBIENT_TEMP;
    double systemHeat = 10.0; // Heat from system operation
    double targetTemp = m_targetValues.value("temperature", DEFAULT_OPERATING_TEMP);
    
    return applyThermalInertia(targetTemp, ambientInfluence + systemHeat, THERMAL_TIME_CONSTANT);
}

double TemperatureSensor::generateSystemReturnTemperature()
{
    // Return temperature is higher due to system losses and heat generation
    double baseTemp = DEFAULT_OPERATING_TEMP + 5.0;
    double heatGeneration = 5.0 * m_randomGenerator->generateDouble(); // Variable heat from losses
    
    return baseTemp + heatGeneration;
}

double TemperatureSensor::generateCylinderTemperature()
{
    // Cylinder temperature varies with operation and can be higher due to friction
    double baseTemp = DEFAULT_OPERATING_TEMP;
    double frictionHeat = 8.0 * m_randomGenerator->generateDouble(); // Heat from friction
    double workingHeat = 3.0 * qSin(QDateTime::currentDateTime().toMSecsSinceEpoch() / 2000.0); // Cyclic heat
    
    return baseTemp + frictionHeat + workingHeat;
}

double TemperatureSensor::generateAmbientTemperature()
{
    // Ambient temperature varies slowly throughout the day
    double timeOfDay = QDateTime::currentDateTime().time().hour() + 
                      QDateTime::currentDateTime().time().minute() / 60.0;
    double dailyVariation = 5.0 * qSin(2 * M_PI * (timeOfDay - 6.0) / 24.0); // Peak at 6 PM
    
    return DEFAULT_AMBIENT_TEMP + dailyVariation;
}

double TemperatureSensor::applyThermalInertia(double currentTemp, double targetTemp, double timeConstant)
{
    double deltaTime = m_simConfig.updateIntervalMs / 1000.0; // Convert to seconds
    double alpha = 1.0 - qExp(-deltaTime / timeConstant);
    
    return currentTemp + alpha * (targetTemp - currentTemp);
}

double TemperatureSensor::addThermalNoise(double baseTemp)
{
    double noiseLevel = 0.2; // ±0.2°C noise
    double noise = noiseLevel * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
    
    return baseTemp + noise;
}
