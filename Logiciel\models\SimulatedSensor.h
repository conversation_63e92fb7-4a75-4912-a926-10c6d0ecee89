#ifndef SIMULATEDSENSOR_H
#define SIMULATEDSENSOR_H

#include "HardwareInterface.h"
#include <QTimer>
#include <QRandomGenerator>
#include <QDateTime>

/**
 * @brief Base class for simulated sensors
 * 
 * This class provides common functionality for all simulated sensors,
 * including realistic data generation, noise simulation, and configurable
 * update rates.
 */
class SimulatedSensor : public HardwareInterface
{
    Q_OBJECT
    
public:
    struct SimulationConfig {
        double minValue = 0.0;
        double maxValue = 100.0;
        double noiseLevel = 0.01;  // Percentage of range
        int updateIntervalMs = 100;
        bool enableDrift = true;
        double driftRate = 0.001;  // Units per second
        bool enableSpikes = false;
        double spikeFrequency = 0.01; // Probability per update
        double spikeAmplitude = 0.1;  // Percentage of range
    };
    
    explicit SimulatedSensor(QObject *parent = nullptr);
    ~SimulatedSensor() override;
    
    // HardwareInterface implementation
    bool connect() override;
    void disconnect() override;
    bool isConnected() const override;
    ConnectionStatus connectionStatus() const override;
    bool isSimulated() const override { return true; }
    
    QVariant readValue(const QString& parameter) override;
    bool writeValue(const QString& parameter, const QVariant& value) override;
    QMap<QString, QVariant> readAllValues() override;
    
    bool configure(const QMap<QString, QVariant>& config) override;
    QMap<QString, QVariant> getConfiguration() const override;
    
    bool calibrate() override;
    bool isCalibrated() const override;
    QDateTime lastCalibrationDate() const override;
    
    bool performSelfTest() override;
    QString getLastError() const override;
    QMap<QString, QVariant> getDiagnosticInfo() const override;
    
    // Simulation-specific methods
    void setSimulationConfig(const SimulationConfig& config);
    SimulationConfig simulationConfig() const { return m_simConfig; }
    
    void setManualValue(double value);
    void enableManualMode(bool enabled);
    bool isManualMode() const { return m_manualMode; }
    
    void startSimulation();
    void stopSimulation();
    bool isSimulationRunning() const;
    
    // Value generation
    virtual double generateRealisticValue() = 0;
    virtual QString getPrimaryParameter() const = 0;
    virtual QStringList getAllParameters() const = 0;
    
signals:
    void simulationStarted();
    void simulationStopped();
    void manualModeChanged(bool enabled);
    
protected slots:
    void updateSimulatedValues();
    
protected:
    QTimer* m_updateTimer;
    SimulationConfig m_simConfig;
    QMap<QString, double> m_currentValues;
    QMap<QString, double> m_targetValues;
    
    bool m_manualMode;
    double m_manualValue;
    double m_driftOffset;
    QDateTime m_simulationStartTime;
    
    // Noise and variation generation
    double addNoise(double baseValue);
    double addDrift(double baseValue);
    double addSpikes(double baseValue);
    double smoothTransition(double currentValue, double targetValue, double factor = 0.1);
    
    // Utility methods
    double clampValue(double value) const;
    double normalizeValue(double value) const;
    void updateTargetValues();

    // Random number generation for derived classes
    QRandomGenerator* m_randomGenerator;

private:
};

#endif // SIMULATEDSENSOR_H
