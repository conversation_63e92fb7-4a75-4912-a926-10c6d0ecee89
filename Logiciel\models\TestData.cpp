#include "TestData.h"
#include <QJsonDocument>
#include <QtMath>

TestData::TestData()
{
    initializeDefaults();
}

TestData::TestData(const QDateTime& timestamp)
    : m_timestamp(timestamp)
{
    initializeDefaults();
}

TestData::TestData(const TestData& other)
    : m_timestamp(other.m_timestamp)
    , m_pressureCPA(other.m_pressureCPA)
    , m_pressureCPB(other.m_pressureCPB)
    , m_pressureSupply(other.m_pressureSupply)
    , m_pressureReturn(other.m_pressureReturn)
    , m_flowRate(other.m_flowRate)
    , m_temperatureFluid(other.m_temperatureFluid)
    , m_temperatureAmbient(other.m_temperatureAmbient)
    , m_actuatorPosition(other.m_actuatorPosition)
    , m_actuatorVelocity(other.m_actuatorVelocity)
    , m_actuatorForce(other.m_actuatorForce)
    , m_powerHydraulic(other.m_powerHydraulic)
    , m_efficiency(other.m_efficiency)
    , m_additionalParameters(other.m_additionalParameters)
{
}

TestData& TestData::operator=(const TestData& other)
{
    if (this != &other) {
        m_timestamp = other.m_timestamp;
        m_pressureCPA = other.m_pressureCPA;
        m_pressureCPB = other.m_pressureCPB;
        m_pressureSupply = other.m_pressureSupply;
        m_pressureReturn = other.m_pressureReturn;
        m_flowRate = other.m_flowRate;
        m_temperatureFluid = other.m_temperatureFluid;
        m_temperatureAmbient = other.m_temperatureAmbient;
        m_actuatorPosition = other.m_actuatorPosition;
        m_actuatorVelocity = other.m_actuatorVelocity;
        m_actuatorForce = other.m_actuatorForce;
        m_powerHydraulic = other.m_powerHydraulic;
        m_efficiency = other.m_efficiency;
        m_additionalParameters = other.m_additionalParameters;
    }
    return *this;
}

QVariant TestData::parameter(const QString& name) const
{
    if (name == "timestamp") return m_timestamp;
    else if (name == "pressure_cpa") return m_pressureCPA;
    else if (name == "pressure_cpb") return m_pressureCPB;
    else if (name == "pressure_supply") return m_pressureSupply;
    else if (name == "pressure_return") return m_pressureReturn;
    else if (name == "flow_rate") return m_flowRate;
    else if (name == "temperature_fluid") return m_temperatureFluid;
    else if (name == "temperature_ambient") return m_temperatureAmbient;
    else if (name == "actuator_position") return m_actuatorPosition;
    else if (name == "actuator_velocity") return m_actuatorVelocity;
    else if (name == "actuator_force") return m_actuatorForce;
    else if (name == "power_hydraulic") return m_powerHydraulic;
    else if (name == "efficiency") return m_efficiency;
    else return m_additionalParameters.value(name);
}

void TestData::setParameter(const QString& name, const QVariant& value)
{
    if (name == "timestamp") m_timestamp = value.toDateTime();
    else if (name == "pressure_cpa") m_pressureCPA = value.toDouble();
    else if (name == "pressure_cpb") m_pressureCPB = value.toDouble();
    else if (name == "pressure_supply") m_pressureSupply = value.toDouble();
    else if (name == "pressure_return") m_pressureReturn = value.toDouble();
    else if (name == "flow_rate") m_flowRate = value.toDouble();
    else if (name == "temperature_fluid") m_temperatureFluid = value.toDouble();
    else if (name == "temperature_ambient") m_temperatureAmbient = value.toDouble();
    else if (name == "actuator_position") m_actuatorPosition = value.toDouble();
    else if (name == "actuator_velocity") m_actuatorVelocity = value.toDouble();
    else if (name == "actuator_force") m_actuatorForce = value.toDouble();
    else if (name == "power_hydraulic") m_powerHydraulic = value.toDouble();
    else if (name == "efficiency") m_efficiency = value.toDouble();
    else m_additionalParameters[name] = value;
}

QStringList TestData::parameterNames() const
{
    QStringList names;
    names << "timestamp" << "pressure_cpa" << "pressure_cpb" << "pressure_supply" << "pressure_return"
          << "flow_rate" << "temperature_fluid" << "temperature_ambient"
          << "actuator_position" << "actuator_velocity" << "actuator_force"
          << "power_hydraulic" << "efficiency";
    names.append(m_additionalParameters.keys());
    return names;
}

bool TestData::isValid() const
{
    return m_timestamp.isValid() && 
           !qIsNaN(m_pressureCPA) && !qIsNaN(m_pressureCPB) &&
           !qIsNaN(m_flowRate) && !qIsNaN(m_actuatorPosition);
}

QStringList TestData::validate() const
{
    QStringList errors;
    
    if (!m_timestamp.isValid()) {
        errors << "Invalid timestamp";
    }
    
    if (qIsNaN(m_pressureCPA) || m_pressureCPA < 0) {
        errors << "Invalid pressure CPA";
    }
    
    if (qIsNaN(m_pressureCPB) || m_pressureCPB < 0) {
        errors << "Invalid pressure CPB";
    }
    
    if (qIsNaN(m_flowRate)) {
        errors << "Invalid flow rate";
    }
    
    if (qIsNaN(m_actuatorPosition)) {
        errors << "Invalid actuator position";
    }
    
    return errors;
}

QJsonObject TestData::toJson() const
{
    QJsonObject json;
    
    json["timestamp"] = m_timestamp.toString(Qt::ISODate);
    json["pressure_cpa"] = m_pressureCPA;
    json["pressure_cpb"] = m_pressureCPB;
    json["pressure_supply"] = m_pressureSupply;
    json["pressure_return"] = m_pressureReturn;
    json["flow_rate"] = m_flowRate;
    json["temperature_fluid"] = m_temperatureFluid;
    json["temperature_ambient"] = m_temperatureAmbient;
    json["actuator_position"] = m_actuatorPosition;
    json["actuator_velocity"] = m_actuatorVelocity;
    json["actuator_force"] = m_actuatorForce;
    json["power_hydraulic"] = m_powerHydraulic;
    json["efficiency"] = m_efficiency;
    
    // Add additional parameters
    for (auto it = m_additionalParameters.begin(); it != m_additionalParameters.end(); ++it) {
        json[it.key()] = QJsonValue::fromVariant(it.value());
    }
    
    return json;
}

void TestData::fromJson(const QJsonObject& json)
{
    m_timestamp = QDateTime::fromString(json["timestamp"].toString(), Qt::ISODate);
    m_pressureCPA = json["pressure_cpa"].toDouble();
    m_pressureCPB = json["pressure_cpb"].toDouble();
    m_pressureSupply = json["pressure_supply"].toDouble();
    m_pressureReturn = json["pressure_return"].toDouble();
    m_flowRate = json["flow_rate"].toDouble();
    m_temperatureFluid = json["temperature_fluid"].toDouble();
    m_temperatureAmbient = json["temperature_ambient"].toDouble();
    m_actuatorPosition = json["actuator_position"].toDouble();
    m_actuatorVelocity = json["actuator_velocity"].toDouble();
    m_actuatorForce = json["actuator_force"].toDouble();
    m_powerHydraulic = json["power_hydraulic"].toDouble();
    m_efficiency = json["efficiency"].toDouble();
    
    // Load additional parameters
    QStringList standardParams = parameterNames();
    for (auto it = json.begin(); it != json.end(); ++it) {
        if (!standardParams.contains(it.key())) {
            m_additionalParameters[it.key()] = it.value().toVariant();
        }
    }
}

bool TestData::operator==(const TestData& other) const
{
    return m_timestamp == other.m_timestamp &&
           qFuzzyCompare(m_pressureCPA, other.m_pressureCPA) &&
           qFuzzyCompare(m_pressureCPB, other.m_pressureCPB) &&
           qFuzzyCompare(m_flowRate, other.m_flowRate) &&
           qFuzzyCompare(m_actuatorPosition, other.m_actuatorPosition);
}

TestData TestData::fromSensorReadings(const QMap<QString, QVariant>& readings)
{
    TestData data(QDateTime::currentDateTime());
    
    for (auto it = readings.begin(); it != readings.end(); ++it) {
        data.setParameter(it.key(), it.value());
    }
    
    return data;
}

TestData TestData::interpolate(const TestData& data1, const TestData& data2, double factor)
{
    TestData result;
    
    // Interpolate timestamp
    qint64 time1 = data1.m_timestamp.toMSecsSinceEpoch();
    qint64 time2 = data2.m_timestamp.toMSecsSinceEpoch();
    qint64 interpolatedTime = time1 + factor * (time2 - time1);
    result.m_timestamp = QDateTime::fromMSecsSinceEpoch(interpolatedTime);
    
    // Interpolate numeric values
    result.m_pressureCPA = data1.m_pressureCPA + factor * (data2.m_pressureCPA - data1.m_pressureCPA);
    result.m_pressureCPB = data1.m_pressureCPB + factor * (data2.m_pressureCPB - data1.m_pressureCPB);
    result.m_pressureSupply = data1.m_pressureSupply + factor * (data2.m_pressureSupply - data1.m_pressureSupply);
    result.m_pressureReturn = data1.m_pressureReturn + factor * (data2.m_pressureReturn - data1.m_pressureReturn);
    result.m_flowRate = data1.m_flowRate + factor * (data2.m_flowRate - data1.m_flowRate);
    result.m_temperatureFluid = data1.m_temperatureFluid + factor * (data2.m_temperatureFluid - data1.m_temperatureFluid);
    result.m_temperatureAmbient = data1.m_temperatureAmbient + factor * (data2.m_temperatureAmbient - data1.m_temperatureAmbient);
    result.m_actuatorPosition = data1.m_actuatorPosition + factor * (data2.m_actuatorPosition - data1.m_actuatorPosition);
    result.m_actuatorVelocity = data1.m_actuatorVelocity + factor * (data2.m_actuatorVelocity - data1.m_actuatorVelocity);
    result.m_actuatorForce = data1.m_actuatorForce + factor * (data2.m_actuatorForce - data1.m_actuatorForce);
    result.m_powerHydraulic = data1.m_powerHydraulic + factor * (data2.m_powerHydraulic - data1.m_powerHydraulic);
    result.m_efficiency = data1.m_efficiency + factor * (data2.m_efficiency - data1.m_efficiency);
    
    return result;
}

void TestData::initializeDefaults()
{
    m_timestamp = QDateTime::currentDateTime();
    m_pressureCPA = 0.0;
    m_pressureCPB = 0.0;
    m_pressureSupply = 0.0;
    m_pressureReturn = 0.0;
    m_flowRate = 0.0;
    m_temperatureFluid = 20.0;
    m_temperatureAmbient = 20.0;
    m_actuatorPosition = 0.0;
    m_actuatorVelocity = 0.0;
    m_actuatorForce = 0.0;
    m_powerHydraulic = 0.0;
    m_efficiency = 0.0;
}

// TestDataSet implementation
TestDataSet::TestDataSet()
{
}

TestDataSet::TestDataSet(const QString& testName)
    : m_testName(testName)
{
}

void TestDataSet::addDataPoint(const TestData& data)
{
    m_dataPoints.append(data);
    
    // Update time range
    if (m_dataPoints.size() == 1) {
        m_startTime = data.timestamp();
        m_endTime = data.timestamp();
    } else {
        if (data.timestamp() < m_startTime) {
            m_startTime = data.timestamp();
        }
        if (data.timestamp() > m_endTime) {
            m_endTime = data.timestamp();
        }
    }
}

void TestDataSet::removeDataPoint(int index)
{
    if (index >= 0 && index < m_dataPoints.size()) {
        m_dataPoints.removeAt(index);
    }
}

void TestDataSet::clear()
{
    m_dataPoints.clear();
    m_startTime = QDateTime();
    m_endTime = QDateTime();
}

TestData TestDataSet::averageValues() const
{
    if (m_dataPoints.isEmpty()) {
        return TestData();
    }
    
    TestData average;
    double count = m_dataPoints.size();
    
    double sumPressureCPA = 0, sumPressureCPB = 0, sumFlow = 0;
    double sumTempFluid = 0, sumPosition = 0, sumVelocity = 0, sumForce = 0;
    
    for (const TestData& data : m_dataPoints) {
        sumPressureCPA += data.pressureCPA();
        sumPressureCPB += data.pressureCPB();
        sumFlow += data.flowRate();
        sumTempFluid += data.temperatureFluid();
        sumPosition += data.actuatorPosition();
        sumVelocity += data.actuatorVelocity();
        sumForce += data.actuatorForce();
    }
    
    average.setPressureCPA(sumPressureCPA / count);
    average.setPressureCPB(sumPressureCPB / count);
    average.setFlowRate(sumFlow / count);
    average.setTemperatureFluid(sumTempFluid / count);
    average.setActuatorPosition(sumPosition / count);
    average.setActuatorVelocity(sumVelocity / count);
    average.setActuatorForce(sumForce / count);
    
    return average;
}

TestData TestDataSet::minimumValues() const
{
    if (m_dataPoints.isEmpty()) {
        return TestData();
    }

    TestData minimum = m_dataPoints.first();

    for (const TestData& data : m_dataPoints) {
        if (data.pressureCPA() < minimum.pressureCPA()) {
            minimum.setPressureCPA(data.pressureCPA());
        }
        if (data.pressureCPB() < minimum.pressureCPB()) {
            minimum.setPressureCPB(data.pressureCPB());
        }
        if (data.pressureSupply() < minimum.pressureSupply()) {
            minimum.setPressureSupply(data.pressureSupply());
        }
        if (data.pressureReturn() < minimum.pressureReturn()) {
            minimum.setPressureReturn(data.pressureReturn());
        }
        if (data.flowRate() < minimum.flowRate()) {
            minimum.setFlowRate(data.flowRate());
        }
        if (data.temperatureFluid() < minimum.temperatureFluid()) {
            minimum.setTemperatureFluid(data.temperatureFluid());
        }
        if (data.temperatureAmbient() < minimum.temperatureAmbient()) {
            minimum.setTemperatureAmbient(data.temperatureAmbient());
        }
        if (data.actuatorPosition() < minimum.actuatorPosition()) {
            minimum.setActuatorPosition(data.actuatorPosition());
        }
        if (data.actuatorVelocity() < minimum.actuatorVelocity()) {
            minimum.setActuatorVelocity(data.actuatorVelocity());
        }
        if (data.actuatorForce() < minimum.actuatorForce()) {
            minimum.setActuatorForce(data.actuatorForce());
        }
    }

    return minimum;
}

TestData TestDataSet::maximumValues() const
{
    if (m_dataPoints.isEmpty()) {
        return TestData();
    }

    TestData maximum = m_dataPoints.first();

    for (const TestData& data : m_dataPoints) {
        if (data.pressureCPA() > maximum.pressureCPA()) {
            maximum.setPressureCPA(data.pressureCPA());
        }
        if (data.pressureCPB() > maximum.pressureCPB()) {
            maximum.setPressureCPB(data.pressureCPB());
        }
        if (data.pressureSupply() > maximum.pressureSupply()) {
            maximum.setPressureSupply(data.pressureSupply());
        }
        if (data.pressureReturn() > maximum.pressureReturn()) {
            maximum.setPressureReturn(data.pressureReturn());
        }
        if (data.flowRate() > maximum.flowRate()) {
            maximum.setFlowRate(data.flowRate());
        }
        if (data.temperatureFluid() > maximum.temperatureFluid()) {
            maximum.setTemperatureFluid(data.temperatureFluid());
        }
        if (data.temperatureAmbient() > maximum.temperatureAmbient()) {
            maximum.setTemperatureAmbient(data.temperatureAmbient());
        }
        if (data.actuatorPosition() > maximum.actuatorPosition()) {
            maximum.setActuatorPosition(data.actuatorPosition());
        }
        if (data.actuatorVelocity() > maximum.actuatorVelocity()) {
            maximum.setActuatorVelocity(data.actuatorVelocity());
        }
        if (data.actuatorForce() > maximum.actuatorForce()) {
            maximum.setActuatorForce(data.actuatorForce());
        }
    }

    return maximum;
}

double TestDataSet::duration() const
{
    if (m_startTime.isValid() && m_endTime.isValid()) {
        return m_startTime.msecsTo(m_endTime) / 1000.0;
    }
    return 0.0;
}

QJsonObject TestDataSet::toJson() const
{
    QJsonObject json;
    json["test_name"] = m_testName;
    json["start_time"] = m_startTime.toString(Qt::ISODate);
    json["end_time"] = m_endTime.toString(Qt::ISODate);
    
    QJsonArray dataArray;
    for (const TestData& data : m_dataPoints) {
        dataArray.append(data.toJson());
    }
    json["data_points"] = dataArray;
    
    return json;
}
