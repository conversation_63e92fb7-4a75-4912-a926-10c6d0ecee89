#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

#include <QToolBar>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QShortcut>
#include <QKeySequence>
#include <QMessageBox>
#include <QJsonObject>
#include <QJsonArray>

// Network layer includes
#include "network/ApiClient.h"
#include "network/AuthManager.h"
#include "models/NetworkError.h"
#include "models/ApiResponse.h"

QT_BEGIN_NAMESPACE

namespace Ui {
    class MainWindow;
}

QT_END_NAMESPACE

class MainWindow final : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);

    ~MainWindow() override;

private:
    Ui::MainWindow *ui;

    // Network layer
    ApiClient *m_apiClient = nullptr;

    // Widgets pour la barre d'outils de login
    QToolBar *loginToolBar = nullptr;
    QWidget *loginWidget = nullptr;
    QHBoxLayout *loginLayout = nullptr;
    QLabel *userLabel = nullptr;
    QLineEdit *usernameLineEdit = nullptr;
    QLabel *passLabel = nullptr;
    QLineEdit *passwordLineEdit = nullptr;
    QPushButton *loginButton = nullptr;

    // Barre de menus personnalisée
    QMenuBar *m_customMenuBar = nullptr;

    // Widgets post-connexion
    QWidget *operatorWidget = nullptr;
    QLabel *operatorStatusLabel = nullptr;
    QPushButton *logoutButton = nullptr;

    // Overlay de connexion
    QWidget *loginOverlay = nullptr;

    // État de l'authentification
    bool m_isAuthenticating = false;

    // Gestion des affaires
    QJsonObject m_currentAffaire;
    bool m_hasSelectedAffaire = false;

    // Gestion des essais
    QJsonObject m_currentEssai;
    bool m_hasSelectedEssai = false;

    // Gestion des courbes
    QJsonObject m_currentCourbe;
    bool m_hasSelectedCourbe = false;
    QJsonArray m_existingCourbes; // Courbes existantes pour l'essai courant

    // État de l'opérateur connecté
    QString m_currentOperatorName;

    // Raccourcis clavier
    QShortcut *m_shortcutFocusLogin = nullptr;
    QShortcut *m_shortcutQuit = nullptr;
    QShortcut *m_shortcutLogout = nullptr;
    QShortcut *m_shortcutEscape = nullptr;
    QShortcut *m_shortcutRefresh = nullptr;

    // Méthodes utilitaires
    void setupLoginWidgets();

    void setupOperatorWidgets(const QString &operatorName);

    void switchToLoginState();

    void switchToOperatorState(const QString &operatorName);

    void tryLogin(const QString &username, const QString &password);

    void showLoginOverlay();

    void hideLoginOverlay() const;

    void resizeEvent(QResizeEvent *event) override;

    // Méthodes de gestion de l'authentification
    void setupApiClient();
    void setLoginButtonsEnabled(bool enabled);
    void cleanupOperatorWidgets();
    void cleanupLoginWidgets();

    // Méthodes de gestion des raccourcis clavier
    void setupKeyboardShortcuts();
    void updateShortcutsState();
    void setTabOrder();
    void keyPressEvent(QKeyEvent *event) override;

    // Méthodes de gestion des affaires
    void showAffaireSelectionDialog();
    void closeSelectedAffaire();
    void updateAffaireMenuState();
    void handleAffairesResponseManually(const JsonResponse& response);
    void createAndShowAffaireDialog(const QJsonArray& affaires);

    // Méthodes de gestion des essais
    void showEssaiSelectionDialog();
    void closeSelectedEssai();
    void updateEssaiMenuState();
    void handleEssaisResponseManually(const JsonResponse& response);
    void createAndShowEssaiDialog(const QJsonArray& essais);

    // Méthodes de gestion des courbes
    void showCourbeSelectionDialog();
    void showCourbeCreationDialog();
    void closeSelectedCourbe();
    void updateCourbeMenuState();
    void handleCourbesResponseManually(const JsonResponse& response);
    void createAndShowCourbeDialog(const QJsonArray& courbes);
    QStringList getAvailableCurveTypes();
    QStringList getExistingCurveTypes() const;
    void refreshExistingCourbes();

    // Méthode de mise à jour du titre de la fenêtre
    void updateWindowTitle();

private slots:
    // Gestionnaires de signaux d'authentification
    void onLoginSuccessful(const AuthManager::UserInfo& userInfo);
    void onLoginFailed(const NetworkError& error);
    void onLogoutRequested();

    // Gestionnaires de raccourcis clavier
    void onFocusLoginShortcut();
    void onQuitShortcut();
    void onLogoutShortcut();
    void onEscapeShortcut();
    void onRefreshShortcut();
    void onLoginFieldEnterPressed();

    // Gestionnaires d'affaires
    void onSelectAffaireTriggered();
    void onCloseAffaireTriggered();
    void onAffairesArrayReceived(const ArrayResponse& response);

    // Gestionnaires d'essais
    void onSelectEssaiTriggered();
    void onCloseEssaiTriggered();
    void onEssaisArrayReceived(const ArrayResponse& response);

    // Gestionnaires de courbes
    void onSelectCourbeTriggered();
    void onCreateCourbeTriggered();
    void onCloseCourbeTriggered();
    void onCourbesArrayReceived(const ArrayResponse& response);

    // Gestionnaires de débogage
    void onGenericResponseReceived(const QString& endpoint, const JsonResponse& response);
    void onApiErrorOccurred(const QString& endpoint, const NetworkError& error);
};
#endif // MAINWINDOW_H
