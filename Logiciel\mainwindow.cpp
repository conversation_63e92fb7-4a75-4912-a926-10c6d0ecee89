/**
 * @file mainwindow.cpp
 * @brief Interface principale pour le système de gestion des essais hydrauliques BTS CIEL
 *
 * Cette fenêtre gère l'interface utilisateur principale avec une hiérarchie :
 * Affaires > Essais > Courbes
 *
 * Fonctionnalités principales :
 * - Authentification utilisateur
 * - Gestion des affaires (projets clients)
 * - Gestion des essais hydrauliques
 * - Gestion des courbes de données (CPA, CPB, Résultante, etc.)
 * - Communication avec l'API REST PHP
 */

#include "mainwindow.h"
#include "./ui_mainwindow.h"

// Inclusions Qt pour les widgets et la barre d'outils/menu
#include <QToolBar>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QMenuBar>
#include <QMenu>
#include <QStatusBar>
#include <QDebug>
#include <QKeyEvent>
#include <QApplication>
#include <QDialog>
#include <QVBoxLayout>
#include <QListWidget>
#include <QListWidgetItem>
#include <QComboBox>
#include <QUrlQuery>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <iostream>

// Constructeur de la fenêtre principale
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
      , ui(new Ui::MainWindow) {
    try {
        qDebug() << "=== MAINWINDOW CONSTRUCTOR START ===";
        std::cout << "=== MAINWINDOW CONSTRUCTOR START ===" << std::endl;

        qDebug() << "Setting up UI...";
        std::cout << "Setting up UI..." << std::endl;
        ui->setupUi(this); // Initialise l'UI générée par Qt Designer
        qDebug() << "UI setup completed";
        std::cout << "UI setup completed" << std::endl;

    qDebug() << "Creating toolbar...";
    std::cout << "Creating toolbar..." << std::endl;
    // Création d'une barre d'outils unique (non déplaçable) qui contiendra menus à gauche et login à droite
    loginToolBar = new QToolBar(this);
    loginToolBar->setMovable(false); // Empêche l'utilisateur de déplacer la barre
    addToolBar(Qt::TopToolBarArea, loginToolBar);
    qDebug() << "Toolbar created and added";
    std::cout << "Toolbar created and added" << std::endl;

    qDebug() << "Creating toolbar widget and layout...";
    std::cout << "Creating toolbar widget and layout..." << std::endl;
    // Widget principal de la barre d'outils (pour gérer la disposition)
    auto *toolBarWidget = new QWidget(this);
    auto *toolBarLayout = new QHBoxLayout(toolBarWidget);
    toolBarLayout->setContentsMargins(0, 0, 0, 0); // Pas de marges
    qDebug() << "Toolbar widget and layout created";
    std::cout << "Toolbar widget and layout created" << std::endl;

    qDebug() << "Creating custom menu bar...";
    std::cout << "Creating custom menu bar..." << std::endl;
    // Création de la barre de menus personnalisée (à gauche de la toolbar)
    m_customMenuBar = new QMenuBar(this);

    qDebug() << "Creating 'Affaires' menu...";
    std::cout << "Creating 'Affaires' menu..." << std::endl;
    // Menu "Affaires" - Niveau racine de la hiérarchie (projets clients)
    const auto affairesMenu = new QMenu("Affaires", this);
    const auto actionSelectAffaire = new QAction("Sélectionner affaire...", this);
    const auto actionCloseAffaire = new QAction("Fermer affaire sélectionnée", this);

    // Connecter les actions aux gestionnaires d'événements
    connect(actionSelectAffaire, &QAction::triggered, this, &MainWindow::onSelectAffaireTriggered);
    connect(actionCloseAffaire, &QAction::triggered, this, &MainWindow::onCloseAffaireTriggered);

    affairesMenu->addAction(actionSelectAffaire);
    affairesMenu->addAction(actionCloseAffaire);
    m_customMenuBar->addMenu(affairesMenu);

    // État initial : seule la sélection est possible
    actionCloseAffaire->setEnabled(false); // Désactivé par défaut
    updateAffaireMenuState(); // Synchronise l'état du menu avec les données

    qDebug() << "'Affaires' menu created";
    std::cout << "'Affaires' menu created" << std::endl;

    qDebug() << "Creating 'Essais' menu...";
    std::cout << "Creating 'Essais' menu..." << std::endl;
    // Menu "Essais" - Tests hydrauliques liés à une affaire
    const auto essaisMenu = new QMenu("Essais", this);
    const auto actionSelectEssai = new QAction("Sélectionner essai...", this);
    const auto actionCloseEssai = new QAction("Fermer essai sélectionné", this);

    // Connecter les actions aux gestionnaires d'événements
    connect(actionSelectEssai, &QAction::triggered, this, &MainWindow::onSelectEssaiTriggered);
    connect(actionCloseEssai, &QAction::triggered, this, &MainWindow::onCloseEssaiTriggered);

    essaisMenu->addAction(actionSelectEssai);
    essaisMenu->addAction(actionCloseEssai);
    m_customMenuBar->addMenu(essaisMenu);

    // État initial : menu désactivé jusqu'à sélection d'une affaire
    actionCloseEssai->setEnabled(false); // Désactivé par défaut
    essaisMenu->setEnabled(false); // Dépend de la sélection d'une affaire
    qDebug() << "'Essais' menu created";
    std::cout << "'Essais' menu created" << std::endl;

    qDebug() << "Creating 'Courbes' menu...";
    std::cout << "Creating 'Courbes' menu..." << std::endl;
    // Menu "Courbes" - Données de mesure hydraulique (CPA, CPB, Résultante, etc.)
    const auto courbesMenu = new QMenu("Courbes", this);
    const auto actionSelectCourbe = new QAction("Sélectionner courbe...", this);
    const auto actionCreateCourbe = new QAction("Créer nouvelle courbe...", this);
    const auto actionCloseCourbe = new QAction("Fermer courbe sélectionnée", this);

    // Connecter les actions aux gestionnaires d'événements
    connect(actionSelectCourbe, &QAction::triggered, this, &MainWindow::onSelectCourbeTriggered);
    connect(actionCreateCourbe, &QAction::triggered, this, &MainWindow::onCreateCourbeTriggered);
    connect(actionCloseCourbe, &QAction::triggered, this, &MainWindow::onCloseCourbeTriggered);

    courbesMenu->addAction(actionSelectCourbe);
    courbesMenu->addAction(actionCreateCourbe);
    courbesMenu->addSeparator(); // Séparateur visuel
    courbesMenu->addAction(actionCloseCourbe);
    m_customMenuBar->addMenu(courbesMenu);

    // État initial : menu désactivé jusqu'à sélection d'un essai
    actionCloseCourbe->setEnabled(false); // Désactivé par défaut
    courbesMenu->setEnabled(false); // Dépend de la sélection d'un essai
    qDebug() << "'Courbes' menu created";
    std::cout << "'Courbes' menu created" << std::endl;

    qDebug() << "Adding menu bar to toolbar layout...";
    std::cout << "Adding menu bar to toolbar layout..." << std::endl;
    // Ajoute la barre de menus à gauche de la barre d'outils
    toolBarLayout->addWidget(m_customMenuBar);
    toolBarLayout->addStretch(); // Espaceur pour pousser le login à droite
    qDebug() << "Menu bar added to toolbar layout";
    std::cout << "Menu bar added to toolbar layout" << std::endl;

    qDebug() << "Creating login widgets...";
    std::cout << "Creating login widgets..." << std::endl;
    // Création du widget de login à droite (initialement affiché)
    loginWidget = new QWidget(this);
    loginLayout = new QHBoxLayout(loginWidget);
    loginLayout->setContentsMargins(0, 0, 0, 0);
    loginLayout->addStretch();
    userLabel = new QLabel("Utilisateur :", this); // Label pour l'identifiant
    usernameLineEdit = new QLineEdit(this); // Champ de saisie identifiant
    usernameLineEdit->setPlaceholderText("Nom d'utilisateur");
    usernameLineEdit->setToolTip("Nom d'utilisateur (Appuyez sur Entrée pour vous connecter)");
    passLabel = new QLabel("Mot de passe :", this); // Label pour le mot de passe
    passwordLineEdit = new QLineEdit(this); // Champ de saisie mot de passe
    passwordLineEdit->setEchoMode(QLineEdit::Password); // Cache le mot de passe
    passwordLineEdit->setPlaceholderText("Mot de passe");
    passwordLineEdit->setToolTip("Mot de passe (Appuyez sur Entrée pour vous connecter)");
    loginButton = new QPushButton("Connexion", this); // Bouton de connexion
    loginButton->setToolTip("Se connecter (Ctrl+L pour focus, Entrée pour valider)");
    // Ajoute les widgets de login dans le layout
    loginLayout->addWidget(userLabel);
    loginLayout->addWidget(usernameLineEdit);
    loginLayout->addWidget(passLabel);
    loginLayout->addWidget(passwordLineEdit);
    loginLayout->addWidget(loginButton);
    toolBarLayout->addWidget(loginWidget);
    qDebug() << "Login widgets created and added to layout";
    std::cout << "Login widgets created and added to layout" << std::endl;

    qDebug() << "Adding toolbar widget to toolbar...";
    std::cout << "Adding toolbar widget to toolbar..." << std::endl;
    // Ajoute le widget principal à la barre d'outils
    loginToolBar->addWidget(toolBarWidget);
    qDebug() << "Toolbar widget added to toolbar";
    std::cout << "Toolbar widget added to toolbar" << std::endl;

    qDebug() << "Connecting login button signal...";
    std::cout << "Connecting login button signal..." << std::endl;
    // Connexion du bouton "Connexion" à la logique d'authentification
    connect(loginButton, &QPushButton::clicked, this, [this] {
        const QString user = usernameLineEdit->text();
        const QString pass = passwordLineEdit->text();

        // Validation côté client avant envoi à l'API
        if (user.isEmpty() || pass.isEmpty()) {
            if (statusBar()) statusBar()->showMessage("Erreur : veuillez saisir un nom d'utilisateur et un mot de passe.");
            return;
        }

        // Tentative d'authentification via l'API REST
        tryLogin(user, pass);
    });

    // Connexion des champs de saisie pour la touche Entrée
    connect(usernameLineEdit, &QLineEdit::returnPressed, this, &MainWindow::onLoginFieldEnterPressed);
    connect(passwordLineEdit, &QLineEdit::returnPressed, this, &MainWindow::onLoginFieldEnterPressed);

    qDebug() << "Login button and Enter key signals connected";
    std::cout << "Login button and Enter key signals connected" << std::endl;

    qDebug() << "Setting up API client...";
    std::cout << "Setting up API client..." << std::endl;
    // Initialise le client API et les connexions d'authentification
    setupApiClient();
    qDebug() << "API client setup completed";
    std::cout << "API client setup completed" << std::endl;

    qDebug() << "Setting up keyboard shortcuts...";
    std::cout << "Setting up keyboard shortcuts..." << std::endl;
    // Initialise les raccourcis clavier et la navigation
    setupKeyboardShortcuts();
    qDebug() << "Keyboard shortcuts setup completed";
    std::cout << "Keyboard shortcuts setup completed" << std::endl;

    qDebug() << "Setting up status bar and window title...";
    std::cout << "Setting up status bar and window title..." << std::endl;
    // Affiche un message d'état au lancement avec les raccourcis disponibles
    if (statusBar()) {
        statusBar()->showMessage("Prêt. Raccourcis : Ctrl+L (focus), Ctrl+Q (quitter), Entrée (connexion)");
    }
    updateWindowTitle();
    qDebug() << "Status bar and window title set";
    std::cout << "Status bar and window title set" << std::endl;

    qDebug() << "Showing login overlay...";
    std::cout << "Showing login overlay..." << std::endl;
    // Affiche l'overlay de connexion
    showLoginOverlay();
        qDebug() << "Login overlay shown";
        std::cout << "Login overlay shown" << std::endl;

        qDebug() << "=== MAINWINDOW CONSTRUCTOR END ===";
        std::cout << "=== MAINWINDOW CONSTRUCTOR END ===" << std::endl;
    } catch (const std::exception& e) {
        qDebug() << "EXCEPTION IN MAINWINDOW CONSTRUCTOR:" << e.what();
        std::cout << "EXCEPTION IN MAINWINDOW CONSTRUCTOR: " << e.what() << std::endl;
        throw; // Re-throw to let main() handle it
    } catch (...) {
        qDebug() << "UNKNOWN EXCEPTION IN MAINWINDOW CONSTRUCTOR";
        std::cout << "UNKNOWN EXCEPTION IN MAINWINDOW CONSTRUCTOR" << std::endl;
        throw; // Re-throw to let main() handle it
    }
}

MainWindow::~MainWindow() {
    qDebug() << "=== MAINWINDOW DESTRUCTOR START ===";
    std::cout << "=== MAINWINDOW DESTRUCTOR START ===" << std::endl;

    qDebug() << "Cleaning up widgets...";
    std::cout << "Cleaning up widgets..." << std::endl;
    // Nettoie explicitement tous les widgets pour éviter les fuites mémoire
    cleanupOperatorWidgets();
    cleanupLoginWidgets();
    qDebug() << "Widgets cleaned up";
    std::cout << "Widgets cleaned up" << std::endl;

    qDebug() << "Cleaning up API client...";
    std::cout << "Cleaning up API client..." << std::endl;
    // Le client API sera automatiquement détruit car il a 'this' comme parent
    if (m_apiClient) {
        m_apiClient = nullptr;
    }
    qDebug() << "API client cleaned up";
    std::cout << "API client cleaned up" << std::endl;

    qDebug() << "Hiding login overlay...";
    std::cout << "Hiding login overlay..." << std::endl;
    hideLoginOverlay();
    qDebug() << "Login overlay hidden";
    std::cout << "Login overlay hidden" << std::endl;

    qDebug() << "Deleting UI...";
    std::cout << "Deleting UI..." << std::endl;
    delete ui;
    qDebug() << "UI deleted";
    std::cout << "UI deleted" << std::endl;

    qDebug() << "=== MAINWINDOW DESTRUCTOR END ===";
    std::cout << "=== MAINWINDOW DESTRUCTOR END ===" << std::endl;
}

// Nettoie les widgets d'opérateur
void MainWindow::cleanupOperatorWidgets() {
    qDebug() << "Cleaning up operator widgets...";

    // Nettoie le widget opérateur principal
    if (operatorWidget) {
        operatorWidget->hide();
        operatorWidget->deleteLater();
        operatorWidget = nullptr;
    }

    // Réinitialise les pointeurs des widgets enfants
    operatorStatusLabel = nullptr;
    logoutButton = nullptr;

    qDebug() << "Operator widgets cleaned up";
}

// Nettoie les widgets de login
void MainWindow::cleanupLoginWidgets() {
    qDebug() << "Cleaning up login widgets...";

    // Supprime l'ancien widget login s'il existe
    if (loginWidget) {
        loginWidget->hide();
        loginWidget->deleteLater();
        loginWidget = nullptr;
    }

    // Réinitialise les pointeurs des widgets enfants
    userLabel = nullptr;
    usernameLineEdit = nullptr;
    passLabel = nullptr;
    passwordLineEdit = nullptr;
    loginButton = nullptr;
    loginLayout = nullptr;

    qDebug() << "Login widgets cleaned up";
}

// Affiche les widgets de login à droite de la barre d'outils
void MainWindow::setupLoginWidgets() {
    qDebug() << "Setting up login widgets...";

    // Nettoie d'abord les widgets existants
    cleanupOperatorWidgets();
    cleanupLoginWidgets();

    showLoginOverlay();
    // Création du widget login (identique à l'initialisation du constructeur)
    loginWidget = new QWidget(this);
    loginLayout = new QHBoxLayout(loginWidget);
    loginLayout->setContentsMargins(0, 0, 0, 0);
    loginLayout->addStretch();
    userLabel = new QLabel("Utilisateur :", this);
    usernameLineEdit = new QLineEdit(this);
    usernameLineEdit->setPlaceholderText("Nom d'utilisateur");
    usernameLineEdit->setToolTip("Nom d'utilisateur (Appuyez sur Entrée pour vous connecter)");
    passLabel = new QLabel("Mot de passe :", this);
    passwordLineEdit = new QLineEdit(this);
    passwordLineEdit->setEchoMode(QLineEdit::Password);
    passwordLineEdit->setPlaceholderText("Mot de passe");
    passwordLineEdit->setToolTip("Mot de passe (Appuyez sur Entrée pour vous connecter)");
    loginButton = new QPushButton("Connexion", this);
    loginButton->setToolTip("Se connecter (Ctrl+L pour focus, Entrée pour valider)");
    loginLayout->addWidget(userLabel);
    loginLayout->addWidget(usernameLineEdit);
    loginLayout->addWidget(passLabel);
    loginLayout->addWidget(passwordLineEdit);
    loginLayout->addWidget(loginButton);
    // Ajoute le loginWidget à la fin du layout de la barre d'outils
    if (loginToolBar && !loginToolBar->children().empty()) {
        const QWidget *toolBarWidget = qobject_cast<QWidget
            *>(loginToolBar->widgetForAction(loginToolBar->actions().first()));
        if (toolBarWidget) {
            if (auto *toolBarLayout = qobject_cast<QHBoxLayout *>(toolBarWidget->layout())) {
                toolBarLayout->addWidget(loginWidget);
            }
        }
    }
    loginWidget->show();
    // Connexion du bouton login à la logique d'authentification
    connect(loginButton, &QPushButton::clicked, this, [this] {
        const QString user = usernameLineEdit->text();
        const QString pass = passwordLineEdit->text();

        // Validation des champs avant l'envoi
        if (user.isEmpty() || pass.isEmpty()) {
            if (statusBar()) statusBar()->showMessage("Erreur : veuillez saisir un nom d'utilisateur et un mot de passe.");
            return;
        }

        // Lancement de l'authentification via l'API
        tryLogin(user, pass);
    });

    // Connexion des champs de saisie pour la touche Entrée
    connect(usernameLineEdit, &QLineEdit::returnPressed, this, &MainWindow::onLoginFieldEnterPressed);
    connect(passwordLineEdit, &QLineEdit::returnPressed, this, &MainWindow::onLoginFieldEnterPressed);

    // Met à jour l'ordre de tabulation et les raccourcis
    setTabOrder();
    updateShortcutsState();
}

// Affiche le widget d'état opérateur (connecté) à droite de la barre d'outils
void MainWindow::setupOperatorWidgets(const QString &operatorName) {
    qDebug() << "Setting up operator widgets for:" << operatorName;

    // Nettoie d'abord les widgets existants
    cleanupLoginWidgets();
    cleanupOperatorWidgets();

    hideLoginOverlay();

    // Crée le widget opérateur (affiche le nom et le bouton déconnexion)
    operatorWidget = new QWidget(this);
    const auto operatorLayout = new QHBoxLayout(operatorWidget);
    operatorLayout->setContentsMargins(0, 0, 0, 0);
    operatorLayout->addStretch();
    operatorStatusLabel = new QLabel("Connecté : " + operatorName, this);
    logoutButton = new QPushButton("Déconnexion", this);
    logoutButton->setToolTip("Se déconnecter (Ctrl+D)");
    operatorLayout->addWidget(operatorStatusLabel);
    operatorLayout->addWidget(logoutButton);
    // Ajoute le widget opérateur à la fin du layout de la barre d'outils
    if (loginToolBar && !loginToolBar->children().empty()) {
        const QWidget *toolBarWidget = qobject_cast<QWidget
            *>(loginToolBar->widgetForAction(loginToolBar->actions().first()));
        if (toolBarWidget) {
            if (const auto toolBarLayout = qobject_cast<QHBoxLayout *>(toolBarWidget->layout())) {
                toolBarLayout->addWidget(operatorWidget);
            }
        }
    }
    operatorWidget->show();

    // Connexion du bouton déconnexion avec une méthode séparée pour éviter les problèmes de destruction
    connect(logoutButton, &QPushButton::clicked, this, &MainWindow::onLogoutRequested);

    // Met à jour l'état des raccourcis pour le mode connecté
    updateShortcutsState();

    qDebug() << "Operator widgets setup completed";
}

// Passe l'interface en mode "non connecté" (login)
void MainWindow::switchToLoginState() {
    qDebug() << "Switching to login state";

    // Réinitialise l'état d'authentification
    m_isAuthenticating = false;

    setupLoginWidgets(); // Affiche les widgets de login
    showLoginOverlay();

    if (statusBar()) {
        statusBar()->showMessage("Déconnecté. Raccourcis : Ctrl+L (focus), Ctrl+Q (quitter), Entrée (connexion)");
    } else {
        qDebug() << "Status bar is null during switch to login state";
    }

    m_currentOperatorName.clear();
    updateWindowTitle();

    // Met à jour l'état des raccourcis pour le mode déconnecté
    updateShortcutsState();

    qDebug() << "Switched to login state";
}

// Affiche un overlay semi-transparent pour bloquer l'interface tant que l'utilisateur n'est pas connecté
void MainWindow::showLoginOverlay() {
    qDebug() << "showLoginOverlay() called";
    std::cout << "showLoginOverlay() called" << std::endl;

    QWidget *area = centralWidget();
    qDebug() << "Central widget:" << area;
    std::cout << "Central widget: " << (area ? "exists" : "null") << std::endl;

    if (!area) {
        qDebug() << "No central widget, returning early";
        std::cout << "No central widget, returning early" << std::endl;
        return;
    }

    if (!loginOverlay) {
        qDebug() << "Creating new login overlay";
        std::cout << "Creating new login overlay" << std::endl;
        loginOverlay = new QWidget(area);
        loginOverlay->setStyleSheet("background: rgba(0,0,0,0.4);"); // Fond semi-transparent
        loginOverlay->setAttribute(Qt::WA_TransparentForMouseEvents, false); // Capture les clics
        loginOverlay->setGeometry(area->rect()); // Couvre toute la zone centrale
        loginOverlay->raise(); // Au premier plan
        loginOverlay->show();
        qDebug() << "Login overlay created and shown";
        std::cout << "Login overlay created and shown" << std::endl;
    } else {
        qDebug() << "Updating existing login overlay";
        std::cout << "Updating existing login overlay" << std::endl;
        loginOverlay->setParent(area);
        loginOverlay->setGeometry(area->rect()); // Redimensionne si nécessaire
        loginOverlay->raise();
        loginOverlay->show();
        qDebug() << "Login overlay updated and shown";
        std::cout << "Login overlay updated and shown" << std::endl;
    }
}

// Cache l'overlay de connexion
void MainWindow::hideLoginOverlay() const {
    qDebug() << "hideLoginOverlay() called";
    std::cout << "hideLoginOverlay() called" << std::endl;

    if (loginOverlay) {
        qDebug() << "Hiding login overlay";
        std::cout << "Hiding login overlay" << std::endl;
        loginOverlay->hide();
        qDebug() << "Login overlay hidden";
        std::cout << "Login overlay hidden" << std::endl;
    } else {
        qDebug() << "No login overlay to hide";
        std::cout << "No login overlay to hide" << std::endl;
    }
}

// S'assure que l'overlay suit la taille de la fenêtre
void MainWindow::resizeEvent(QResizeEvent *event) {
    QMainWindow::resizeEvent(event);
    if (loginOverlay && loginOverlay->isVisible() && centralWidget()) {
        loginOverlay->setGeometry(centralWidget()->rect());
    }
}

// Passe l'interface en mode "opérateur connecté"
void MainWindow::switchToOperatorState(const QString &operatorName) {
    m_currentOperatorName = operatorName;
    setupOperatorWidgets(operatorName); // Affiche le widget opérateur
    if (statusBar()) {
        statusBar()->showMessage("Connecté en tant que " + operatorName + ". Raccourcis : Ctrl+D (déconnexion), F5 (actualiser), Ctrl+Q (quitter)");
    }
    updateWindowTitle();
}

// Initialise le client API et connecte les signaux d'authentification
void MainWindow::setupApiClient() {
    qDebug() << "Initializing API client...";

    // Création du client API
    m_apiClient = new ApiClient(this);

    // Connexion des signaux d'authentification avec Qt::QueuedConnection pour éviter les problèmes de destruction
    connect(m_apiClient, &ApiClient::loginSuccessful, this, &MainWindow::onLoginSuccessful, Qt::QueuedConnection);
    connect(m_apiClient, &ApiClient::loginFailed, this, &MainWindow::onLoginFailed, Qt::QueuedConnection);

    // Connexion du signal de déconnexion pour gérer les déconnexions automatiques
    connect(m_apiClient, &ApiClient::loggedOut, this, &MainWindow::switchToLoginState, Qt::QueuedConnection);

    qDebug() << "API client initialized and signals connected";
}

// Active/désactive les boutons de connexion
void MainWindow::setLoginButtonsEnabled(bool enabled) {
    qDebug() << "Setting login buttons enabled:" << enabled;

    if (loginButton) {
        loginButton->setEnabled(enabled);
        loginButton->setText(enabled ? "Connexion" : "Connexion en cours...");
    } else {
        qDebug() << "Login button is null";
    }

    if (usernameLineEdit) {
        usernameLineEdit->setEnabled(enabled);
    } else {
        qDebug() << "Username line edit is null";
    }

    if (passwordLineEdit) {
        passwordLineEdit->setEnabled(enabled);
    } else {
        qDebug() << "Password line edit is null";
    }
}

// Lance l'authentification via l'API
void MainWindow::tryLogin(const QString &username, const QString &password) {
    qDebug() << "Attempting login for user:" << username;

    if (!m_apiClient) {
        qWarning() << "API client not initialized!";
        if (statusBar()) statusBar()->showMessage("Erreur : client API non initialisé.");
        return;
    }

    // Évite les tentatives multiples simultanées
    if (m_isAuthenticating) {
        qDebug() << "Authentication already in progress, ignoring request";
        return;
    }

    // Marque le début de l'authentification
    m_isAuthenticating = true;
    setLoginButtonsEnabled(false);

    if (statusBar()) {
        statusBar()->showMessage("Authentification en cours...");
    }

    // Lance l'authentification via l'API
    m_apiClient->login(username, password);
}

// Gestionnaire de succès d'authentification
void MainWindow::onLoginSuccessful(const AuthManager::UserInfo& userInfo) {
    qDebug() << "Login successful for user:" << userInfo.username << "with role:" << userInfo.role;

    // Vérification défensive de l'état
    if (!m_isAuthenticating) {
        qWarning() << "Received login success but not in authenticating state";
        return;
    }

    // Réinitialise l'état d'authentification
    m_isAuthenticating = false;
    setLoginButtonsEnabled(true);

    // Efface le mot de passe pour la sécurité
    if (passwordLineEdit) {
        passwordLineEdit->clear();
    } else {
        qDebug() << "Password field is null during login success";
    }

    // Passe en mode connecté
    switchToOperatorState(userInfo.username);

    if (statusBar()) {
        statusBar()->showMessage(QString("Connecté en tant que %1 (%2)").arg(userInfo.username, userInfo.role));
    } else {
        qDebug() << "Status bar is null during login success";
    }
}

// Gestionnaire d'échec d'authentification
void MainWindow::onLoginFailed(const NetworkError& error) {
    qDebug() << "Login failed:" << error.message();

    // Vérification défensive de l'état
    if (!m_isAuthenticating) {
        qWarning() << "Received login failure but not in authenticating state";
        return;
    }

    // Réinitialise l'état d'authentification
    m_isAuthenticating = false;
    setLoginButtonsEnabled(true);

    // Efface le mot de passe
    if (passwordLineEdit) {
        passwordLineEdit->clear();
    } else {
        qDebug() << "Password field is null during login failure";
    }

    // Affiche le message d'erreur
    QString errorMessage;
    switch (error.type()) {
        case NetworkError::Type::AuthenticationError:
            errorMessage = "Erreur : identifiants invalides.";
            break;
        case NetworkError::Type::NetworkError:
            errorMessage = "Erreur : problème de connexion réseau.";
            break;
        case NetworkError::Type::TimeoutError:
            errorMessage = "Erreur : délai d'attente dépassé.";
            break;
        case NetworkError::Type::ServerError:
            errorMessage = "Erreur : problème serveur.";
            break;
        default:
            errorMessage = QString("Erreur : %1").arg(error.message());
            break;
    }

    if (statusBar()) {
        statusBar()->showMessage(errorMessage);
    }
}

// Gestionnaire de demande de déconnexion
void MainWindow::onLogoutRequested() {
    qDebug() << "Logout requested";

    // Vérification défensive pour éviter les appels multiples
    if (m_isAuthenticating) {
        qDebug() << "Authentication in progress, ignoring logout request";
        return;
    }

    // Désactive temporairement le bouton de déconnexion pour éviter les clics multiples
    if (logoutButton) {
        logoutButton->setEnabled(false);
        logoutButton->setText("Déconnexion...");
    }

    // Déconnexion via l'API client avec vérification de sécurité
    if (m_apiClient) {
        qDebug() << "Calling API client logout";
        m_apiClient->logout();
    } else {
        qWarning() << "API client is null during logout";
    }

    // Passe immédiatement en mode déconnecté
    qDebug() << "Switching to login state";
    switchToLoginState();

    qDebug() << "Logout completed";
}

// ========================================
// MÉTHODES DE GESTION DES RACCOURCIS CLAVIER
// ========================================

// Initialise tous les raccourcis clavier
void MainWindow::setupKeyboardShortcuts() {
    qDebug() << "Setting up keyboard shortcuts...";

    // Ctrl+L : Focus sur les champs de connexion
    m_shortcutFocusLogin = new QShortcut(QKeySequence("Ctrl+L"), this);
    connect(m_shortcutFocusLogin, &QShortcut::activated, this, &MainWindow::onFocusLoginShortcut);

    // Ctrl+Q : Quitter l'application
    m_shortcutQuit = new QShortcut(QKeySequence("Ctrl+Q"), this);
    connect(m_shortcutQuit, &QShortcut::activated, this, &MainWindow::onQuitShortcut);

    // Ctrl+D : Déconnexion (quand connecté)
    m_shortcutLogout = new QShortcut(QKeySequence("Ctrl+D"), this);
    connect(m_shortcutLogout, &QShortcut::activated, this, &MainWindow::onLogoutShortcut);

    // Escape : Annuler l'opération courante
    m_shortcutEscape = new QShortcut(QKeySequence("Escape"), this);
    connect(m_shortcutEscape, &QShortcut::activated, this, &MainWindow::onEscapeShortcut);

    // F5 : Actualiser/recharger les données
    m_shortcutRefresh = new QShortcut(QKeySequence("F5"), this);
    connect(m_shortcutRefresh, &QShortcut::activated, this, &MainWindow::onRefreshShortcut);

    // Met à jour l'état initial des raccourcis
    updateShortcutsState();

    qDebug() << "Keyboard shortcuts setup completed";
}

// Met à jour l'état des raccourcis selon le contexte
void MainWindow::updateShortcutsState() {
    qDebug() << "Updating shortcuts state...";

    // Détermine si l'utilisateur est connecté
    bool isLoggedIn = (operatorWidget != nullptr && operatorWidget->isVisible());
    bool isLoginState = (loginWidget != nullptr && loginWidget->isVisible());

    // Ctrl+L : Actif seulement en mode login
    if (m_shortcutFocusLogin) {
        m_shortcutFocusLogin->setEnabled(isLoginState);
    }

    // Ctrl+D : Actif seulement quand connecté
    if (m_shortcutLogout) {
        m_shortcutLogout->setEnabled(isLoggedIn);
    }

    // Ctrl+Q : Toujours actif
    if (m_shortcutQuit) {
        m_shortcutQuit->setEnabled(true);
    }

    // Escape : Toujours actif
    if (m_shortcutEscape) {
        m_shortcutEscape->setEnabled(true);
    }

    // F5 : Actif quand connecté
    if (m_shortcutRefresh) {
        m_shortcutRefresh->setEnabled(isLoggedIn);
    }

    qDebug() << "Shortcuts state updated - Login state:" << isLoginState << "Logged in:" << isLoggedIn;
}

// Configure l'ordre de tabulation des widgets
void MainWindow::setTabOrder() {
    qDebug() << "Setting tab order...";

    if (loginWidget && loginWidget->isVisible()) {
        // Ordre de tabulation pour le mode login
        if (usernameLineEdit && passwordLineEdit && loginButton) {
            QWidget::setTabOrder(usernameLineEdit, passwordLineEdit);
            QWidget::setTabOrder(passwordLineEdit, loginButton);
            qDebug() << "Tab order set for login widgets";
        }
    }

    qDebug() << "Tab order configuration completed";
}

// Gestion globale des événements clavier
void MainWindow::keyPressEvent(QKeyEvent *event) {
    // Gestion spéciale de la touche Entrée pour les boutons par défaut
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter) {
        if (loginButton && loginButton->isVisible() && loginButton->isEnabled()) {
            // Si on est en mode login et que le bouton est actif
            if (usernameLineEdit && passwordLineEdit) {
                QWidget *focused = QApplication::focusWidget();
                if (focused == usernameLineEdit || focused == passwordLineEdit || focused == loginButton) {
                    onLoginFieldEnterPressed();
                    return;
                }
            }
        }
    }

    // Délègue à la classe parent pour les autres événements
    QMainWindow::keyPressEvent(event);
}

// ========================================
// GESTIONNAIRES DE RACCOURCIS CLAVIER
// ========================================

// Ctrl+L : Focus sur les champs de connexion
void MainWindow::onFocusLoginShortcut() {
    qDebug() << "Focus login shortcut activated";

    if (usernameLineEdit && usernameLineEdit->isVisible() && usernameLineEdit->isEnabled()) {
        usernameLineEdit->setFocus();
        usernameLineEdit->selectAll();

        if (statusBar()) {
            statusBar()->showMessage("Focus sur le champ utilisateur (Ctrl+L)", 2000);
        }

        qDebug() << "Focus set to username field";
    } else {
        qDebug() << "Username field not available for focus";
    }
}

// Ctrl+Q : Quitter l'application
void MainWindow::onQuitShortcut() {
    qDebug() << "Quit shortcut activated";

    // Affiche une boîte de dialogue de confirmation
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        "Confirmation de fermeture",
        "Êtes-vous sûr de vouloir quitter l'application ?",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        qDebug() << "User confirmed quit";
        if (statusBar()) {
            statusBar()->showMessage("Fermeture de l'application...");
        }
        QApplication::quit();
    } else {
        qDebug() << "User cancelled quit";
        if (statusBar()) {
            statusBar()->showMessage("Fermeture annulée", 2000);
        }
    }
}

// Ctrl+D : Déconnexion
void MainWindow::onLogoutShortcut() {
    qDebug() << "Logout shortcut activated";

    // Vérifie si l'utilisateur est connecté
    if (operatorWidget && operatorWidget->isVisible()) {
        if (statusBar()) {
            statusBar()->showMessage("Déconnexion via raccourci clavier (Ctrl+D)", 2000);
        }
        onLogoutRequested();
    } else {
        qDebug() << "Logout shortcut ignored - user not logged in";
        if (statusBar()) {
            statusBar()->showMessage("Aucune session active à fermer", 2000);
        }
    }
}

// Escape : Annuler l'opération courante
void MainWindow::onEscapeShortcut() {
    qDebug() << "Escape shortcut activated";

    // Annule l'authentification en cours
    if (m_isAuthenticating) {
        qDebug() << "Cancelling authentication";
        m_isAuthenticating = false;
        setLoginButtonsEnabled(true);

        if (statusBar()) {
            statusBar()->showMessage("Authentification annulée", 2000);
        }
        return;
    }

    // Efface les champs de saisie si on est en mode login
    if (loginWidget && loginWidget->isVisible()) {
        if (usernameLineEdit) {
            usernameLineEdit->clear();
        }
        if (passwordLineEdit) {
            passwordLineEdit->clear();
        }
        if (usernameLineEdit) {
            usernameLineEdit->setFocus();
        }

        if (statusBar()) {
            statusBar()->showMessage("Champs de connexion effacés", 2000);
        }

        qDebug() << "Login fields cleared";
        return;
    }

    // Autres actions d'annulation selon le contexte
    if (statusBar()) {
        statusBar()->showMessage("Opération annulée (Escape)", 2000);
    }
}

// F5 : Actualiser/recharger les données
void MainWindow::onRefreshShortcut() {
    qDebug() << "Refresh shortcut activated";

    // Vérifie si l'utilisateur est connecté
    if (operatorWidget && operatorWidget->isVisible()) {
        if (statusBar()) {
            statusBar()->showMessage("Actualisation des données (F5)...", 3000);
        }

        // TODO: Implémenter l'actualisation des données selon les modules actifs
        // Par exemple : recharger les affaires, actualiser les graphiques, etc.

        qDebug() << "Data refresh requested";
    } else {
        qDebug() << "Refresh shortcut ignored - user not logged in";
        if (statusBar()) {
            statusBar()->showMessage("Connectez-vous pour actualiser les données", 2000);
        }
    }
}

// Gestion de la touche Entrée dans les champs de connexion
void MainWindow::onLoginFieldEnterPressed() {
    qDebug() << "Enter key pressed in login field";

    if (!usernameLineEdit || !passwordLineEdit || !loginButton) {
        qDebug() << "Login widgets not available";
        return;
    }

    if (!loginButton->isEnabled()) {
        qDebug() << "Login button is disabled";
        return;
    }

    const QString user = usernameLineEdit->text();
    const QString pass = passwordLineEdit->text();

    // Validation des champs avant l'envoi
    if (user.isEmpty() || pass.isEmpty()) {
        if (statusBar()) {
            statusBar()->showMessage("Erreur : veuillez saisir un nom d'utilisateur et un mot de passe.");
        }

        // Focus sur le premier champ vide
        if (user.isEmpty() && usernameLineEdit) {
            usernameLineEdit->setFocus();
        } else if (pass.isEmpty() && passwordLineEdit) {
            passwordLineEdit->setFocus();
        }

        return;
    }

    // Lancement de l'authentification via l'API
    if (statusBar()) {
        statusBar()->showMessage("Connexion via touche Entrée...");
    }

    tryLogin(user, pass);

    qDebug() << "Login triggered via Enter key";
}

// Méthodes de gestion des affaires

void MainWindow::onSelectAffaireTriggered()
{
    qDebug() << "Select affaire triggered";

    if (!m_apiClient || !m_apiClient->isAuthenticated()) {
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour sélectionner une affaire.");
        return;
    }

    showAffaireSelectionDialog();
}

void MainWindow::onCloseAffaireTriggered()
{
    qDebug() << "Close affaire triggered";

    if (!m_hasSelectedAffaire) {
        QMessageBox::information(this, "Information", "Aucune affaire n'est actuellement sélectionnée.");
        return;
    }

    closeSelectedAffaire();
}

void MainWindow::showAffaireSelectionDialog()
{
    qDebug() << "Showing affaire selection dialog";

    if (!m_apiClient) {
        qDebug() << "ERROR: API client not available";
        QMessageBox::warning(this, "Erreur", "Client API non disponible.");
        return;
    }

    if (!m_apiClient->isAuthenticated()) {
        qDebug() << "ERROR: User not authenticated";
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour accéder aux affaires.");
        return;
    }

    qDebug() << "API client available and user authenticated";
    qDebug() << "API base URL:" << m_apiClient->baseUrl();

    // Connecter les signaux pour recevoir la réponse des affaires
    connect(m_apiClient, &ApiClient::affairesReceived, this, &MainWindow::onAffairesArrayReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred, Qt::UniqueConnection);

    qDebug() << "Signals connected, making API request...";

    // Faire la requête pour récupérer les affaires avec pagination
    QUrlQuery params;
    params.addQueryItem("page", "1");
    params.addQueryItem("limit", "10");

    qDebug() << "Request parameters:" << params.toString();

    // Utiliser la méthode générique get avec les paramètres
    m_apiClient->get("affaires", params);

    qDebug() << "API request sent";

    if (statusBar()) {
        statusBar()->showMessage("Chargement des affaires...", 3000);
    }
}

void MainWindow::closeSelectedAffaire()
{
    qDebug() << "Closing selected affaire";

    if (!m_hasSelectedAffaire) {
        return;
    }

    QString affaireInfo = QString("Affaire %1 - %2")
        .arg(m_currentAffaire["numero"].toString())
        .arg(m_currentAffaire["client"].toString());

    int ret = QMessageBox::question(this, "Fermer l'affaire",
        QString("Voulez-vous vraiment fermer l'affaire sélectionnée ?\n\n%1").arg(affaireInfo),
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // Déselection en cascade : fermer tous les éléments dépendants
        // Ordre hiérarchique : Affaire > Essai > Courbe

        // Fermer automatiquement la courbe sélectionnée si il y en a une
        if (m_hasSelectedCourbe) {
            m_currentCourbe = QJsonObject();
            m_hasSelectedCourbe = false;
        }

        // Fermer automatiquement l'essai sélectionné si il y en a un
        if (m_hasSelectedEssai) {
            m_currentEssai = QJsonObject();
            m_hasSelectedEssai = false;
        }

        // Fermer l'affaire elle-même
        m_currentAffaire = QJsonObject();
        m_hasSelectedAffaire = false;
        updateAffaireMenuState(); // Met à jour l'état des menus
        updateWindowTitle(); // Reflète les changements dans le titre

        if (statusBar()) {
            statusBar()->showMessage("Affaire fermée", 3000);
        }

        qDebug() << "Affaire closed successfully";
    }
}

// Met à jour l'état du menu Affaires en fonction de la sélection actuelle
void MainWindow::updateAffaireMenuState()
{
    qDebug() << "Updating affaire menu state - m_hasSelectedAffaire:" << m_hasSelectedAffaire;

    // Rechercher le menu Affaires dans la barre de menus
    if (!m_customMenuBar) {
        qDebug() << "ERROR: Custom MenuBar is null";
        return;
    }

    for (QAction* action : m_customMenuBar->actions()) {
        QMenu* menu = action->menu();
        if (menu && menu->title() == "Affaires") {
            QList<QAction*> actions = menu->actions();

            if (actions.size() >= 2) {
                // actions[0] = "Sélectionner affaire..." / "Affaire: ..."
                // actions[1] = "Fermer affaire sélectionnée"

                // Activer/désactiver l'action de fermeture
                actions[1]->setEnabled(m_hasSelectedAffaire);

                // Mise à jour dynamique du texte de la première action
                if (m_hasSelectedAffaire) {
                    QString affaireText = QString("Affaire: %1 - %2")
                        .arg(m_currentAffaire["numero"].toString())
                        .arg(m_currentAffaire["client"].toString());
                    actions[0]->setText(affaireText);
                } else {
                    actions[0]->setText("Sélectionner affaire...");
                }

                qDebug() << "Close action enabled:" << actions[1]->isEnabled();
            } else {
                qDebug() << "ERROR: Not enough actions in Affaires menu";
            }
            break;
        }
    }

    // Propagation en cascade : mise à jour des menus dépendants
    updateEssaiMenuState();
}

void MainWindow::onAffairesArrayReceived(const ArrayResponse& response)
{
    qDebug() << "=== AFFAIRES ARRAY RESPONSE RECEIVED ===";
    qDebug() << "Response success:" << response.isSuccess();
    qDebug() << "Response has error:" << response.hasError();
    qDebug() << "Array size:" << response.data().size();

    // Déconnecter le signal pour éviter les appels multiples
    disconnect(m_apiClient, &ApiClient::affairesReceived, this, &MainWindow::onAffairesArrayReceived);
    disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
    disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

    if (!response.isSuccess()) {
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des affaires: %1").arg(response.message()));
        return;
    }

    QJsonArray affaires = response.data();

    if (affaires.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune affaire disponible.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowAffaireDialog(affaires);
}

// Méthodes de débogage

void MainWindow::onGenericResponseReceived(const QString& endpoint, const JsonResponse& response)
{
    qDebug() << "=== GENERIC RESPONSE RECEIVED ===";
    qDebug() << "Endpoint:" << endpoint;
    qDebug() << "Success:" << response.isSuccess();
    qDebug() << "Has error:" << response.hasError();

    if (response.hasError()) {
        qDebug() << "Error message:" << response.error().message();
    } else {
        qDebug() << "Response data keys:" << response.data().keys();
        qDebug() << "Response data:" << QJsonDocument(response.data()).toJson(QJsonDocument::Compact);
    }

    // Si c'est une réponse pour les affaires et qu'on n'a pas reçu le signal spécifique
    if (endpoint == "affaires" || endpoint.contains("affaires")) {
        qDebug() << "This is an affaires response, but we received it via generic signal.";
        qDebug() << "This might indicate the specific affairesReceived signal wasn't emitted.";
        qDebug() << "Handling manually as fallback...";

        // Déconnecter les signaux de débogage
        disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
        disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

        // Essayer de traiter la réponse manuellement
        handleAffairesResponseManually(response);
    } else if (endpoint == "essais" || endpoint.contains("essais")) {
        qDebug() << "This is an essais response, but we received it via generic signal.";
        qDebug() << "This might indicate the specific essaisReceived signal wasn't emitted.";
        qDebug() << "Handling manually as fallback...";

        // Déconnecter les signaux de débogage
        disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
        disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

        // Essayer de traiter la réponse manuellement
        handleEssaisResponseManually(response);
    } else if (endpoint == "courbes" || endpoint.contains("courbes")) {
        qDebug() << "This is a courbes response, but we received it via generic signal.";
        qDebug() << "This might indicate the specific courbesReceived signal wasn't emitted.";
        qDebug() << "Handling manually as fallback...";

        // Déconnecter les signaux de débogage
        disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
        disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

        // Essayer de traiter la réponse manuellement
        handleCourbesResponseManually(response);
    } else {
        qDebug() << "Generic response for non-affaires/essais/courbes endpoint, ignoring.";
    }
}

void MainWindow::onApiErrorOccurred(const QString& endpoint, const NetworkError& error)
{
    qDebug() << "=== API ERROR OCCURRED ===";
    qDebug() << "Endpoint:" << endpoint;
    qDebug() << "Error type:" << static_cast<int>(error.type());
    qDebug() << "Error message:" << error.message();

    // Déconnecter les signaux de débogage
    disconnect(m_apiClient, &ApiClient::affairesReceived, this, &MainWindow::onAffairesArrayReceived);
    disconnect(m_apiClient, &ApiClient::essaisReceived, this, &MainWindow::onEssaisArrayReceived);
    disconnect(m_apiClient, &ApiClient::courbesReceived, this, &MainWindow::onCourbesArrayReceived);
    disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
    disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

    QString errorContext = "données";
    if (endpoint.contains("affaires")) {
        errorContext = "affaires";
    } else if (endpoint.contains("essais")) {
        errorContext = "essais";
    } else if (endpoint.contains("courbes")) {
        errorContext = "courbes";
    }

    if (statusBar()) {
        statusBar()->showMessage(QString("Erreur lors du chargement des %1").arg(errorContext), 5000);
    }

    QMessageBox::warning(this, "Erreur API",
        QString("Erreur lors de la requête vers %1:\n%2").arg(endpoint, error.message()));
}

void MainWindow::handleAffairesResponseManually(const JsonResponse& response)
{
    qDebug() << "=== HANDLING AFFAIRES RESPONSE MANUALLY ===";

    if (!response.isSuccess()) {
        qDebug() << "Response not successful:" << response.message();
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des affaires: %1").arg(response.message()));
        return;
    }

    QJsonObject data = response.data();
    QJsonArray affaires;

    qDebug() << "Response data keys:" << data.keys();
    qDebug() << "Full response:" << QJsonDocument(data).toJson(QJsonDocument::Compact);

    // Analyse de la structure de réponse de l'API
    // L'API peut retourner soit des données paginées, soit des données directes
    if (data.contains("data") && data["data"].isArray()) {
        qDebug() << "Found paginated data structure";
        affaires = data["data"].toArray(); // Structure paginée standard
    } else if (data.isEmpty()) {
        qDebug() << "Response data is empty, this might be a direct array response";
        QMessageBox::warning(this, "Erreur", "Réponse vide du serveur.");
        return;
    } else {
        qDebug() << "Unexpected response format, trying to interpret as direct data";
        // Tentative d'interprétation comme objet affaire unique
        if (data.contains("id") && data.contains("numero")) {
            // Conversion d'un objet unique en tableau
            affaires.append(data);
        } else {
            QMessageBox::warning(this, "Erreur", "Format de réponse inattendu pour les affaires.");
            return;
        }
    }

    qDebug() << "Found" << affaires.size() << "affaires";

    if (affaires.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune affaire disponible.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowAffaireDialog(affaires);
}

void MainWindow::createAndShowAffaireDialog(const QJsonArray& affaires)
{
    qDebug() << "=== CREATING AND SHOWING AFFAIRE DIALOG ===";
    qDebug() << "Number of affaires:" << affaires.size();

    if (affaires.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune affaire disponible.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    QDialog dialog(this);
    dialog.setWindowTitle("Sélectionner une affaire");
    dialog.setModal(true);
    dialog.resize(600, 400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // Ajouter un label d'information
    QLabel* infoLabel = new QLabel("Sélectionnez une affaire dans la liste ci-dessous:", &dialog);
    layout->addWidget(infoLabel);

    // Créer la liste des affaires
    QListWidget* listWidget = new QListWidget(&dialog);
    layout->addWidget(listWidget);

    qDebug() << "Adding affaires to list widget...";

    // Population de la liste avec les données d'affaires
    for (const QJsonValue& value : affaires) {
        QJsonObject affaire = value.toObject();
        // Format d'affichage : Numéro - Client (Statut)
        QString itemText = QString("%1 - %2 (%3)")
            .arg(affaire["numero"].toString())
            .arg(affaire["client"].toString())
            .arg(affaire["statut"].toString());

        qDebug() << "Adding affaire:" << itemText;

        QListWidgetItem* item = new QListWidgetItem(itemText);
        item->setData(Qt::UserRole, affaire); // Stockage des données complètes
        listWidget->addItem(item);
    }

    qDebug() << "List widget now has" << listWidget->count() << "items";

    // Ajouter les boutons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* selectButton = new QPushButton("Sélectionner", &dialog);
    QPushButton* cancelButton = new QPushButton("Annuler", &dialog);

    selectButton->setEnabled(false); // Désactivé par défaut

    buttonLayout->addStretch();
    buttonLayout->addWidget(selectButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // Connecter les signaux
    connect(listWidget, &QListWidget::itemSelectionChanged, [selectButton, listWidget]() {
        selectButton->setEnabled(listWidget->currentItem() != nullptr);
    });

    connect(listWidget, &QListWidget::itemDoubleClicked, [&dialog]() {
        dialog.accept();
    });

    connect(selectButton, &QPushButton::clicked, [&dialog]() {
        dialog.accept();
    });

    connect(cancelButton, &QPushButton::clicked, [&dialog]() {
        dialog.reject();
    });

    qDebug() << "Showing dialog...";

    // Afficher le dialogue et traiter le résultat
    if (dialog.exec() == QDialog::Accepted) {
        QListWidgetItem* selectedItem = listWidget->currentItem();
        if (selectedItem) {
            m_currentAffaire = selectedItem->data(Qt::UserRole).toJsonObject();
            m_hasSelectedAffaire = true;

            qDebug() << "Affaire object stored:" << QJsonDocument(m_currentAffaire).toJson(QJsonDocument::Compact);

            updateAffaireMenuState();
            updateWindowTitle();

            QString affaireInfo = QString("Affaire %1 - %2 sélectionnée")
                .arg(m_currentAffaire["numero"].toString())
                .arg(m_currentAffaire["client"].toString());

            if (statusBar()) {
                statusBar()->showMessage(affaireInfo, 5000);
            }

            qDebug() << "Affaire selected:" << m_currentAffaire["numero"].toString();
        }
    } else {
        qDebug() << "Dialog was cancelled";
    }
}

// Met à jour le titre de la fenêtre pour refléter l'état actuel de l'application
void MainWindow::updateWindowTitle()
{
    QString baseTitle = "Supervision Banc d'Essai Hydraulique";

    if (m_currentOperatorName.isEmpty()) {
        // Mode déconnecté : affichage minimal
        setWindowTitle(baseTitle + " - [État: Non connecté]");
    } else {
        // Mode connecté : construction hiérarchique du titre
        QString title = baseTitle + " - [Connecté en tant que " + m_currentOperatorName + "]";

        // Ajout des informations hiérarchiques si disponibles
        if (m_hasSelectedAffaire && !m_currentAffaire.isEmpty()) {
            // Niveau 1 : Affaire sélectionnée
            QString affaireInfo = QString("Affaire: %1 - %2")
                .arg(m_currentAffaire["numero"].toString())
                .arg(m_currentAffaire["client"].toString());
            title += " - " + affaireInfo;

            if (m_hasSelectedEssai && !m_currentEssai.isEmpty()) {
                // Niveau 2 : Essai sélectionné
                QString essaiInfo = QString("Essai: %1 (%2)")
                    .arg(m_currentEssai["id"].toString())
                    .arg(m_currentEssai["type"].toString());
                title += " - " + essaiInfo;

                if (m_hasSelectedCourbe && !m_currentCourbe.isEmpty()) {
                    // Niveau 3 : Courbe sélectionnée
                    QString courbeInfo = QString("Courbe: %1 (%2)")
                        .arg(m_currentCourbe["id"].toString())
                        .arg(m_currentCourbe["type_courbe"].toString());
                    title += " - " + courbeInfo;
                }
            }
        }

        setWindowTitle(title);
    }

    qDebug() << "Window title updated to:" << windowTitle();
}

// ========================================
// MÉTHODES DE GESTION DES ESSAIS
// ========================================

// Gestionnaire de sélection d'essai
void MainWindow::onSelectEssaiTriggered()
{
    qDebug() << "Select essai triggered";

    if (!m_apiClient || !m_apiClient->isAuthenticated()) {
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour sélectionner un essai.");
        return;
    }

    if (!m_hasSelectedAffaire) {
        QMessageBox::warning(this, "Erreur", "Vous devez d'abord sélectionner une affaire.");
        return;
    }

    showEssaiSelectionDialog();
}

// Gestionnaire de fermeture d'essai
void MainWindow::onCloseEssaiTriggered()
{
    qDebug() << "Close essai triggered";

    if (!m_hasSelectedEssai) {
        QMessageBox::information(this, "Information", "Aucun essai n'est actuellement sélectionné.");
        return;
    }

    closeSelectedEssai();
}

// Affiche le dialogue de sélection des essais
void MainWindow::showEssaiSelectionDialog()
{
    qDebug() << "Showing essai selection dialog";

    if (!m_apiClient) {
        qDebug() << "ERROR: API client not available";
        QMessageBox::warning(this, "Erreur", "Client API non disponible.");
        return;
    }

    if (!m_apiClient->isAuthenticated()) {
        qDebug() << "ERROR: User not authenticated";
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour accéder aux essais.");
        return;
    }

    if (!m_hasSelectedAffaire) {
        qDebug() << "ERROR: No affaire selected";
        QMessageBox::warning(this, "Erreur", "Vous devez d'abord sélectionner une affaire.");
        return;
    }

    qDebug() << "API client available, user authenticated, and affaire selected";
    qDebug() << "API base URL:" << m_apiClient->baseUrl();
    qDebug() << "Current affaire object:" << QJsonDocument(m_currentAffaire).toJson(QJsonDocument::Compact);
    qDebug() << "Selected affaire ID:" << m_currentAffaire["id"].toString();

    // Connecter les signaux pour recevoir la réponse des essais
    connect(m_apiClient, &ApiClient::essaisReceived, this, &MainWindow::onEssaisArrayReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred, Qt::UniqueConnection);

    qDebug() << "Signals connected, making API request...";

    // Faire la requête pour récupérer les essais avec filtrage par affaire
    QUrlQuery params;

    // Essayer de récupérer l'ID de l'affaire avec plusieurs variantes possibles
    QString affaireId;
    if (m_currentAffaire.contains("id")) {
        QJsonValue idValue = m_currentAffaire["id"];
        if (idValue.isString() && !idValue.toString().isEmpty()) {
            affaireId = idValue.toString();
        } else if (idValue.isDouble()) {
            affaireId = QString::number(idValue.toInt());
        } else {
            qDebug() << "ERROR: ID field exists but has unexpected type:" << idValue.type();
        }
    }

    if (affaireId.isEmpty()) {
        qDebug() << "ERROR: No valid affaire ID found in current affaire object";
        qDebug() << "Available keys:" << m_currentAffaire.keys();
        QMessageBox::warning(this, "Erreur", "Impossible de récupérer l'ID de l'affaire sélectionnée.");
        return;
    }

    params.addQueryItem("affaire_id", affaireId);
    params.addQueryItem("page", "1");
    params.addQueryItem("limit", "10");

    qDebug() << "Using affaire ID:" << affaireId;

    qDebug() << "Request parameters:" << params.toString();

    // Utiliser la méthode générique get avec les paramètres
    m_apiClient->get("essais", params);

    qDebug() << "API request sent";

    if (statusBar()) {
        statusBar()->showMessage("Chargement des essais...", 3000);
    }
}

// Ferme l'essai sélectionné
void MainWindow::closeSelectedEssai()
{
    qDebug() << "Closing selected essai";

    if (!m_hasSelectedEssai) {
        return;
    }

    QString essaiInfo = QString("Essai %1 - %2")
        .arg(m_currentEssai["id"].toString())
        .arg(m_currentEssai["type"].toString());

    int ret = QMessageBox::question(this, "Fermer l'essai",
        QString("Voulez-vous vraiment fermer l'essai sélectionné ?\n\n%1").arg(essaiInfo),
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // Fermer automatiquement la courbe sélectionnée si il y en a une
        if (m_hasSelectedCourbe) {
            m_currentCourbe = QJsonObject();
            m_hasSelectedCourbe = false;
        }

        // Vider les courbes existantes quand on ferme l'essai
        m_existingCourbes = QJsonArray();

        m_currentEssai = QJsonObject();
        m_hasSelectedEssai = false;
        updateEssaiMenuState();
        updateWindowTitle();

        if (statusBar()) {
            statusBar()->showMessage("Essai fermé", 3000);
        }

        qDebug() << "Essai closed successfully";
    }
}

// Met à jour l'état du menu Essais
void MainWindow::updateEssaiMenuState()
{
    qDebug() << "Updating essai menu state - m_hasSelectedEssai:" << m_hasSelectedEssai;

    // Trouver le menu Essais et mettre à jour l'état des actions
    if (!m_customMenuBar) {
        qDebug() << "ERROR: Custom MenuBar is null";
        return;
    }

    for (QAction* action : m_customMenuBar->actions()) {
        QMenu* menu = action->menu();
        if (menu && menu->title() == "Essais") {
            QList<QAction*> actions = menu->actions();

            if (actions.size() >= 2) {
                // La deuxième action est "Fermer essai sélectionné"
                actions[1]->setEnabled(m_hasSelectedEssai);

                // Mettre à jour le texte de la première action si un essai est sélectionné
                if (m_hasSelectedEssai) {
                    QString essaiText = QString("Essai: %1 - %2")
                        .arg(m_currentEssai["id"].toString())
                        .arg(m_currentEssai["type"].toString());
                    actions[0]->setText(essaiText);
                } else {
                    actions[0]->setText("Sélectionner essai...");
                }

                qDebug() << "Close action enabled:" << actions[1]->isEnabled();
            } else {
                qDebug() << "ERROR: Not enough actions in Essais menu";
            }

            // Le menu Essais est activé seulement si une affaire est sélectionnée
            menu->setEnabled(m_hasSelectedAffaire);
            break;
        }
    }

    // Mettre à jour l'état du menu Courbes quand l'état des essais change
    updateCourbeMenuState();
}

// Gestionnaire de réception des essais
void MainWindow::onEssaisArrayReceived(const ArrayResponse& response)
{
    qDebug() << "=== ESSAIS ARRAY RESPONSE RECEIVED ===";
    qDebug() << "Response success:" << response.isSuccess();
    qDebug() << "Response has error:" << response.hasError();
    qDebug() << "Array size:" << response.data().size();

    // Déconnecter le signal pour éviter les appels multiples
    disconnect(m_apiClient, &ApiClient::essaisReceived, this, &MainWindow::onEssaisArrayReceived);
    disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
    disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

    if (!response.isSuccess()) {
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des essais: %1").arg(response.message()));
        return;
    }

    QJsonArray essais = response.data();

    if (essais.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucun essai disponible pour cette affaire.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowEssaiDialog(essais);
}

// Gère la réponse des essais manuellement (fallback)
void MainWindow::handleEssaisResponseManually(const JsonResponse& response)
{
    qDebug() << "=== HANDLING ESSAIS RESPONSE MANUALLY ===";

    if (!response.isSuccess()) {
        qDebug() << "Response not successful:" << response.message();
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des essais: %1").arg(response.message()));
        return;
    }

    QJsonObject data = response.data();
    QJsonArray essais;

    qDebug() << "Response data keys:" << data.keys();
    qDebug() << "Full response:" << QJsonDocument(data).toJson(QJsonDocument::Compact);

    // Vérifier si la réponse contient des données paginées
    if (data.contains("data") && data["data"].isArray()) {
        qDebug() << "Found paginated data structure";
        essais = data["data"].toArray();
    } else if (data.isEmpty()) {
        qDebug() << "Response data is empty, this might be a direct array response";
        QMessageBox::warning(this, "Erreur", "Réponse vide du serveur.");
        return;
    } else {
        qDebug() << "Unexpected response format, trying to interpret as direct data";
        // Peut-être que la réponse est directement les données d'essais
        if (data.contains("id") && data.contains("type")) {
            // C'est un seul essai, on le met dans un tableau
            essais.append(data);
        } else {
            QMessageBox::warning(this, "Erreur", "Format de réponse inattendu pour les essais.");
            return;
        }
    }

    qDebug() << "Found" << essais.size() << "essais";

    if (essais.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucun essai disponible pour cette affaire.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowEssaiDialog(essais);
}

// Crée et affiche le dialogue de sélection des essais
void MainWindow::createAndShowEssaiDialog(const QJsonArray& essais)
{
    qDebug() << "=== CREATING AND SHOWING ESSAI DIALOG ===";
    qDebug() << "Number of essais:" << essais.size();

    if (essais.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucun essai disponible pour cette affaire.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    QDialog dialog(this);
    dialog.setWindowTitle("Sélectionner un essai");
    dialog.setModal(true);
    dialog.resize(700, 400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // Ajouter un label d'information
    QString affaireInfo = QString("Affaire: %1 - %2")
        .arg(m_currentAffaire["numero"].toString())
        .arg(m_currentAffaire["client"].toString());
    QLabel* infoLabel = new QLabel("Sélectionnez un essai pour l'affaire " + affaireInfo + ":", &dialog);
    layout->addWidget(infoLabel);

    // Créer la liste des essais
    QListWidget* listWidget = new QListWidget(&dialog);
    layout->addWidget(listWidget);

    qDebug() << "Adding essais to list widget...";

    // Remplir la liste avec les essais
    for (const QJsonValue& value : essais) {
        QJsonObject essai = value.toObject();
        QString itemText = QString("Essai %1 - Type: %2 - Date: %3 - Statut: %4")
            .arg(essai["id"].toString())
            .arg(essai["type"].toString())
            .arg(essai["date_essai"].toString())
            .arg(essai["statut"].toString());

        qDebug() << "Adding essai:" << itemText;

        QListWidgetItem* item = new QListWidgetItem(itemText);
        item->setData(Qt::UserRole, essai);
        listWidget->addItem(item);
    }

    qDebug() << "List widget now has" << listWidget->count() << "items";

    // Ajouter les boutons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* selectButton = new QPushButton("Sélectionner", &dialog);
    QPushButton* cancelButton = new QPushButton("Annuler", &dialog);

    selectButton->setEnabled(false); // Désactivé par défaut

    buttonLayout->addStretch();
    buttonLayout->addWidget(selectButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // Connecter les signaux
    connect(listWidget, &QListWidget::itemSelectionChanged, [selectButton, listWidget]() {
        selectButton->setEnabled(listWidget->currentItem() != nullptr);
    });

    connect(listWidget, &QListWidget::itemDoubleClicked, [&dialog]() {
        dialog.accept();
    });

    connect(selectButton, &QPushButton::clicked, [&dialog]() {
        dialog.accept();
    });

    connect(cancelButton, &QPushButton::clicked, [&dialog]() {
        dialog.reject();
    });

    qDebug() << "Showing dialog...";

    // Afficher le dialogue et traiter le résultat
    if (dialog.exec() == QDialog::Accepted) {
        QListWidgetItem* selectedItem = listWidget->currentItem();
        if (selectedItem) {
            m_currentEssai = selectedItem->data(Qt::UserRole).toJsonObject();
            m_hasSelectedEssai = true;

            // Vider les courbes existantes quand on sélectionne un nouvel essai
            m_existingCourbes = QJsonArray();

            updateEssaiMenuState();
            updateWindowTitle();

            // Charger automatiquement les courbes existantes pour cet essai
            refreshExistingCourbes();

            QString essaiInfo = QString("Essai %1 - %2 sélectionné")
                .arg(m_currentEssai["id"].toString())
                .arg(m_currentEssai["type"].toString());

            if (statusBar()) {
                statusBar()->showMessage(essaiInfo, 5000);
            }

            qDebug() << "Essai selected:" << m_currentEssai["id"].toString();
        }
    } else {
        qDebug() << "Dialog was cancelled";
    }
}

// ========================================
// MÉTHODES DE GESTION DES COURBES
// ========================================

// Gestionnaire de sélection de courbe
void MainWindow::onSelectCourbeTriggered()
{
    qDebug() << "Select courbe triggered";

    if (!m_apiClient || !m_apiClient->isAuthenticated()) {
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour sélectionner une courbe.");
        return;
    }

    if (!m_hasSelectedEssai) {
        QMessageBox::warning(this, "Erreur", "Vous devez d'abord sélectionner un essai.");
        return;
    }

    showCourbeSelectionDialog();
}

// Gestionnaire de création de courbe
void MainWindow::onCreateCourbeTriggered()
{
    qDebug() << "Create courbe triggered";

    if (!m_apiClient || !m_apiClient->isAuthenticated()) {
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour créer une courbe.");
        return;
    }

    if (!m_hasSelectedEssai) {
        QMessageBox::warning(this, "Erreur", "Vous devez d'abord sélectionner un essai.");
        return;
    }

    // Vérifier si tous les types de courbes sont déjà créés
    QStringList availableTypes = getAvailableCurveTypes();
    if (availableTypes.isEmpty()) {
        QMessageBox::information(this, "Information",
            "Tous les types de courbes (CPA, CPB, Résultante) ont déjà été créés pour cet essai.");
        return;
    }

    showCourbeCreationDialog();
}



// Gestionnaire de fermeture de courbe
void MainWindow::onCloseCourbeTriggered()
{
    qDebug() << "Close courbe triggered";

    if (!m_hasSelectedCourbe) {
        QMessageBox::information(this, "Information", "Aucune courbe n'est actuellement sélectionnée.");
        return;
    }

    closeSelectedCourbe();
}

// Affiche le dialogue de sélection des courbes
void MainWindow::showCourbeSelectionDialog()
{
    qDebug() << "Showing courbe selection dialog";

    if (!m_apiClient) {
        qDebug() << "ERROR: API client not available";
        QMessageBox::warning(this, "Erreur", "Client API non disponible.");
        return;
    }

    if (!m_apiClient->isAuthenticated()) {
        qDebug() << "ERROR: User not authenticated";
        QMessageBox::warning(this, "Erreur", "Vous devez être connecté pour accéder aux courbes.");
        return;
    }

    if (!m_hasSelectedEssai) {
        qDebug() << "ERROR: No essai selected";
        QMessageBox::warning(this, "Erreur", "Vous devez d'abord sélectionner un essai.");
        return;
    }

    qDebug() << "API client available, user authenticated, and essai selected";
    qDebug() << "API base URL:" << m_apiClient->baseUrl();
    qDebug() << "Current essai object:" << QJsonDocument(m_currentEssai).toJson(QJsonDocument::Compact);

    // Essayer de récupérer l'ID de l'essai
    QString essaiId;
    if (m_currentEssai.contains("id")) {
        QJsonValue idValue = m_currentEssai["id"];
        if (idValue.isString() && !idValue.toString().isEmpty()) {
            essaiId = idValue.toString();
        } else if (idValue.isDouble()) {
            essaiId = QString::number(idValue.toInt());
        } else {
            qDebug() << "ERROR: ID field exists but has unexpected type:" << idValue.type();
        }
    }

    if (essaiId.isEmpty()) {
        qDebug() << "ERROR: No valid essai ID found in current essai object";
        qDebug() << "Available keys:" << m_currentEssai.keys();
        QMessageBox::warning(this, "Erreur", "Impossible de récupérer l'ID de l'essai sélectionné.");
        return;
    }

    qDebug() << "Using essai ID:" << essaiId;

    // Connecter les signaux pour recevoir la réponse des courbes
    connect(m_apiClient, &ApiClient::courbesReceived, this, &MainWindow::onCourbesArrayReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived, Qt::UniqueConnection);
    connect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred, Qt::UniqueConnection);

    qDebug() << "Signals connected, making API request...";

    // Faire la requête pour récupérer les courbes avec filtrage par essai
    QUrlQuery params;
    params.addQueryItem("essai_id", essaiId);
    params.addQueryItem("page", "1");
    params.addQueryItem("limit", "10");

    qDebug() << "Request parameters:" << params.toString();

    // Utiliser la méthode générique get avec les paramètres
    m_apiClient->get("courbes", params);

    qDebug() << "API request sent";

    if (statusBar()) {
        statusBar()->showMessage("Chargement des courbes...", 3000);
    }
}

// Affiche le dialogue de création de courbe
void MainWindow::showCourbeCreationDialog()
{
    qDebug() << "Showing courbe creation dialog";

    // Créer le dialogue de création
    QDialog dialog(this);
    dialog.setWindowTitle("Créer une nouvelle courbe");
    dialog.setModal(true);
    dialog.resize(500, 300);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // Ajouter un label d'information
    QString essaiInfo = QString("Essai: %1 - %2")
        .arg(m_currentEssai["id"].toString())
        .arg(m_currentEssai["type"].toString());
    QLabel* infoLabel = new QLabel("Créer une courbe pour l'essai " + essaiInfo + ":", &dialog);
    layout->addWidget(infoLabel);

    // Sélection du type de courbe
    QLabel* typeLabel = new QLabel("Type de courbe:", &dialog);
    layout->addWidget(typeLabel);

    QComboBox* typeComboBox = new QComboBox(&dialog);
    QStringList curveTypes = getAvailableCurveTypes();

    // Vérifier s'il y a des types disponibles
    if (curveTypes.isEmpty()) {
        QLabel* noTypesLabel = new QLabel("Aucun type de courbe disponible - tous les types ont été créés.", &dialog);
        noTypesLabel->setStyleSheet("color: #dc3545; font-style: italic;");
        layout->addWidget(noTypesLabel);
        typeComboBox->setEnabled(false);
    } else {
        typeComboBox->addItems(curveTypes);
    }

    layout->addWidget(typeComboBox);

    // Ajouter les boutons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* createButton = new QPushButton("Créer", &dialog);
    QPushButton* cancelButton = new QPushButton("Annuler", &dialog);

    // Désactiver le bouton Créer si aucun type n'est disponible
    if (curveTypes.isEmpty()) {
        createButton->setEnabled(false);
    }

    buttonLayout->addStretch();
    buttonLayout->addWidget(createButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // Connecter les signaux
    connect(createButton, &QPushButton::clicked, [&dialog]() {
        dialog.accept();
    });

    connect(cancelButton, &QPushButton::clicked, [&dialog]() {
        dialog.reject();
    });

    qDebug() << "Showing creation dialog...";

    // Afficher le dialogue et traiter le résultat
    if (dialog.exec() == QDialog::Accepted) {
        QString selectedType = typeComboBox->currentText();

        // Créer la courbe via l'API
        QJsonObject courbeData;
        courbeData["essai_id"] = m_currentEssai["id"];
        courbeData["type_courbe"] = selectedType;
        courbeData["donnees"] = QJsonArray(); // Données vides pour commencer

        // Envoyer la requête de création
        connect(m_apiClient, &ApiClient::responseReceived, this, [this](const QString& endpoint, const JsonResponse& response) {
            if (response.isSuccess()) {
                QMessageBox::information(this, "Succès", "Courbe créée avec succès.");
                if (statusBar()) {
                    statusBar()->showMessage("Courbe créée", 3000);
                }
                // Utiliser un timer pour différer le rafraîchissement
                QTimer::singleShot(100, this, [this]() {
                    refreshExistingCourbes();
                });
            } else {
                QMessageBox::warning(this, "Erreur", "Erreur lors de la création de la courbe: " + response.message());
            }
        }, Qt::SingleShotConnection);

        m_apiClient->post("courbes", courbeData);

        qDebug() << "Courbe creation request sent for type:" << selectedType;
    } else {
        qDebug() << "Creation dialog was cancelled";
    }
}



// Ferme la courbe sélectionnée
void MainWindow::closeSelectedCourbe()
{
    qDebug() << "Closing selected courbe";

    if (!m_hasSelectedCourbe) {
        return;
    }

    QString courbeInfo = QString("Courbe %1 - %2")
        .arg(m_currentCourbe["id"].toString())
        .arg(m_currentCourbe["type_courbe"].toString());

    int ret = QMessageBox::question(this, "Fermer la courbe",
        QString("Voulez-vous vraiment fermer la courbe sélectionnée ?\n\n%1").arg(courbeInfo),
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_currentCourbe = QJsonObject();
        m_hasSelectedCourbe = false;
        updateCourbeMenuState();
        updateWindowTitle();

        if (statusBar()) {
            statusBar()->showMessage("Courbe fermée", 3000);
        }

        qDebug() << "Courbe closed successfully";
    }
}

// Met à jour l'état du menu Courbes
void MainWindow::updateCourbeMenuState()
{
    qDebug() << "Updating courbe menu state - m_hasSelectedCourbe:" << m_hasSelectedCourbe;

    // Trouver le menu Courbes de niveau supérieur et mettre à jour l'état des actions
    if (!m_customMenuBar) {
        qDebug() << "ERROR: Custom MenuBar is null";
        return;
    }

    for (QAction* action : m_customMenuBar->actions()) {
        QMenu* menu = action->menu();
        if (menu && menu->title() == "Courbes") {
            QList<QAction*> actions = menu->actions();

            if (actions.size() >= 3) {
                // actions[0] = Sélectionner courbe
                // actions[1] = Créer courbe
                // actions[2] = Separator
                // actions[3] = Fermer courbe

                // Mettre à jour le texte de la première action si une courbe est sélectionnée
                if (m_hasSelectedCourbe) {
                    QString courbeText = QString("Courbe: %1 - %2")
                        .arg(m_currentCourbe["id"].toString())
                        .arg(m_currentCourbe["type_courbe"].toString());
                    actions[0]->setText(courbeText);
                } else {
                    actions[0]->setText("Sélectionner courbe...");
                }

                // Activer/désactiver "Créer nouvelle courbe" selon les types disponibles
                QStringList availableTypes = getAvailableCurveTypes();
                actions[1]->setEnabled(!availableTypes.isEmpty());

                // Mettre à jour le texte de l'action "Créer courbe" pour indiquer l'état
                if (availableTypes.isEmpty()) {
                    actions[1]->setText("Créer nouvelle courbe... (tous types créés)");
                } else {
                    actions[1]->setText("Créer nouvelle courbe...");
                }

                // Activer/désactiver "Fermer courbe sélectionnée"
                if (actions.size() > 3) {
                    actions[3]->setEnabled(m_hasSelectedCourbe);
                }

                qDebug() << "Close courbe action enabled:" << (actions.size() > 3 ? actions[3]->isEnabled() : false);
            } else {
                qDebug() << "ERROR: Not enough actions in Courbes menu";
            }

            // Le menu Courbes est activé seulement si un essai est sélectionné
            menu->setEnabled(m_hasSelectedEssai);
            break;
        }
    }
}

// Gestionnaire de réception des courbes
void MainWindow::onCourbesArrayReceived(const ArrayResponse& response)
{
    qDebug() << "=== COURBES ARRAY RESPONSE RECEIVED ===";
    qDebug() << "Response success:" << response.isSuccess();
    qDebug() << "Response has error:" << response.hasError();
    qDebug() << "Array size:" << response.data().size();

    // Déconnecter le signal pour éviter les appels multiples
    disconnect(m_apiClient, &ApiClient::courbesReceived, this, &MainWindow::onCourbesArrayReceived);
    disconnect(m_apiClient, &ApiClient::responseReceived, this, &MainWindow::onGenericResponseReceived);
    disconnect(m_apiClient, &ApiClient::errorOccurred, this, &MainWindow::onApiErrorOccurred);

    if (!response.isSuccess()) {
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des courbes: %1").arg(response.message()));
        return;
    }

    QJsonArray courbes = response.data();

    if (courbes.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune courbe disponible pour cet essai.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowCourbeDialog(courbes);
}

// Gère la réponse des courbes manuellement (fallback)
void MainWindow::handleCourbesResponseManually(const JsonResponse& response)
{
    qDebug() << "=== HANDLING COURBES RESPONSE MANUALLY ===";

    if (!response.isSuccess()) {
        qDebug() << "Response not successful:" << response.message();
        QMessageBox::warning(this, "Erreur",
            QString("Erreur lors du chargement des courbes: %1").arg(response.message()));
        return;
    }

    QJsonObject data = response.data();
    QJsonArray courbes;

    qDebug() << "Response data keys:" << data.keys();
    qDebug() << "Full response:" << QJsonDocument(data).toJson(QJsonDocument::Compact);

    // Vérifier si la réponse contient des données paginées
    if (data.contains("data") && data["data"].isArray()) {
        qDebug() << "Found paginated data structure";
        courbes = data["data"].toArray();
    } else if (data.isEmpty()) {
        qDebug() << "Response data is empty, this might be a direct array response";
        QMessageBox::warning(this, "Erreur", "Réponse vide du serveur.");
        return;
    } else {
        qDebug() << "Unexpected response format, trying to interpret as direct data";
        // Peut-être que la réponse est directement les données de courbes
        if (data.contains("id") && data.contains("type_courbe")) {
            // C'est une seule courbe, on la met dans un tableau
            courbes.append(data);
        } else {
            QMessageBox::warning(this, "Erreur", "Format de réponse inattendu pour les courbes.");
            return;
        }
    }

    qDebug() << "Found" << courbes.size() << "courbes";

    // Stocker les courbes existantes pour filtrage des types disponibles
    m_existingCourbes = courbes;

    // Mettre à jour l'état du menu des courbes
    updateCourbeMenuState();

    if (courbes.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune courbe disponible pour cet essai.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    createAndShowCourbeDialog(courbes);
}

// Crée et affiche le dialogue de sélection des courbes
void MainWindow::createAndShowCourbeDialog(const QJsonArray& courbes)
{
    qDebug() << "=== CREATING AND SHOWING COURBE DIALOG ===";
    qDebug() << "Number of courbes:" << courbes.size();

    if (courbes.isEmpty()) {
        QMessageBox::information(this, "Information", "Aucune courbe disponible pour cet essai.");
        return;
    }

    // Créer et afficher le dialogue de sélection
    QDialog dialog(this);
    dialog.setWindowTitle("Sélectionner une courbe");
    dialog.setModal(true);
    dialog.resize(700, 400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // Ajouter un label d'information
    QString essaiInfo = QString("Essai: %1 - %2")
        .arg(m_currentEssai["id"].toString())
        .arg(m_currentEssai["type"].toString());
    QLabel* infoLabel = new QLabel("Sélectionnez une courbe pour l'essai " + essaiInfo + ":", &dialog);
    layout->addWidget(infoLabel);

    // Créer la liste des courbes
    QListWidget* listWidget = new QListWidget(&dialog);
    layout->addWidget(listWidget);

    qDebug() << "Adding courbes to list widget...";

    // Remplir la liste avec les courbes
    for (const QJsonValue& value : courbes) {
        QJsonObject courbe = value.toObject();
        QString itemText = QString("Courbe %1 - Type: %2 - Créée: %3")
            .arg(courbe["id"].toString())
            .arg(courbe["type_courbe"].toString())
            .arg(courbe["TIMESTAMP"].toString());

        qDebug() << "Adding courbe:" << itemText;

        QListWidgetItem* item = new QListWidgetItem(itemText);
        item->setData(Qt::UserRole, courbe);
        listWidget->addItem(item);
    }

    qDebug() << "List widget now has" << listWidget->count() << "items";

    // Ajouter les boutons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* selectButton = new QPushButton("Sélectionner", &dialog);
    QPushButton* cancelButton = new QPushButton("Annuler", &dialog);

    selectButton->setEnabled(false); // Désactivé par défaut

    buttonLayout->addStretch();
    buttonLayout->addWidget(selectButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // Connecter les signaux
    connect(listWidget, &QListWidget::itemSelectionChanged, [selectButton, listWidget]() {
        selectButton->setEnabled(listWidget->currentItem() != nullptr);
    });

    connect(listWidget, &QListWidget::itemDoubleClicked, [&dialog]() {
        dialog.accept();
    });

    connect(selectButton, &QPushButton::clicked, [&dialog]() {
        dialog.accept();
    });

    connect(cancelButton, &QPushButton::clicked, [&dialog]() {
        dialog.reject();
    });

    qDebug() << "Showing dialog...";

    // Afficher le dialogue et traiter le résultat
    if (dialog.exec() == QDialog::Accepted) {
        QListWidgetItem* selectedItem = listWidget->currentItem();
        if (selectedItem) {
            m_currentCourbe = selectedItem->data(Qt::UserRole).toJsonObject();
            m_hasSelectedCourbe = true;

            updateCourbeMenuState();
            updateWindowTitle();

            QString courbeInfo = QString("Courbe %1 - %2 sélectionnée")
                .arg(m_currentCourbe["id"].toString())
                .arg(m_currentCourbe["type_courbe"].toString());

            if (statusBar()) {
                statusBar()->showMessage(courbeInfo, 5000);
            }

            qDebug() << "Courbe selected:" << m_currentCourbe["id"].toString();
        }
    } else {
        qDebug() << "Dialog was cancelled";
    }
}

// Retourne la liste des types de courbes existants pour l'essai courant
QStringList MainWindow::getExistingCurveTypes() const
{
    QStringList existingTypes;

    qDebug() << "=== GETTING EXISTING CURVE TYPES ===";
    qDebug() << "Number of existing courbes:" << m_existingCourbes.size();

    for (const QJsonValue& value : m_existingCourbes) {
        QJsonObject courbe = value.toObject();
        QString type = courbe["type_courbe"].toString();
        qDebug() << "Found courbe with type:" << type;
        if (!type.isEmpty() && !existingTypes.contains(type)) {
            existingTypes.append(type);
        }
    }

    qDebug() << "Existing types found:" << existingTypes;
    return existingTypes;
}

// Retourne la liste des types de courbes disponibles (non existants) pour les essais hydrauliques
QStringList MainWindow::getAvailableCurveTypes()
{
    qDebug() << "=== GETTING AVAILABLE CURVE TYPES ===";

    // Types de courbes hydrauliques standards
    QStringList allTypes;
    allTypes << "CPA"           // Courbe de Pression A
             << "CPB"           // Courbe de Pression B
             << "Résultante";   // Courbe résultante calculée

    qDebug() << "All possible types:" << allTypes;

    // Filtrer les types déjà existants
    QStringList existingTypes = getExistingCurveTypes();
    QStringList availableTypes;

    for (const QString& type : allTypes) {
        if (!existingTypes.contains(type)) {
            availableTypes.append(type);
        }
    }

    qDebug() << "Available types (after filtering):" << availableTypes;
    return availableTypes;
}

// Rafraîchit la liste des courbes existantes pour l'essai courant
void MainWindow::refreshExistingCourbes()
{
    qDebug() << "=== REFRESHING EXISTING COURBES ===";

    if (!m_hasSelectedEssai || !m_apiClient) {
        qDebug() << "Cannot refresh: hasSelectedEssai=" << m_hasSelectedEssai << ", apiClient=" << (m_apiClient != nullptr);
        return;
    }

    QString essaiId;
    if (m_currentEssai.contains("id")) {
        QJsonValue idValue = m_currentEssai["id"];
        if (idValue.isString() && !idValue.toString().isEmpty()) {
            essaiId = idValue.toString();
        } else if (idValue.isDouble()) {
            essaiId = QString::number(idValue.toInt());
        }
    }

    if (essaiId.isEmpty()) {
        qDebug() << "Cannot refresh: essaiId is empty";
        return;
    }

    qDebug() << "Refreshing courbes for essai ID:" << essaiId;

    // Connecter le signal pour recevoir les courbes (connexion à usage unique)
    connect(m_apiClient, &ApiClient::courbesReceived, this, [this](const ArrayResponse& response) {
        qDebug() << "=== REFRESH COURBES RESPONSE RECEIVED ===";
        qDebug() << "Response success:" << response.isSuccess();
        qDebug() << "Response data size:" << response.data().size();

        if (response.isSuccess()) {
            // Mettre à jour les courbes existantes
            m_existingCourbes = response.data();
            qDebug() << "Updated existing courbes, new count:" << m_existingCourbes.size();

            // Afficher le contenu des courbes pour debug
            for (const QJsonValue& value : m_existingCourbes) {
                QJsonObject courbe = value.toObject();
                qDebug() << "Courbe:" << courbe["id"].toString() << "Type:" << courbe["type_courbe"].toString();
            }

            // Mettre à jour l'état du menu
            updateCourbeMenuState();
        } else {
            qDebug() << "Refresh failed:" << response.message();
        }
    }, Qt::SingleShotConnection);

    // Faire la requête pour récupérer les courbes
    QUrlQuery params;
    params.addQueryItem("essai_id", essaiId);
    qDebug() << "Sending refresh request with params:" << params.toString();
    m_apiClient->get("courbes", params);
}
