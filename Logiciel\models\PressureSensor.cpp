#include "PressureSensor.h"
#include <QDebug>
#include <QtMath>

// Default pressure values in bar
const double PressureSensor::DEFAULT_SUPPLY_PRESSURE = 200.0;
const double PressureSensor::DEFAULT_MAX_CHAMBER_PRESSURE = 180.0;
const double PressureSensor::DEFAULT_RETURN_PRESSURE = 2.0;

PressureSensor::PressureSensor(PressureType type, QObject *parent)
    : SimulatedSensor(parent)
    , m_pressureType(type)
    , m_testTimer(new QTimer(this))
    , m_cycleTimer(new QTimer(this))
    , m_currentTestMode(TestMode::Normal)
    , m_testTargetPressure(0.0)
    , m_testInitialPressure(0.0)
    , m_testLeakRate(0.0)
    , m_testMinPressure(0.0)
    , m_testMaxPressure(0.0)
    , m_testDuration(0)
    , m_cycleTime(2000)
    , m_currentCycle(0)
    , m_cycleDirection(true)
{
    setupPressureType();
    
    // Setup test timers
    m_testTimer->setSingleShot(true);
    QObject::connect(m_testTimer, &QTimer::timeout, this, &PressureSensor::onTestTimer);

    m_cycleTimer->setSingleShot(false);
    QObject::connect(m_cycleTimer, &QTimer::timeout, this, &PressureSensor::onCycleTimer);
}

QString PressureSensor::deviceName() const
{
    return QString("Hydraulic Pressure Sensor (%1)").arg(m_pressureTypeString);
}

QString PressureSensor::deviceVersion() const
{
    return "Simulated v1.0";
}

HardwareInterface::DeviceType PressureSensor::deviceType() const
{
    return DeviceType::PressureSensor;
}

double PressureSensor::generateRealisticValue()
{
    switch (m_currentTestMode) {
        case TestMode::PressureTest:
            return generatePressureTestValue();
        case TestMode::LeakTest:
            return generateLeakTestValue();
        case TestMode::CycleTest:
            return generateCycleTestValue();
        case TestMode::StaticTest:
            return generateStaticTestValue();
        case TestMode::Normal:
        default:
            return generateNormalPressureValue();
    }
}

QString PressureSensor::getPrimaryParameter() const
{
    return "pressure";
}

QStringList PressureSensor::getAllParameters() const
{
    return QStringList() << "pressure" << "pressure_psi" << "pressure_pascal" << "temperature";
}

void PressureSensor::setPressureType(PressureType type)
{
    m_pressureType = type;
    setupPressureType();
}

void PressureSensor::simulatePressureTest(double targetPressure, int durationMs)
{
    m_currentTestMode = TestMode::PressureTest;
    m_testTargetPressure = targetPressure;
    m_testDuration = durationMs;
    m_testStartTime = QDateTime::currentDateTime();
    
    // Set timer to complete test
    m_testTimer->start(durationMs);
    
    qDebug() << "Starting pressure test:" << targetPressure << "bar for" << durationMs << "ms";
}

void PressureSensor::simulateLeakTest(double initialPressure, double leakRate)
{
    m_currentTestMode = TestMode::LeakTest;
    m_testInitialPressure = initialPressure;
    m_testLeakRate = leakRate; // bar per second
    m_testStartTime = QDateTime::currentDateTime();
    
    qDebug() << "Starting leak test:" << initialPressure << "bar, leak rate:" << leakRate << "bar/s";
}

void PressureSensor::simulateCycleTest(double minPressure, double maxPressure, int cycleTimeMs)
{
    m_currentTestMode = TestMode::CycleTest;
    m_testMinPressure = minPressure;
    m_testMaxPressure = maxPressure;
    m_cycleTime = cycleTimeMs;
    m_currentCycle = 0;
    m_cycleDirection = true;
    
    // Start cycle timer
    m_cycleTimer->start(cycleTimeMs / 2); // Half cycle time for each direction
    
    qDebug() << "Starting cycle test:" << minPressure << "-" << maxPressure << "bar, cycle time:" << cycleTimeMs << "ms";
}

void PressureSensor::simulateStaticTest(double pressure)
{
    m_currentTestMode = TestMode::StaticTest;
    m_testTargetPressure = pressure;
    
    qDebug() << "Starting static test:" << pressure << "bar";
}

double PressureSensor::barToPsi(double bar)
{
    return bar * 14.5038;
}

double PressureSensor::psiToBar(double psi)
{
    return psi / 14.5038;
}

double PressureSensor::barToPascal(double bar)
{
    return bar * 100000.0;
}

double PressureSensor::pascalToBar(double pascal)
{
    return pascal / 100000.0;
}

void PressureSensor::onTestTimer()
{
    if (m_currentTestMode == TestMode::PressureTest) {
        emit pressureTestCompleted();
        m_currentTestMode = TestMode::Normal;
        qDebug() << "Pressure test completed";
    }
}

void PressureSensor::onCycleTimer()
{
    if (m_currentTestMode == TestMode::CycleTest) {
        m_cycleDirection = !m_cycleDirection;
        if (m_cycleDirection) {
            m_currentCycle++;
            emit cycleCompleted(m_currentCycle);
            qDebug() << "Cycle" << m_currentCycle << "completed";
        }
    }
}

void PressureSensor::setupPressureType()
{
    switch (m_pressureType) {
        case PressureType::CPA:
            m_pressureTypeString = "CPA";
            m_simConfig.minValue = 0.0;
            m_simConfig.maxValue = DEFAULT_MAX_CHAMBER_PRESSURE;
            break;
        case PressureType::CPB:
            m_pressureTypeString = "CPB";
            m_simConfig.minValue = 0.0;
            m_simConfig.maxValue = DEFAULT_MAX_CHAMBER_PRESSURE;
            break;
        case PressureType::Supply:
            m_pressureTypeString = "Supply";
            m_simConfig.minValue = 150.0;
            m_simConfig.maxValue = 250.0;
            break;
        case PressureType::Return:
            m_pressureTypeString = "Return";
            m_simConfig.minValue = 0.0;
            m_simConfig.maxValue = 10.0;
            break;
    }
    
    // Update target values
    updateTargetValues();
}

double PressureSensor::generatePressureTestValue()
{
    // Gradually approach target pressure
    double currentPressure = m_currentValues.value("pressure", 0.0);
    double pressureDiff = m_testTargetPressure - currentPressure;
    
    // Approach target with some overshoot and settling
    double approachRate = 0.1; // 10% per update
    double newPressure = currentPressure + pressureDiff * approachRate;
    
    // Add some overshoot near target
    if (qAbs(pressureDiff) < m_testTargetPressure * 0.05) {
        double overshoot = m_testTargetPressure * 0.02 * qSin(QDateTime::currentDateTime().msecsTo(m_testStartTime) / 100.0);
        newPressure += overshoot;
    }
    
    return newPressure;
}

double PressureSensor::generateLeakTestValue()
{
    // Calculate elapsed time
    qint64 elapsedMs = m_testStartTime.msecsTo(QDateTime::currentDateTime());
    double elapsedSeconds = elapsedMs / 1000.0;
    
    // Apply leak rate
    double currentPressure = m_testInitialPressure - (m_testLeakRate * elapsedSeconds);
    
    // Emit leak detection if significant pressure drop
    if (currentPressure < m_testInitialPressure * 0.95) {
        double actualLeakRate = (m_testInitialPressure - currentPressure) / elapsedSeconds;
        emit leakDetected(actualLeakRate);
    }
    
    return qMax(0.0, currentPressure);
}

double PressureSensor::generateCycleTestValue()
{
    // Calculate position in cycle
    qint64 elapsedMs = m_testStartTime.msecsTo(QDateTime::currentDateTime());
    double cyclePosition = (elapsedMs % m_cycleTime) / double(m_cycleTime);
    
    // Generate sinusoidal pressure variation
    double pressureRange = m_testMaxPressure - m_testMinPressure;
    double pressure = m_testMinPressure + pressureRange * (0.5 + 0.5 * qSin(2 * M_PI * cyclePosition));
    
    return pressure;
}

double PressureSensor::generateStaticTestValue()
{
    // Return target pressure with minimal variation
    return m_testTargetPressure;
}

double PressureSensor::generateNormalPressureValue()
{
    switch (m_pressureType) {
        case PressureType::Supply:
            return generateSupplyPressure();
        case PressureType::CPA:
        case PressureType::CPB:
            return generateChamberPressure();
        case PressureType::Return:
            return generateReturnPressure();
    }
    return 0.0;
}

double PressureSensor::generateSupplyPressure()
{
    // Supply pressure should be relatively stable around 200 bar
    double basePressure = DEFAULT_SUPPLY_PRESSURE;
    double variation = 5.0; // ±5 bar variation
    
    return basePressure + variation * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
}

double PressureSensor::generateChamberPressure()
{
    // Chamber pressure varies based on hydraulic operation
    // Simulate typical hydraulic cylinder operation
    double targetPressure = m_targetValues.value("pressure", 50.0);
    
    // Add some realistic variation based on load
    double loadVariation = 20.0 * qSin(QDateTime::currentDateTime().toMSecsSinceEpoch() / 1000.0);
    
    return targetPressure + loadVariation;
}

double PressureSensor::generateReturnPressure()
{
    // Return pressure should be low and stable
    double basePressure = DEFAULT_RETURN_PRESSURE;
    double variation = 0.5; // ±0.5 bar variation
    
    return basePressure + variation * (m_randomGenerator->generateDouble() - 0.5) * 2.0;
}
