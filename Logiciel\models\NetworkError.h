#ifndef NETWORKERROR_H
#define NETWORKERROR_H

#include <QString>
#include <QNetworkReply>

/**
 * @brief Network error handling and classification
 * 
 * This class provides structured error handling for network operations,
 * including HTTP status codes, network errors, and custom error types.
 */
class NetworkError
{
public:
    enum class Type {
        None,
        NetworkError,
        HttpError,
        JsonParseError,
        AuthenticationError,
        AuthorizationError,
        ValidationError,
        ServerError,
        TimeoutError,
        UnknownError
    };
    
    enum class Severity {
        Info,
        Warning,
        Error,
        Critical
    };
    
    NetworkError();
    NetworkError(Type type, const QString& message, int httpCode = 0);
    NetworkError(QNetworkReply::NetworkError networkError, const QString& message);
    
    // Getters
    Type type() const { return m_type; }
    QString message() const { return m_message; }
    int httpCode() const { return m_httpCode; }
    QNetworkReply::NetworkError networkError() const { return m_networkError; }
    Severity severity() const { return m_severity; }
    bool isValid() const { return m_type != Type::None; }
    
    // Setters
    void setType(Type type) { m_type = type; }
    void setMessage(const QString& message) { m_message = message; }
    void setHttpCode(int code) { m_httpCode = code; }
    void setSeverity(Severity severity) { m_severity = severity; }
    
    // Utility methods
    QString toString() const;
    QString typeString() const;
    QString severityString() const;
    bool isRetryable() const;
    bool requiresAuthentication() const;
    
    // Static factory methods
    static NetworkError fromHttpCode(int httpCode, const QString& message = QString());
    static NetworkError fromNetworkReply(QNetworkReply* reply);
    static NetworkError authenticationRequired(const QString& message = "Authentication required");
    static NetworkError serverError(const QString& message = "Server error");
    static NetworkError timeout(const QString& message = "Request timeout");
    static NetworkError jsonParseError(const QString& message = "JSON parse error");
    
private:
    Type m_type;
    QString m_message;
    int m_httpCode;
    QNetworkReply::NetworkError m_networkError;
    Severity m_severity;
    
    void determineSeverity();
};

#endif // NETWORKERROR_H
