#include "User.h"
#include <QRegularExpression>
#include <QDebug>

Q_LOGGING_CATEGORY(userModelLog, "hydraulic.models.user")

User::User(QObject *parent)
    : QObject(parent)
    , m_id(0)
    , m_role("guest")
    , m_createdAt(QDateTime::currentDateTime())
{
    connectSignals();
}

User::User(const User& other)
    : QObject(other.parent())
    , m_id(other.m_id)
    , m_username(other.m_username)
    , m_email(other.m_email)
    , m_role(other.m_role)
    , m_createdAt(other.m_createdAt)
{
    connectSignals();
}

User& User::operator=(const User& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_username = other.m_username;
        m_email = other.m_email;
        m_role = other.m_role;
        m_createdAt = other.m_createdAt;
        
        emit userDataChanged();
        validateAndEmitSignals();
    }
    return *this;
}

void User::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged(id);
        emit userDataChanged();
    }
}

void User::setUsername(const QString& username)
{
    if (m_username != username) {
        m_username = username;
        emit usernameChanged(username);
        emit userDataChanged();
        validateAndEmitSignals();
    }
}

void User::setEmail(const QString& email)
{
    if (m_email != email) {
        m_email = email;
        emit emailChanged(email);
        emit userDataChanged();
        validateAndEmitSignals();
    }
}

void User::setRole(const QString& role)
{
    if (m_role != role) {
        m_role = role.toLower();
        emit roleChanged(m_role);
        emit userDataChanged();
        validateAndEmitSignals();
    }
}

void User::setCreatedAt(const QDateTime& createdAt)
{
    if (m_createdAt != createdAt) {
        m_createdAt = createdAt;
        emit createdAtChanged(createdAt);
        emit userDataChanged();
    }
}

User::Role User::userRole() const
{
    return roleFromString(m_role);
}

void User::setUserRole(Role role)
{
    setRole(roleToString(role));
}

QString User::roleDisplayName() const
{
    switch (userRole()) {
        case Role::Guest: return "Guest";
        case Role::Operator: return "Operator";
        case Role::Technician: return "Technician";
        case Role::Engineer: return "Engineer";
        case Role::Administrator: return "Administrator";
    }
    return "Unknown";
}

QString User::roleToString(Role role)
{
    switch (role) {
        case Role::Guest: return "guest";
        case Role::Operator: return "operator";
        case Role::Technician: return "technician";
        case Role::Engineer: return "engineer";
        case Role::Administrator: return "administrator";
    }
    return "guest";
}

User::Role User::roleFromString(const QString& roleStr)
{
    QString role = roleStr.toLower();
    if (role == "operator") return Role::Operator;
    if (role == "technician") return Role::Technician;
    if (role == "engineer") return Role::Engineer;
    if (role == "administrator") return Role::Administrator;
    return Role::Guest;
}

bool User::canRunTests() const
{
    return userRole() >= Role::Operator;
}

bool User::canConfigureTests() const
{
    return userRole() >= Role::Technician;
}

bool User::canManageUsers() const
{
    return userRole() >= Role::Administrator;
}

bool User::canAccessAdminFeatures() const
{
    return userRole() >= Role::Administrator;
}

bool User::hasPermission(const QString& permission) const
{
    if (permission == "run_tests") return canRunTests();
    if (permission == "configure_tests") return canConfigureTests();
    if (permission == "manage_users") return canManageUsers();
    if (permission == "admin_features") return canAccessAdminFeatures();
    if (permission == "view_results") return true; // All users can view results
    return false;
}

bool User::isValid() const
{
    return validate().isEmpty();
}

QStringList User::validate() const
{
    QStringList errors;
    
    if (m_id <= 0) {
        errors << "Invalid user ID";
    }
    
    if (!isUsernameValid()) {
        errors << QString("Username must be %1-%2 characters and contain only letters, numbers, and underscores")
                     .arg(MIN_USERNAME_LENGTH).arg(MAX_USERNAME_LENGTH);
    }
    
    if (!isEmailValid()) {
        errors << "Invalid email address format";
    }
    
    if (!isRoleValid()) {
        errors << "Invalid user role";
    }
    
    if (!m_createdAt.isValid()) {
        errors << "Invalid creation date";
    }
    
    return errors;
}

bool User::isUsernameValid() const
{
    return isValidUsername(m_username);
}

bool User::isEmailValid() const
{
    return isValidEmail(m_email);
}

bool User::isRoleValid() const
{
    QStringList validRoles = getAllRoles();
    return validRoles.contains(m_role);
}

QJsonObject User::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["username"] = m_username;
    json["email"] = m_email;
    json["role"] = m_role;
    json["created_at"] = m_createdAt.toString(Qt::ISODate);
    json["display_name"] = displayName();
    json["role_display"] = roleDisplayName();
    json["is_active"] = isActive();
    json["days_since_creation"] = daysSinceCreation();
    return json;
}

void User::fromJson(const QJsonObject& json)
{
    setId(json["id"].toInt());
    setUsername(json["username"].toString());
    setEmail(json["email"].toString());
    setRole(json["role"].toString());
    
    QString createdAtStr = json["created_at"].toString();
    if (!createdAtStr.isEmpty()) {
        setCreatedAt(QDateTime::fromString(createdAtStr, Qt::ISODate));
    }
}

QJsonObject User::toJsonPartial() const
{
    QJsonObject json;
    if (m_id > 0) json["id"] = m_id;
    if (!m_username.isEmpty()) json["username"] = m_username;
    if (!m_email.isEmpty()) json["email"] = m_email;
    if (!m_role.isEmpty()) json["role"] = m_role;
    return json;
}

QString User::displayName() const
{
    if (!m_username.isEmpty()) {
        return m_username;
    }
    if (!m_email.isEmpty()) {
        return m_email.split('@').first();
    }
    return QString("User %1").arg(m_id);
}

QString User::initials() const
{
    QString name = displayName();
    QStringList parts = name.split(' ', Qt::SkipEmptyParts);
    QString initials;
    
    for (const QString& part : parts) {
        if (!part.isEmpty()) {
            initials += part.at(0).toUpper();
        }
    }
    
    return initials.isEmpty() ? "U" : initials.left(2);
}

bool User::isActive() const
{
    return m_id > 0 && !m_username.isEmpty() && isValid();
}

int User::daysSinceCreation() const
{
    if (!m_createdAt.isValid()) {
        return 0;
    }
    return m_createdAt.daysTo(QDateTime::currentDateTime());
}

bool User::operator==(const User& other) const
{
    return m_id == other.m_id &&
           m_username == other.m_username &&
           m_email == other.m_email &&
           m_role == other.m_role;
}

User User::createFromJson(const QJsonObject& json)
{
    User user;
    user.fromJson(json);
    return user;
}

User User::createGuest()
{
    User user;
    user.setRole("guest");
    user.setUsername("guest");
    user.setEmail("guest@localhost");
    return user;
}

User User::createOperator(const QString& username, const QString& email)
{
    User user;
    user.setRole("operator");
    user.setUsername(username);
    user.setEmail(email);
    return user;
}

QStringList User::getAllRoles()
{
    return QStringList() << "guest" << "operator" << "technician" << "engineer" << "administrator";
}

void User::connectSignals()
{
    // Connect property change signals to validation
    connect(this, &User::usernameChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &User::emailChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &User::roleChanged, this, [this]() { validateAndEmitSignals(); });
}

void User::validateAndEmitSignals()
{
    bool valid = isValid();
    emit validationChanged(valid);
    
    if (!valid) {
        QStringList errors = validate();
        qCWarning(userModelLog) << "User validation failed:" << errors;
    }
}

bool User::isValidEmail(const QString& email) const
{
    if (email.isEmpty() || email.length() > MAX_EMAIL_LENGTH) {
        return false;
    }
    
    QRegularExpression emailRegex(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return emailRegex.match(email).hasMatch();
}

bool User::isValidUsername(const QString& username) const
{
    if (username.length() < MIN_USERNAME_LENGTH || username.length() > MAX_USERNAME_LENGTH) {
        return false;
    }
    
    QRegularExpression usernameRegex(R"(^[a-zA-Z0-9_]+$)");
    return usernameRegex.match(username).hasMatch();
}
