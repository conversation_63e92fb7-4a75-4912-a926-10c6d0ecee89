#ifndef TEMPERATURESENSOR_H
#define TEMPERATURESENSOR_H

#include "SimulatedSensor.h"

/**
 * @brief Simulated temperature sensor for hydraulic fluid monitoring
 * 
 * This class simulates temperature measurements of hydraulic fluid,
 * which is critical for system performance and safety monitoring.
 */
class TemperatureSensor : public SimulatedSensor
{
    Q_OBJECT
    
public:
    enum class TemperatureLocation {
        FluidTank,
        SystemReturn,
        CylinderChamberA,
        CylinderChamberB,
        Ambient
    };
    
    explicit TemperatureSensor(TemperatureLocation location, QObject *parent = nullptr);
    
    // HardwareInterface implementation
    QString deviceName() const override;
    QString deviceVersion() const override;
    DeviceType deviceType() const override;
    
    // SimulatedSensor implementation
    double generateRealisticValue() override;
    QString getPrimaryParameter() const override;
    QStringList getAllParameters() const override;
    
    // Temperature-specific methods
    TemperatureLocation temperatureLocation() const { return m_location; }
    void setTemperatureLocation(TemperatureLocation location);
    
    // Temperature monitoring
    void simulateHeatup(double targetTemp, int heatupTimeMs = 10000);
    void simulateCooldown(double targetTemp, int cooldownTimeMs = 15000);
    void simulateTemperatureAlarm(double alarmThreshold);
    void simulateThermalCycling(double minTemp, double maxTemp, int cycleTimeMs = 30000);
    
    // Temperature unit conversion
    static double celsiusToFahrenheit(double celsius);
    static double fahrenheitToCelsius(double fahrenheit);
    static double celsiusToKelvin(double celsius);
    static double kelvinToCelsius(double kelvin);
    
    // Temperature analysis
    double calculateViscosityIndex(double referenceTemp = 40.0) const;
    bool isOverheating() const;
    bool isWithinOperatingRange() const;
    
signals:
    void temperatureAlarmTriggered(double temperature);
    void overheatingDetected();
    void thermalCycleCompleted(int cycleNumber);
    void heatupCompleted();
    void cooldownCompleted();
    
private slots:
    void onThermalTimer();
    void onCycleTimer();
    
private:
    TemperatureLocation m_location;
    QString m_locationString;
    
    // Thermal simulation
    QTimer* m_thermalTimer;
    QTimer* m_cycleTimer;
    
    enum class ThermalMode {
        Normal,
        Heatup,
        Cooldown,
        ThermalCycling,
        AlarmMonitoring
    };
    
    ThermalMode m_currentThermalMode;
    double m_thermalTargetTemp;
    double m_thermalStartTemp;
    double m_thermalMinTemp;
    double m_thermalMaxTemp;
    double m_alarmThreshold;
    int m_thermalDuration;
    int m_cycleTime;
    int m_currentThermalCycle;
    bool m_cycleHeating; // true = heating, false = cooling
    QDateTime m_thermalStartTime;
    
    void setupTemperatureLocation();
    void updateThermalMode();
    double generateHeatupValue();
    double generateCooldownValue();
    double generateThermalCyclingValue();
    double generateNormalTemperatureValue();
    
    // Realistic temperature patterns
    double generateFluidTankTemperature();
    double generateSystemReturnTemperature();
    double generateCylinderTemperature();
    double generateAmbientTemperature();
    
    // Thermal dynamics
    double applyThermalInertia(double currentTemp, double targetTemp, double timeConstant);
    double addThermalNoise(double baseTemp);
    
    static const double DEFAULT_AMBIENT_TEMP; // °C
    static const double DEFAULT_OPERATING_TEMP; // °C
    static const double MAX_SAFE_TEMP; // °C
    static const double MIN_OPERATING_TEMP; // °C
    static const double THERMAL_TIME_CONSTANT; // seconds
};

#endif // TEMPERATURESENSOR_H
