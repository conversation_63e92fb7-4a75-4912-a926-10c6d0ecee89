#include "mainwindow.h"

#include <QApplication>
#include <QDebug>
#include <iostream>

int main(int argc, char *argv[]) {
    try {
        qDebug() << "=== APPLICATION STARTUP ===";
        std::cout << "=== APPLICATION STARTUP ===" << std::endl;

        qDebug() << "Creating QApplication...";
        std::cout << "Creating QApplication..." << std::endl;
        QApplication a(argc, argv);
        qDebug() << "QApplication created successfully";
        std::cout << "QApplication created successfully" << std::endl;

        qDebug() << "Creating MainWindow...";
        std::cout << "Creating MainWindow..." << std::endl;
        MainWindow w;
        qDebug() << "MainWindow created successfully";
        std::cout << "MainWindow created successfully" << std::endl;

        qDebug() << "Showing MainWindow...";
        std::cout << "Showing MainWindow..." << std::endl;
        w.show();
        qDebug() << "MainWindow shown successfully";
        std::cout << "MainWindow shown successfully" << std::endl;

        qDebug() << "Starting event loop...";
        std::cout << "Starting event loop..." << std::endl;
        int result = a.exec();
        qDebug() << "Event loop finished with code:" << result;
        std::cout << "Event loop finished with code: " << result << std::endl;

        return result;
    } catch (const std::exception& e) {
        qDebug() << "EXCEPTION CAUGHT:" << e.what();
        std::cout << "EXCEPTION CAUGHT: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        qDebug() << "UNKNOWN EXCEPTION CAUGHT";
        std::cout << "UNKNOWN EXCEPTION CAUGHT" << std::endl;
        return -1;
    }
}
