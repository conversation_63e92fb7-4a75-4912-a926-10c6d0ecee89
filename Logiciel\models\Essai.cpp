#include "Essai.h"
#include <QDebug>
#include <QJsonDocument>

Q_LOGGING_CATEGORY(essaiModelLog, "hydraulic.models.essai")

// Static constants for required parameters
const QStringList Essai::REQUIRED_PRESSURE_PARAMS = {"target_pressure", "test_duration", "tolerance"};
const QStringList <PERSON><PERSON><PERSON>::REQUIRED_FLOW_PARAMS = {"target_flow", "test_distance", "velocity"};
const QStringList Essai::REQUIRED_LEAKAGE_PARAMS = {"initial_pressure", "test_duration", "max_leakage_rate"};

Essai::<PERSON><PERSON><PERSON>(QObject *parent)
    : QObject(parent)
    , m_id(0)
    , m_affaireId(0)
    , m_typeEssai("pressure_test")
    , m_statut("pending")
    , m_dateExecution(QDateTime::currentDateTime())
{
    connectSignals();
}

Essai::<PERSON><PERSON><PERSON>(const <PERSON><PERSON><PERSON>& other)
    : QObject(other.parent())
    , m_id(other.m_id)
    , m_affaireId(other.m_affaireId)
    , m_typeEssai(other.m_typeEssai)
    , m_parametres(other.m_parametres)
    , m_resultats(other.m_resultats)
    , m_statut(other.m_statut)
    , m_dateExecution(other.m_dateExecution)
{
    connectSignals();
}

Essai& Essai::operator=(const Essai& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_affaireId = other.m_affaireId;
        m_typeEssai = other.m_typeEssai;
        m_parametres = other.m_parametres;
        m_resultats = other.m_resultats;
        m_statut = other.m_statut;
        m_dateExecution = other.m_dateExecution;
        
        emit essaiDataChanged();
        validateAndEmitSignals();
    }
    return *this;
}

void Essai::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged(id);
        emit essaiDataChanged();
    }
}

void Essai::setAffaireId(int affaireId)
{
    if (m_affaireId != affaireId) {
        m_affaireId = affaireId;
        emit affaireIdChanged(affaireId);
        emit essaiDataChanged();
        validateAndEmitSignals();
    }
}

void Essai::setTypeEssai(const QString& typeEssai)
{
    if (m_typeEssai != typeEssai) {
        m_typeEssai = typeEssai.toLower();
        emit typeEssaiChanged(m_typeEssai);
        emit essaiDataChanged();
        validateAndEmitSignals();
    }
}

void Essai::setParametres(const QJsonObject& parametres)
{
    if (m_parametres != parametres) {
        m_parametres = parametres;
        emit parametresChanged(parametres);
        emit essaiDataChanged();
        validateAndEmitSignals();
    }
}

void Essai::setResultats(const QJsonObject& resultats)
{
    if (m_resultats != resultats) {
        m_resultats = resultats;
        emit resultatsChanged(resultats);
        emit essaiDataChanged();
    }
}

void Essai::setStatut(const QString& statut)
{
    if (m_statut != statut) {
        Status oldStatus = essaiStatus();
        m_statut = statut.toLower();
        Status newStatus = essaiStatus();
        
        emit statutChanged(m_statut);
        emit essaiDataChanged();
        emit statusChanged(oldStatus, newStatus);
        validateAndEmitSignals();
    }
}

void Essai::setDateExecution(const QDateTime& dateExecution)
{
    if (m_dateExecution != dateExecution) {
        m_dateExecution = dateExecution;
        emit dateExecutionChanged(dateExecution);
        emit essaiDataChanged();
    }
}

Essai::TestType Essai::testType() const
{
    return testTypeFromString(m_typeEssai);
}

void Essai::setTestType(TestType type)
{
    setTypeEssai(testTypeToString(type));
}

QString Essai::testTypeDisplayName() const
{
    switch (testType()) {
        case TestType::PressureTest: return "Pressure Test";
        case TestType::FlowTest: return "Flow Test";
        case TestType::LeakageTest: return "Leakage Test";
        case TestType::CycleTest: return "Cycle Test";
        case TestType::PerformanceTest: return "Performance Test";
        case TestType::CalibrationTest: return "Calibration Test";
        case TestType::SafetyTest: return "Safety Test";
    }
    return "Unknown Test";
}

QString Essai::testTypeToString(TestType type)
{
    switch (type) {
        case TestType::PressureTest: return "pressure_test";
        case TestType::FlowTest: return "flow_test";
        case TestType::LeakageTest: return "leakage_test";
        case TestType::CycleTest: return "cycle_test";
        case TestType::PerformanceTest: return "performance_test";
        case TestType::CalibrationTest: return "calibration_test";
        case TestType::SafetyTest: return "safety_test";
    }
    return "pressure_test";
}

Essai::TestType Essai::testTypeFromString(const QString& typeStr)
{
    QString type = typeStr.toLower();
    if (type == "flow_test") return TestType::FlowTest;
    if (type == "leakage_test") return TestType::LeakageTest;
    if (type == "cycle_test") return TestType::CycleTest;
    if (type == "performance_test") return TestType::PerformanceTest;
    if (type == "calibration_test") return TestType::CalibrationTest;
    if (type == "safety_test") return TestType::SafetyTest;
    return TestType::PressureTest;
}

Essai::Status Essai::essaiStatus() const
{
    return statusFromString(m_statut);
}

void Essai::setEssaiStatus(Status status)
{
    setStatut(statusToString(status));
}

QString Essai::statusDisplayName() const
{
    switch (essaiStatus()) {
        case Status::Pending: return "Pending";
        case Status::Running: return "Running";
        case Status::Paused: return "Paused";
        case Status::Completed: return "Completed";
        case Status::Failed: return "Failed";
        case Status::Cancelled: return "Cancelled";
        case Status::Archived: return "Archived";
    }
    return "Unknown";
}

QString Essai::statusToString(Status status)
{
    switch (status) {
        case Status::Pending: return "pending";
        case Status::Running: return "running";
        case Status::Paused: return "paused";
        case Status::Completed: return "completed";
        case Status::Failed: return "failed";
        case Status::Cancelled: return "cancelled";
        case Status::Archived: return "archived";
    }
    return "pending";
}

Essai::Status Essai::statusFromString(const QString& statusStr)
{
    QString status = statusStr.toLower();
    if (status == "running") return Status::Running;
    if (status == "paused") return Status::Paused;
    if (status == "completed") return Status::Completed;
    if (status == "failed") return Status::Failed;
    if (status == "cancelled") return Status::Cancelled;
    if (status == "archived") return Status::Archived;
    return Status::Pending;
}

void Essai::setParameter(const QString& key, const QVariant& value)
{
    QJsonObject params = m_parametres;
    params[key] = QJsonValue::fromVariant(value);
    setParametres(params);
    emit parameterChanged(key, value);
}

QVariant Essai::parameter(const QString& key, const QVariant& defaultValue) const
{
    if (m_parametres.contains(key)) {
        return m_parametres.value(key).toVariant();
    }
    return defaultValue;
}

QStringList Essai::parameterKeys() const
{
    return m_parametres.keys();
}

bool Essai::hasParameter(const QString& key) const
{
    return m_parametres.contains(key);
}

void Essai::removeParameter(const QString& key)
{
    QJsonObject params = m_parametres;
    params.remove(key);
    setParametres(params);
}

void Essai::setResult(const QString& key, const QVariant& value)
{
    QJsonObject results = m_resultats;
    results[key] = QJsonValue::fromVariant(value);
    setResultats(results);
    emit resultChanged(key, value);
}

QVariant Essai::result(const QString& key, const QVariant& defaultValue) const
{
    if (m_resultats.contains(key)) {
        return m_resultats.value(key).toVariant();
    }
    return defaultValue;
}

QStringList Essai::resultKeys() const
{
    return m_resultats.keys();
}

bool Essai::hasResult(const QString& key) const
{
    return m_resultats.contains(key);
}

void Essai::removeResult(const QString& key)
{
    QJsonObject results = m_resultats;
    results.remove(key);
    setResultats(results);
}

void Essai::clearResults()
{
    setResultats(QJsonObject());
}

bool Essai::canBeStarted() const
{
    Status status = essaiStatus();
    return status == Status::Pending && areParametersValid();
}

bool Essai::canBePaused() const
{
    return essaiStatus() == Status::Running;
}

bool Essai::canBeResumed() const
{
    return essaiStatus() == Status::Paused;
}

bool Essai::canBeStopped() const
{
    Status status = essaiStatus();
    return status == Status::Running || status == Status::Paused;
}

bool Essai::canBeModified() const
{
    Status status = essaiStatus();
    return status == Status::Pending || status == Status::Paused;
}

bool Essai::canBeDeleted() const
{
    Status status = essaiStatus();
    return status == Status::Pending || status == Status::Cancelled || status == Status::Failed;
}

bool Essai::isRunning() const
{
    return essaiStatus() == Status::Running;
}

bool Essai::isCompleted() const
{
    Status status = essaiStatus();
    return status == Status::Completed || status == Status::Archived;
}

bool Essai::hasFailed() const
{
    return essaiStatus() == Status::Failed;
}

bool Essai::isValid() const
{
    return validate().isEmpty();
}

QStringList Essai::validate() const
{
    QStringList errors;
    
    if (m_id <= 0) {
        errors << "Invalid essai ID";
    }
    
    if (m_affaireId <= 0) {
        errors << "Invalid affaire ID";
    }
    
    if (!isTypeValid()) {
        errors << "Invalid test type";
    }
    
    if (!isStatusValid()) {
        errors << "Invalid status";
    }
    
    if (!areParametersValid()) {
        errors << "Invalid or missing test parameters";
    }
    
    if (!m_dateExecution.isValid()) {
        errors << "Invalid execution date";
    }
    
    return errors;
}

bool Essai::areParametersValid() const
{
    TestType type = testType();
    QStringList requiredParams;
    
    switch (type) {
        case TestType::PressureTest:
            requiredParams = REQUIRED_PRESSURE_PARAMS;
            break;
        case TestType::FlowTest:
            requiredParams = REQUIRED_FLOW_PARAMS;
            break;
        case TestType::LeakageTest:
            requiredParams = REQUIRED_LEAKAGE_PARAMS;
            break;
        default:
            return true; // Other test types don't have strict requirements yet
    }
    
    for (const QString& param : requiredParams) {
        if (!hasParameter(param)) {
            return false;
        }
    }
    
    return true;
}

bool Essai::isTypeValid() const
{
    return isValidTestType(m_typeEssai);
}

bool Essai::isStatusValid() const
{
    return isValidStatus(m_statut);
}

QJsonObject Essai::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["affaire_id"] = m_affaireId;
    json["type_essai"] = m_typeEssai;
    json["parametres"] = m_parametres;
    json["resultats"] = m_resultats;
    json["statut"] = m_statut;
    json["date_execution"] = m_dateExecution.toString(Qt::ISODate);
    json["display_name"] = displayName();
    json["test_type_display"] = testTypeDisplayName();
    json["status_display"] = statusDisplayName();
    json["can_be_started"] = canBeStarted();
    json["can_be_paused"] = canBePaused();
    json["can_be_resumed"] = canBeResumed();
    json["can_be_stopped"] = canBeStopped();
    json["can_be_modified"] = canBeModified();
    json["can_be_deleted"] = canBeDeleted();
    json["is_running"] = isRunning();
    json["is_completed"] = isCompleted();
    json["has_failed"] = hasFailed();
    json["duration_minutes"] = durationMinutes();
    json["progress_percentage"] = progressPercentage();
    return json;
}

void Essai::fromJson(const QJsonObject& json)
{
    setId(json["id"].toInt());
    setAffaireId(json["affaire_id"].toInt());
    setTypeEssai(json["type_essai"].toString());
    setParametres(json["parametres"].toObject());
    setResultats(json["resultats"].toObject());
    setStatut(json["statut"].toString());
    
    QString dateExecutionStr = json["date_execution"].toString();
    if (!dateExecutionStr.isEmpty()) {
        setDateExecution(QDateTime::fromString(dateExecutionStr, Qt::ISODate));
    }
}

QJsonObject Essai::toJsonPartial() const
{
    QJsonObject json;
    if (m_id > 0) json["id"] = m_id;
    if (m_affaireId > 0) json["affaire_id"] = m_affaireId;
    if (!m_typeEssai.isEmpty()) json["type_essai"] = m_typeEssai;
    if (!m_parametres.isEmpty()) json["parametres"] = m_parametres;
    if (!m_resultats.isEmpty()) json["resultats"] = m_resultats;
    if (!m_statut.isEmpty()) json["statut"] = m_statut;
    return json;
}

QString Essai::displayName() const
{
    return QString("%1 #%2").arg(testTypeDisplayName()).arg(m_id);
}

QString Essai::shortDescription() const
{
    QString desc = testTypeDisplayName();
    if (hasParameter("target_pressure")) {
        desc += QString(" (%1 bar)").arg(parameter("target_pressure").toDouble());
    } else if (hasParameter("target_flow")) {
        desc += QString(" (%1 L/min)").arg(parameter("target_flow").toDouble());
    }
    return desc;
}

int Essai::durationMinutes() const
{
    if (hasParameter("test_duration")) {
        return parameter("test_duration").toInt() / 60000; // Convert ms to minutes
    }
    return 0;
}

QString Essai::formattedExecutionDate() const
{
    if (!m_dateExecution.isValid()) {
        return "Not executed";
    }
    return m_dateExecution.toString("dd/MM/yyyy hh:mm");
}

double Essai::progressPercentage() const
{
    if (hasResult("progress")) {
        return result("progress").toDouble();
    }
    
    Status status = essaiStatus();
    switch (status) {
        case Status::Pending: return 0.0;
        case Status::Running: return 50.0; // Assume 50% if no specific progress
        case Status::Paused: return result("last_progress", 25.0).toDouble();
        case Status::Completed: return 100.0;
        case Status::Failed:
        case Status::Cancelled: return result("last_progress", 0.0).toDouble();
        case Status::Archived: return 100.0;
    }
    return 0.0;
}

bool Essai::operator==(const Essai& other) const
{
    return m_id == other.m_id &&
           m_affaireId == other.m_affaireId &&
           m_typeEssai == other.m_typeEssai &&
           m_statut == other.m_statut;
}

Essai Essai::createFromJson(const QJsonObject& json)
{
    Essai essai;
    essai.fromJson(json);
    return essai;
}

Essai Essai::createNew(int affaireId, TestType type)
{
    Essai essai;
    essai.setAffaireId(affaireId);
    essai.setTestType(type);
    essai.setParametres(getDefaultParameters(type));
    essai.setEssaiStatus(Status::Pending);
    return essai;
}

QStringList Essai::getAllTestTypes()
{
    return QStringList() << "pressure_test" << "flow_test" << "leakage_test" 
                         << "cycle_test" << "performance_test" << "calibration_test" << "safety_test";
}

QStringList Essai::getAllStatuses()
{
    return QStringList() << "pending" << "running" << "paused" << "completed" 
                         << "failed" << "cancelled" << "archived";
}

QJsonObject Essai::getDefaultParameters(TestType type)
{
    QJsonObject params;
    
    switch (type) {
        case TestType::PressureTest:
            params["target_pressure"] = 150.0;
            params["test_duration"] = 5000;
            params["tolerance"] = 2.0;
            break;
        case TestType::FlowTest:
            params["target_flow"] = 50.0;
            params["test_distance"] = 100.0;
            params["velocity"] = 25.0;
            break;
        case TestType::LeakageTest:
            params["initial_pressure"] = 100.0;
            params["test_duration"] = 10000;
            params["max_leakage_rate"] = 1.0;
            break;
        case TestType::CycleTest:
            params["min_position"] = 0.0;
            params["max_position"] = 400.0;
            params["cycle_count"] = 10;
            params["velocity"] = 50.0;
            break;
        default:
            params["test_duration"] = 5000;
            break;
    }
    
    return params;
}

void Essai::connectSignals()
{
    // Connect property change signals to validation
    connect(this, &Essai::affaireIdChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Essai::typeEssaiChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Essai::parametresChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &Essai::statutChanged, this, [this]() { validateAndEmitSignals(); });
}

void Essai::validateAndEmitSignals()
{
    bool valid = isValid();
    emit validationChanged(valid);
    
    if (!valid) {
        QStringList errors = validate();
        qCWarning(essaiModelLog) << "Essai validation failed:" << errors;
    }
}

bool Essai::isValidTestType(const QString& type) const
{
    QStringList validTypes = getAllTestTypes();
    return validTypes.contains(type);
}

bool Essai::isValidStatus(const QString& status) const
{
    QStringList validStatuses = getAllStatuses();
    return validStatuses.contains(status);
}
