#ifndef DATAACQUISITIONTHREAD_H
#define DATAACQUISITIONTHREAD_H

#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QWaitCondition>
#include <QElapsedTimer>
#include <memory>

#include "ThreadSafeQueue.h"
#include "models/TestData.h"
#include "models/HardwareInterface.h"
#include "models/PressureSensor.h"
#include "models/FlowMeter.h"
#include "models/TemperatureSensor.h"
#include "models/ActuatorControl.h"

/**
 * @brief Background thread for continuous data acquisition
 * 
 * This thread continuously collects data from all hardware sensors
 * and actuators, processes it, and makes it available to the main thread
 * through a thread-safe queue.
 */
class DataAcquisitionThread : public QThread
{
    Q_OBJECT
    
public:
    explicit DataAcquisitionThread(QObject *parent = nullptr);
    ~DataAcquisitionThread() override;
    
    // Thread control
    void startAcquisition();
    void stopAcquisition();
    void pauseAcquisition();
    void resumeAcquisition();
    
    bool isAcquisitionRunning() const { return m_isRunning; }
    bool isAcquisitionPaused() const { return m_isPaused; }
    
    // Configuration
    void setAcquisitionRate(int rateHz);
    int acquisitionRate() const { return m_acquisitionRateHz; }
    
    void setBufferSize(int maxSamples);
    int bufferSize() const { return m_maxBufferSize; }
    
    // Hardware management
    void addHardwareDevice(std::shared_ptr<HardwareInterface> device);
    void removeHardwareDevice(std::shared_ptr<HardwareInterface> device);
    void clearHardwareDevices();
    QList<std::shared_ptr<HardwareInterface>> hardwareDevices() const;
    
    // Data access
    bool getLatestData(TestData& data);
    QList<TestData> getBufferedData(int maxSamples = -1);
    void clearDataBuffer();
    
    // Statistics
    int totalSamplesCollected() const { return m_totalSamples; }
    double actualAcquisitionRate() const { return m_actualRate; }
    int droppedSamples() const { return m_droppedSamples; }
    
    // Error handling
    QString lastError() const { return m_lastError; }
    bool hasErrors() const { return !m_lastError.isEmpty(); }
    void clearErrors();
    
signals:
    void dataReady(const TestData& data);
    void acquisitionStarted();
    void acquisitionStopped();
    void acquisitionPaused();
    void acquisitionResumed();
    void errorOccurred(const QString& error);
    void statisticsUpdated(int totalSamples, double actualRate, int droppedSamples);
    
protected:
    void run() override;
    
private slots:
    void onHardwareError(const QString& error);
    void onHardwareDataReceived(const QString& parameter, const QVariant& value);
    
private:
    // Thread control
    mutable QMutex m_mutex;
    QWaitCondition m_pauseCondition;
    volatile bool m_isRunning;
    volatile bool m_isPaused;
    volatile bool m_stopRequested;
    
    // Configuration
    int m_acquisitionRateHz;
    int m_maxBufferSize;
    int m_acquisitionIntervalMs;
    
    // Hardware devices
    QList<std::shared_ptr<HardwareInterface>> m_hardwareDevices;
    QMap<QString, QVariant> m_latestReadings;
    
    // Data management
    ThreadSafeQueue<TestData> m_dataQueue;
    QList<TestData> m_dataBuffer;
    
    // Statistics and monitoring
    int m_totalSamples;
    int m_droppedSamples;
    double m_actualRate;
    QElapsedTimer m_rateTimer;
    QElapsedTimer m_acquisitionTimer;
    QString m_lastError;
    
    // Performance monitoring
    qint64 m_lastSampleTime;
    QList<qint64> m_sampleTimes;
    static const int RATE_CALCULATION_SAMPLES = 100;
    
    // Data collection methods
    TestData collectDataFromHardware();
    void updateStatistics();
    void calculateActualRate();
    void addToBuffer(const TestData& data);
    void validateHardwareDevices();
    
    // Error handling
    void setError(const QString& error);
    void handleHardwareError(std::shared_ptr<HardwareInterface> device, const QString& error);
    
    // Utility methods
    void updateAcquisitionInterval();
    bool waitForNextSample();
    void connectHardwareSignals(std::shared_ptr<HardwareInterface> device);
    void disconnectHardwareSignals(std::shared_ptr<HardwareInterface> device);
};

/**
 * @brief Data acquisition configuration
 */
struct DataAcquisitionConfig
{
    int acquisitionRateHz = 100;           // Samples per second
    int bufferSize = 10000;                // Maximum buffered samples
    bool enableDataValidation = true;      // Validate data before processing
    bool enableStatistics = true;          // Calculate acquisition statistics
    bool enableErrorRecovery = true;       // Attempt to recover from hardware errors
    double maxAllowableDropRate = 0.05;    // Maximum acceptable sample drop rate (5%)
    int errorRecoveryTimeoutMs = 5000;     // Timeout for error recovery attempts
    
    // Data filtering
    bool enableDataFiltering = false;      // Enable real-time data filtering
    double filterCutoffFrequency = 10.0;   // Low-pass filter cutoff frequency (Hz)
    int filterOrder = 2;                   // Filter order
    
    // Trigger conditions
    bool enableTrigger = false;            // Enable data acquisition trigger
    QString triggerParameter;              // Parameter to monitor for trigger
    double triggerThreshold = 0.0;         // Trigger threshold value
    bool triggerRisingEdge = true;         // Trigger on rising edge
};

#endif // DATAACQUISITIONTHREAD_H
