#ifndef TESTCONFIGURATION_H
#define TESTCONFIGURATION_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(testConfigLog)

/**
 * @brief Test configuration model for hydraulic test bench system
 * 
 * This class represents the configuration parameters for hydraulic tests,
 * including pressure ranges, flow limits, temperature thresholds, acquisition
 * settings, and safety parameters.
 */
class TestConfiguration : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    Q_PROPERTY(QString description READ description WRITE setDescription NOTIFY descriptionChanged)
    Q_PROPERTY(double pressureMin READ pressureMin WRITE setPressureMin NOTIFY pressureMinChanged)
    Q_PROPERTY(double pressureMax READ pressureMax WRITE setPressureMax NOTIFY pressureMaxChanged)
    Q_PROPERTY(double flowMin READ flowMin WRITE setFlowMin NOTIFY flowMinChanged)
    Q_PROPERTY(double flowMax READ flowMax WRITE setFlowMax NOTIFY flowMaxChanged)
    Q_PROPERTY(double temperatureMax READ temperatureMax WRITE setTemperatureMax NOTIFY temperatureMaxChanged)
    Q_PROPERTY(int acquisitionRate READ acquisitionRate WRITE setAcquisitionRate NOTIFY acquisitionRateChanged)
    Q_PROPERTY(int testDuration READ testDuration WRITE setTestDuration NOTIFY testDurationChanged)
    
public:
    /**
     * @brief Safety threshold configuration
     */
    struct SafetyThresholds {
        double maxPressure = 250.0;         ///< Maximum allowed pressure (bar)
        double maxTemperature = 80.0;       ///< Maximum allowed temperature (°C)
        double maxFlow = 100.0;             ///< Maximum allowed flow rate (L/min)
        double maxVelocity = 200.0;         ///< Maximum actuator velocity (mm/s)
        double maxForce = 50000.0;          ///< Maximum force (N)
        double minPressure = 0.0;           ///< Minimum pressure for operation (bar)
        double leakageThreshold = 2.0;      ///< Maximum acceptable leakage (L/min)
        int emergencyStopDelay = 1000;      ///< Emergency stop response time (ms)
    };
    
    /**
     * @brief Data acquisition configuration
     */
    struct AcquisitionConfig {
        int sampleRate = 100;               ///< Samples per second
        int bufferSize = 10000;             ///< Maximum buffered samples
        bool enableFiltering = true;        ///< Enable data filtering
        double filterCutoff = 10.0;         ///< Filter cutoff frequency (Hz)
        bool enableValidation = true;       ///< Enable data validation
        bool enableLogging = true;          ///< Enable data logging
        QString logFormat = "csv";          ///< Log file format
    };
    
    /**
     * @brief Test execution configuration
     */
    struct ExecutionConfig {
        bool autoStart = false;             ///< Automatically start test
        bool autoStop = true;               ///< Automatically stop on completion
        bool pauseOnError = true;           ///< Pause test on error
        bool saveResults = true;            ///< Save test results
        bool generateReport = true;         ///< Generate test report
        QString reportFormat = "pdf";       ///< Report format
        int maxRetries = 3;                 ///< Maximum retry attempts
        int retryDelay = 5000;              ///< Delay between retries (ms)
    };
    
    explicit TestConfiguration(QObject *parent = nullptr);
    TestConfiguration(const TestConfiguration& other);
    TestConfiguration& operator=(const TestConfiguration& other);
    virtual ~TestConfiguration() = default;
    
    // Basic properties
    QString name() const { return m_name; }
    void setName(const QString& name);
    
    QString description() const { return m_description; }
    void setDescription(const QString& description);
    
    // Pressure range
    double pressureMin() const { return m_pressureMin; }
    void setPressureMin(double pressureMin);
    
    double pressureMax() const { return m_pressureMax; }
    void setPressureMax(double pressureMax);
    
    void setPressureRange(double min, double max);
    QPair<double, double> pressureRange() const;
    
    // Flow range
    double flowMin() const { return m_flowMin; }
    void setFlowMin(double flowMin);
    
    double flowMax() const { return m_flowMax; }
    void setFlowMax(double flowMax);
    
    void setFlowRange(double min, double max);
    QPair<double, double> flowRange() const;
    
    // Temperature limits
    double temperatureMax() const { return m_temperatureMax; }
    void setTemperatureMax(double temperatureMax);
    
    // Acquisition settings
    int acquisitionRate() const { return m_acquisitionRate; }
    void setAcquisitionRate(int acquisitionRate);
    
    int testDuration() const { return m_testDuration; }
    void setTestDuration(int testDuration);
    
    // Configuration structures
    SafetyThresholds safetyThresholds() const { return m_safetyThresholds; }
    void setSafetyThresholds(const SafetyThresholds& thresholds);
    
    AcquisitionConfig acquisitionConfig() const { return m_acquisitionConfig; }
    void setAcquisitionConfig(const AcquisitionConfig& config);
    
    ExecutionConfig executionConfig() const { return m_executionConfig; }
    void setExecutionConfig(const ExecutionConfig& config);
    
    // Validation
    bool isValid() const;
    QStringList validate() const;
    bool isPressureRangeValid() const;
    bool isFlowRangeValid() const;
    bool isAcquisitionRateValid() const;
    bool isTestDurationValid() const;
    bool areSafetyThresholdsValid() const;
    
    // Safety checks
    bool isPressureSafe(double pressure) const;
    bool isTemperatureSafe(double temperature) const;
    bool isFlowSafe(double flow) const;
    bool isVelocitySafe(double velocity) const;
    bool isForceSafe(double force) const;
    bool isWithinOperatingLimits(double pressure, double flow, double temperature) const;
    
    // JSON serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    QJsonObject toJsonPartial() const;
    
    // File operations
    bool saveToFile(const QString& filename) const;
    bool loadFromFile(const QString& filename);
    
    // Utility methods
    QString displayName() const;
    QString summary() const;
    double estimatedTestTime() const; // in minutes
    
    // Operators
    bool operator==(const TestConfiguration& other) const;
    bool operator!=(const TestConfiguration& other) const { return !(*this == other); }
    
    // Static factory methods
    static TestConfiguration createDefault();
    static TestConfiguration createPressureTest();
    static TestConfiguration createFlowTest();
    static TestConfiguration createLeakageTest();
    static TestConfiguration createCycleTest();
    static TestConfiguration createFromJson(const QJsonObject& json);
    
signals:
    void nameChanged(const QString& name);
    void descriptionChanged(const QString& description);
    void pressureMinChanged(double pressureMin);
    void pressureMaxChanged(double pressureMax);
    void flowMinChanged(double flowMin);
    void flowMaxChanged(double flowMax);
    void temperatureMaxChanged(double temperatureMax);
    void acquisitionRateChanged(int acquisitionRate);
    void testDurationChanged(int testDuration);
    void configurationChanged();
    void validationChanged(bool isValid);
    void safetyThresholdsChanged();
    void acquisitionConfigChanged();
    void executionConfigChanged();
    
private:
    QString m_name;
    QString m_description;
    double m_pressureMin;
    double m_pressureMax;
    double m_flowMin;
    double m_flowMax;
    double m_temperatureMax;
    int m_acquisitionRate;
    int m_testDuration;
    
    SafetyThresholds m_safetyThresholds;
    AcquisitionConfig m_acquisitionConfig;
    ExecutionConfig m_executionConfig;
    
    void connectSignals();
    void validateAndEmitSignals();
    QJsonObject safetyThresholdsToJson() const;
    void safetyThresholdsFromJson(const QJsonObject& json);
    QJsonObject acquisitionConfigToJson() const;
    void acquisitionConfigFromJson(const QJsonObject& json);
    QJsonObject executionConfigToJson() const;
    void executionConfigFromJson(const QJsonObject& json);
    
    // Constants
    static const double DEFAULT_PRESSURE_MIN;
    static const double DEFAULT_PRESSURE_MAX;
    static const double DEFAULT_FLOW_MIN;
    static const double DEFAULT_FLOW_MAX;
    static const double DEFAULT_TEMPERATURE_MAX;
    static const int DEFAULT_ACQUISITION_RATE;
    static const int DEFAULT_TEST_DURATION;
    static const int MIN_ACQUISITION_RATE;
    static const int MAX_ACQUISITION_RATE;
    static const int MIN_TEST_DURATION;
    static const int MAX_TEST_DURATION;
};

#endif // TESTCONFIGURATION_H
