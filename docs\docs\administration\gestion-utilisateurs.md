# Gestion des Utilisateurs

<div className="role-controleur">
<strong>🔒 Fonctionnalité Contrôleur Uniquement</strong><br/>
Cette section est réservée aux utilisateurs ayant le rôle "Contrôleur". Les opérateurs n'ont pas accès à ces fonctionnalités.
</div>

## Vue d'ensemble

Le module **Gestion des Utilisateurs** permet aux contrôleurs d'administrer les comptes utilisateurs du système FluidMotion Labs. Il offre des outils complets pour créer, modifier, supprimer et gérer les permissions des utilisateurs.

## Accès au Module

### Navigation
- **Menu utilisateur** (clic sur l'avatar en haut à droite)
- **"Gestion des Utilisateurs"** dans le sous-menu d'administration
- Visible uniquement pour les contrôleurs
- Accès direct via <kbd>Alt</kbd> + <kbd>U</kbd>

### URL directe
```
/users.php
```

## Fonctionnalités Principales

### 📋 Liste des Utilisateurs

L'interface principale affiche un tableau complet de tous les utilisateurs du système avec :

- **ID** : Identifiant unique de l'utilisateur
- **Nom d'utilisateur** : Login de connexion
- **Rôle** : Contrôleur ou Opérateur (avec badge coloré)
- **Date de création** : Horodatage de création du compte
- **Actions** : Boutons pour modifier, changer le mot de passe ou supprimer

### ➕ Création d'Utilisateur

#### Processus de création
1. Cliquer sur le bouton **"Nouvel Utilisateur"**
2. Remplir le formulaire modal :
   - **Nom d'utilisateur** : Identifiant unique (3-50 caractères)
   - **Mot de passe** : Mot de passe sécurisé
   - **Rôle** : Sélectionner "Opérateur" ou "Contrôleur"
3. Valider avec **"Créer l'utilisateur"**

#### Validation
- Le nom d'utilisateur doit être unique
- Le mot de passe est automatiquement haché pour la sécurité
- Tous les champs sont obligatoires

### ✏️ Modification d'Utilisateur

#### Informations modifiables
- **Nom d'utilisateur** : Peut être changé (doit rester unique)
- **Rôle** : Peut être modifié entre Opérateur et Contrôleur

#### Processus de modification
1. Cliquer sur **"Modifier"** dans la ligne de l'utilisateur
2. Le formulaire se pré-remplit avec les données actuelles
3. Modifier les champs souhaités
4. Valider avec **"Mettre à jour"**

#### Protections spéciales
- **Dernier contrôleur** : Si l'utilisateur est le dernier contrôleur du système, l'option "Opérateur" est désactivée
- **Message d'avertissement** : Un message explicatif s'affiche dans ce cas
- **Prévention du verrouillage** : Garantit qu'il y a toujours au moins un contrôleur dans le système

### 🔐 Changement de Mot de Passe

#### Sécurité et Validation
- **Politique de sécurité** : Respect des exigences minimales
- **Validation en temps réel** : Vérification immédiate de la conformité
- **Indicateur de force** : Évaluation visuelle (Très faible à Très fort)
- **Confirmation requise** : Double saisie pour éviter les erreurs
- **Hachage automatique** : Chiffrement sécurisé avant stockage

#### Processus
1. Cliquer sur **"Mot de passe"** dans la ligne de l'utilisateur
2. Saisir le nouveau mot de passe (validation automatique)
3. Confirmer le mot de passe
4. Utiliser le générateur si nécessaire (**"Générer un mot de passe sécurisé"**)
5. Valider avec **"Changer le mot de passe"**

#### Fonctionnalités Avancées
- **Générateur intégré** : Création automatique de mots de passe conformes
- **Visibilité temporaire** : Bouton pour afficher/masquer le mot de passe
- **Copie automatique** : Le mot de passe généré est copié dans le presse-papiers

### 🗑️ Suppression d'Utilisateur

#### Protections
- **Auto-protection** : Impossible de supprimer son propre compte
- **Protection du dernier contrôleur** : Impossible de supprimer le dernier contrôleur du système
- **Confirmation** : Modal de confirmation avec nom d'utilisateur
- **Suppression définitive** : Action irréversible

#### Processus
1. Cliquer sur **"Supprimer"** (non disponible pour son propre compte ou le dernier contrôleur)
2. Confirmer dans la modal de sécurité
3. Valider avec **"Oui, supprimer"**

#### Cas particuliers
- Si l'utilisateur est le **dernier contrôleur**, le bouton "Supprimer" est désactivé
- Un message d'avertissement s'affiche : "⚠️ Dernier contrôleur"
- La modal de suppression affiche un message explicatif au lieu du formulaire de confirmation

## Rôles et Permissions

### 👨‍💼 Contrôleur
- **Permissions complètes** sur la gestion des utilisateurs
- Peut créer, modifier et supprimer tous les comptes
- Accès à toutes les fonctionnalités du système
- Peut promouvoir des opérateurs en contrôleurs

### 👨‍🔧 Opérateur
- **Aucun accès** à la gestion des utilisateurs
- Ne peut pas voir ou modifier les comptes
- Accès limité aux fonctionnalités opérationnelles

## Interface Utilisateur

### Design
- **Interface moderne** avec Tailwind CSS et Flowbite
- **Mode sombre/clair** supporté
- **Responsive** pour tous les écrans
- **Accessibilité** avec support clavier

### Feedback Utilisateur
- **Messages de succès** en vert pour les actions réussies
- **Messages d'erreur** en rouge pour les problèmes
- **Confirmations** pour les actions destructives
- **Auto-actualisation** du tableau après modifications

## Politique de Mots de Passe

### Exigences par Défaut
Le système applique automatiquement les exigences suivantes pour tous les mots de passe :

- **Longueur minimale** : 8 caractères
- **Lettres majuscules** : Au moins une (A-Z)
- **Lettres minuscules** : Au moins une (a-z)
- **Chiffres** : Au moins un (0-9)
- **Motifs interdits** : password, admin, 123456, qwerty
- **Caractères consécutifs** : Maximum 3 identiques

### Interface de Validation
- **Validation en temps réel** : Vérification immédiate lors de la saisie
- **Indicateur de force** : Évaluation visuelle de la robustesse
- **Messages d'erreur** : Explications détaillées des problèmes
- **Générateur intégré** : Création automatique de mots de passe conformes

### Configuration Avancée
- **Page dédiée** : Accessible via le menu utilisateur → "Politique de Mots de Passe"
- **Raccourci** : <kbd>Alt</kbd> + <kbd>P</kbd>
- **Testeur intégré** : Validation de mots de passe existants
- **Paramètres futurs** : Interface de configuration en développement

## Raccourcis Clavier

| Raccourci | Action |
|-----------|--------|
| <kbd>Alt</kbd> + <kbd>U</kbd> | Accéder à la gestion des utilisateurs |
| <kbd>Alt</kbd> + <kbd>P</kbd> | Accéder à la politique de mots de passe |
| <kbd>Échap</kbd> | Fermer les modales ouvertes |
| <kbd>Entrée</kbd> | Valider les formulaires |

## Sécurité

### Authentification
- **Vérification de session** obligatoire
- **Contrôle des rôles** à chaque action
- **Protection CSRF** sur tous les formulaires

### Protection contre le verrouillage
- **Dernier contrôleur protégé** : Impossible de supprimer ou rétrograder le dernier contrôleur
- **Comptage automatique** : Le système compte en temps réel le nombre de contrôleurs
- **Messages d'avertissement** : Interface claire sur les restrictions
- **Prévention proactive** : Boutons désactivés quand l'action n'est pas autorisée

### Mots de passe
- **Politique de sécurité** : Exigences minimales configurables
- **Validation en temps réel** : Vérification côté client et serveur
- **Hachage sécurisé** avec `password_hash()` PHP
- **Confirmation requise** pour les changements
- **Générateur intégré** : Création automatique de mots de passe conformes
- **Indicateur de force** : Évaluation visuelle de la robustesse

### Validation des données
- **Vérifications côté serveur** pour toutes les actions critiques
- **Messages d'erreur explicites** pour les tentatives non autorisées
- **Sanitisation des données** d'entrée

## Dépannage

### Problèmes Courants

#### "Permission refusée"
- **Cause** : Utilisateur non contrôleur
- **Solution** : Demander à un contrôleur de modifier votre rôle

#### "Nom d'utilisateur déjà utilisé"
- **Cause** : Tentative de création avec un nom existant
- **Solution** : Choisir un nom d'utilisateur unique

#### "Erreur de connexion"
- **Cause** : Problème réseau ou serveur
- **Solution** : Vérifier la connexion et réessayer

### Support Technique

En cas de problème persistant :
1. Vérifier les logs du serveur
2. Contrôler la base de données
3. Contacter l'administrateur système

## Bonnes Pratiques

### Gestion des Comptes
- **Principe du moindre privilège** : Attribuer le rôle minimum nécessaire
- **Révision régulière** : Vérifier périodiquement les comptes actifs
- **Suppression des comptes inutilisés** : Nettoyer régulièrement

### Sécurité
- **Mots de passe forts** : Encourager l'utilisation de mots de passe complexes
- **Changement régulier** : Recommander le changement périodique
- **Formation utilisateurs** : Sensibiliser à la sécurité

---

<div className="role-controleur">
<strong>📝 Note Importante</strong><br/>
La gestion des utilisateurs est une responsabilité critique. Utilisez ces fonctionnalités avec précaution et suivez les bonnes pratiques de sécurité.
</div>
