#include "StatusIndicatorWidget.h"
#include <QDebug>
#include <QMessageBox>
#include <QApplication>

Q_LOGGING_CATEGORY(statusIndicatorLog, "hydraulic.widgets.statusindicator")

StatusIndicatorWidget::StatusIndicatorWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_animationEnabled(true)
    , m_compactMode(false)
    , m_blinkTimer(new QTimer(this))
    , m_overallStatus(IndicatorStatus::Unknown)
{
    setupUI();
    
    // Setup blink timer
    m_blinkTimer->setInterval(BLINK_INTERVAL_MS);
    connect(m_blinkTimer, &QTimer::timeout, this, &StatusIndicatorWidget::onBlinkTimer);
    
    // Initialize all indicators to unknown status
    resetAllStatus();
    
    qCDebug(statusIndicatorLog) << "StatusIndicatorWidget created";
}

void StatusIndicatorWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    setupOverallStatus();
    setupIndicators();
    setupControlButtons();
}

void StatusIndicatorWidget::setupOverallStatus()
{
    m_overallStatusGroup = new QGroupBox("System Status", this);
    QHBoxLayout* layout = new QHBoxLayout(m_overallStatusGroup);
    
    // Overall status icon
    m_overallStatusIcon = new QLabel();
    m_overallStatusIcon->setFixedSize(OVERALL_ICON_SIZE, OVERALL_ICON_SIZE);
    m_overallStatusIcon->setStyleSheet("background-color: gray; border-radius: 16px;");
    layout->addWidget(m_overallStatusIcon);
    
    // Overall status label
    m_overallStatusLabel = new QLabel("System Status: Unknown");
    m_overallStatusLabel->setStyleSheet("font-size: 14px; font-weight: bold;");
    layout->addWidget(m_overallStatusLabel);
    
    layout->addStretch();
    
    // Error and warning counts
    m_errorCountLabel = new QLabel("Errors: 0");
    m_errorCountLabel->setStyleSheet("color: red; font-weight: bold;");
    layout->addWidget(m_errorCountLabel);
    
    m_warningCountLabel = new QLabel("Warnings: 0");
    m_warningCountLabel->setStyleSheet("color: orange; font-weight: bold;");
    layout->addWidget(m_warningCountLabel);
    
    // System health bar
    m_systemHealthBar = new QProgressBar();
    m_systemHealthBar->setRange(0, 100);
    m_systemHealthBar->setValue(0);
    m_systemHealthBar->setMaximumWidth(150);
    layout->addWidget(m_systemHealthBar);
    
    m_mainLayout->addWidget(m_overallStatusGroup);
}

void StatusIndicatorWidget::setupIndicators()
{
    m_indicatorsGroup = new QGroupBox("Component Status", this);
    m_indicatorsLayout = new QGridLayout(m_indicatorsGroup);
    
    // Create indicators for each type
    QList<QPair<IndicatorType, QString>> indicatorTypes = {
        {IndicatorType::Connection, "Connection"},
        {IndicatorType::Hardware, "Hardware"},
        {IndicatorType::DataAcquisition, "Data Acquisition"},
        {IndicatorType::Safety, "Safety Systems"},
        {IndicatorType::Communication, "Communication"},
        {IndicatorType::Power, "Power Supply"},
        {IndicatorType::Temperature, "Temperature"},
        {IndicatorType::Pressure, "Pressure"}
    };
    
    int row = 0, col = 0;
    for (const auto& pair : indicatorTypes) {
        QWidget* indicator = createIndicator(pair.first, pair.second);
        m_indicatorsLayout->addWidget(indicator, row, col);
        
        col++;
        if (col >= 2) {
            col = 0;
            row++;
        }
    }
    
    m_mainLayout->addWidget(m_indicatorsGroup);
}

void StatusIndicatorWidget::setupControlButtons()
{
    m_controlGroup = new QGroupBox("Controls", this);
    QHBoxLayout* layout = new QHBoxLayout(m_controlGroup);
    
    m_acknowledgeButton = new QPushButton("Acknowledge Alarms");
    m_acknowledgeButton->setEnabled(false);
    connect(m_acknowledgeButton, &QPushButton::clicked, this, &StatusIndicatorWidget::onAcknowledgeClicked);
    layout->addWidget(m_acknowledgeButton);
    
    m_resetButton = new QPushButton("Reset Status");
    connect(m_resetButton, &QPushButton::clicked, this, &StatusIndicatorWidget::resetAllStatus);
    layout->addWidget(m_resetButton);
    
    m_testButton = new QPushButton("Test Indicators");
    connect(m_testButton, &QPushButton::clicked, this, [this]() {
        // Cycle through all indicators for testing
        static int testIndex = 0;
        QList<IndicatorType> types = {
            IndicatorType::Connection, IndicatorType::Hardware, IndicatorType::DataAcquisition,
            IndicatorType::Safety, IndicatorType::Communication, IndicatorType::Power,
            IndicatorType::Temperature, IndicatorType::Pressure
        };
        
        if (testIndex < types.size()) {
            setIndicatorStatus(types[testIndex], IndicatorStatus::Error, "Test alarm");
            testIndex++;
        } else {
            resetAllStatus();
            testIndex = 0;
        }
    });
    layout->addWidget(m_testButton);
    
    layout->addStretch();
    
    m_mainLayout->addWidget(m_controlGroup);
}

QWidget* StatusIndicatorWidget::createIndicator(IndicatorType type, const QString& label)
{
    QWidget* widget = new QWidget();
    widget->setMaximumHeight(60);
    QHBoxLayout* layout = new QHBoxLayout(widget);
    layout->setContentsMargins(5, 5, 5, 5);
    
    // Status icon
    QLabel* icon = new QLabel();
    icon->setFixedSize(INDICATOR_ICON_SIZE, INDICATOR_ICON_SIZE);
    icon->setStyleSheet("background-color: gray; border-radius: 12px;");
    layout->addWidget(icon);
    
    // Label and status
    QVBoxLayout* textLayout = new QVBoxLayout();
    
    QLabel* labelWidget = new QLabel(label);
    labelWidget->setStyleSheet("font-weight: bold;");
    textLayout->addWidget(labelWidget);
    
    QLabel* statusLabel = new QLabel("Unknown");
    statusLabel->setStyleSheet("font-size: 10px; color: gray;");
    textLayout->addWidget(statusLabel);
    
    layout->addLayout(textLayout);
    layout->addStretch();
    
    // Message label
    QLabel* messageLabel = new QLabel();
    messageLabel->setStyleSheet("font-size: 9px; color: gray;");
    messageLabel->setWordWrap(true);
    messageLabel->setMaximumWidth(100);
    layout->addWidget(messageLabel);
    
    // Setup indicator data
    IndicatorData data;
    data.widget = widget;
    data.icon = icon;
    data.label = labelWidget;
    data.status = statusLabel;
    data.message = messageLabel;
    data.currentStatus = IndicatorStatus::Unknown;
    data.currentMessage = "";
    data.isBlinking = false;
    
    // Setup opacity effect for blinking
    data.opacityEffect = new QGraphicsOpacityEffect();
    data.opacityEffect->setOpacity(1.0);
    icon->setGraphicsEffect(data.opacityEffect);
    
    // Setup animation
    data.blinkAnimation = new QPropertyAnimation(data.opacityEffect, "opacity");
    data.blinkAnimation->setDuration(BLINK_INTERVAL_MS);
    data.blinkAnimation->setStartValue(1.0);
    data.blinkAnimation->setEndValue(0.3);
    data.blinkAnimation->setLoopCount(-1);
    
    m_indicators[type] = data;
    
    // Make clickable
    widget->setCursor(Qt::PointingHandCursor);
    widget->installEventFilter(this);
    widget->setProperty("indicatorType", static_cast<int>(type));
    
    return widget;
}

void StatusIndicatorWidget::setIndicatorStatus(IndicatorType type, IndicatorStatus status, const QString& message)
{
    if (!m_indicators.contains(type)) {
        return;
    }
    
    IndicatorData& data = m_indicators[type];
    IndicatorStatus oldStatus = data.currentStatus;
    
    data.currentStatus = status;
    data.currentMessage = message;
    
    updateIndicatorAppearance(type);
    updateOverallStatus();
    
    // Handle alarm conditions
    if (status == IndicatorStatus::Error || status == IndicatorStatus::Warning) {
        if (!m_activeAlarms.contains(getIndicatorTypeText(type))) {
            m_activeAlarms.append(getIndicatorTypeText(type));
            emit alarmTriggered(type, message);
        }
        
        if (m_animationEnabled && status == IndicatorStatus::Error) {
            startBlinkAnimation(type);
        }
    } else {
        m_activeAlarms.removeAll(getIndicatorTypeText(type));
        stopBlinkAnimation(type);
    }
    
    // Update acknowledge button
    m_acknowledgeButton->setEnabled(!m_activeAlarms.isEmpty());
    
    if (oldStatus != status) {
        emit statusChanged(type, status);
    }
    
    qCDebug(statusIndicatorLog) << "Indicator" << getIndicatorTypeText(type) 
                               << "status changed to" << getStatusText(status);
}

void StatusIndicatorWidget::updateIndicatorAppearance(IndicatorType type)
{
    if (!m_indicators.contains(type)) {
        return;
    }
    
    const IndicatorData& data = m_indicators[type];
    QString color = getStatusColor(data.currentStatus);
    QString statusText = getStatusText(data.currentStatus);
    
    // Update icon color
    data.icon->setStyleSheet(QString("background-color: %1; border-radius: 12px;").arg(color));
    
    // Update status text
    data.status->setText(statusText);
    data.status->setStyleSheet(QString("font-size: 10px; color: %1; font-weight: bold;").arg(color));
    
    // Update message
    data.message->setText(data.currentMessage);
    data.message->setStyleSheet(QString("font-size: 9px; color: %1;").arg(color));
}

// Public slot implementations
void StatusIndicatorWidget::updateConnectionStatus(bool connected)
{
    IndicatorStatus status = connected ? IndicatorStatus::OK : IndicatorStatus::Error;
    QString message = connected ? "Connected" : "Connection lost";
    setIndicatorStatus(IndicatorType::Connection, status, message);
}

void StatusIndicatorWidget::updateHardwareStatus(bool operational)
{
    IndicatorStatus status = operational ? IndicatorStatus::OK : IndicatorStatus::Error;
    QString message = operational ? "Hardware operational" : "Hardware fault";
    setIndicatorStatus(IndicatorType::Hardware, status, message);
}

void StatusIndicatorWidget::updateDataAcquisitionStatus(bool active, double rate)
{
    IndicatorStatus status;
    QString message;

    if (!active) {
        status = IndicatorStatus::Error;
        message = "Data acquisition stopped";
    } else if (rate < 1.0) {
        status = IndicatorStatus::Warning;
        message = QString("Low data rate: %1 Hz").arg(rate, 0, 'f', 1);
    } else {
        status = IndicatorStatus::OK;
        message = QString("Active: %1 Hz").arg(rate, 0, 'f', 1);
    }

    setIndicatorStatus(IndicatorType::DataAcquisition, status, message);
}

void StatusIndicatorWidget::updateSafetyStatus(bool safe, const QStringList& violations)
{
    IndicatorStatus status = safe ? IndicatorStatus::OK : IndicatorStatus::Error;
    QString message = safe ? "All safety systems OK" : QString("Violations: %1").arg(violations.join(", "));
    setIndicatorStatus(IndicatorType::Safety, status, message);
}

void StatusIndicatorWidget::updateCommunicationStatus(bool communicating)
{
    IndicatorStatus status = communicating ? IndicatorStatus::OK : IndicatorStatus::Warning;
    QString message = communicating ? "Communication active" : "Communication timeout";
    setIndicatorStatus(IndicatorType::Communication, status, message);
}

void StatusIndicatorWidget::updatePowerStatus(bool powered)
{
    IndicatorStatus status = powered ? IndicatorStatus::OK : IndicatorStatus::Error;
    QString message = powered ? "Power supply OK" : "Power supply fault";
    setIndicatorStatus(IndicatorType::Power, status, message);
}

void StatusIndicatorWidget::updateTemperatureStatus(bool normal, double temperature)
{
    IndicatorStatus status = normal ? IndicatorStatus::OK : IndicatorStatus::Warning;
    QString message = QString("Temperature: %1°C").arg(temperature, 0, 'f', 1);
    if (!normal) {
        message += " (Out of range)";
    }
    setIndicatorStatus(IndicatorType::Temperature, status, message);
}

void StatusIndicatorWidget::updatePressureStatus(bool normal, double pressure)
{
    IndicatorStatus status = normal ? IndicatorStatus::OK : IndicatorStatus::Warning;
    QString message = QString("Pressure: %1 bar").arg(pressure, 0, 'f', 1);
    if (!normal) {
        message += " (Out of range)";
    }
    setIndicatorStatus(IndicatorType::Pressure, status, message);
}

void StatusIndicatorWidget::resetAllStatus()
{
    for (auto it = m_indicators.begin(); it != m_indicators.end(); ++it) {
        setIndicatorStatus(it.key(), IndicatorStatus::Unknown, "");
    }

    m_activeAlarms.clear();
    m_acknowledgeButton->setEnabled(false);
    updateOverallStatus();
}

void StatusIndicatorWidget::acknowledgeAlarms()
{
    for (auto it = m_indicators.begin(); it != m_indicators.end(); ++it) {
        if (it.value().currentStatus == IndicatorStatus::Error ||
            it.value().currentStatus == IndicatorStatus::Warning) {
            stopBlinkAnimation(it.key());
            emit alarmAcknowledged(it.key());
        }
    }

    m_acknowledgeButton->setEnabled(false);
}

// Utility methods
QString StatusIndicatorWidget::getStatusText(IndicatorStatus status) const
{
    switch (status) {
        case IndicatorStatus::OK: return "OK";
        case IndicatorStatus::Warning: return "Warning";
        case IndicatorStatus::Error: return "Error";
        case IndicatorStatus::Offline: return "Offline";
        case IndicatorStatus::Unknown:
        default: return "Unknown";
    }
}

QString StatusIndicatorWidget::getStatusColor(IndicatorStatus status) const
{
    switch (status) {
        case IndicatorStatus::OK: return "green";
        case IndicatorStatus::Warning: return "orange";
        case IndicatorStatus::Error: return "red";
        case IndicatorStatus::Offline: return "darkgray";
        case IndicatorStatus::Unknown:
        default: return "gray";
    }
}

QString StatusIndicatorWidget::getIndicatorTypeText(IndicatorType type) const
{
    switch (type) {
        case IndicatorType::Connection: return "Connection";
        case IndicatorType::Hardware: return "Hardware";
        case IndicatorType::DataAcquisition: return "Data Acquisition";
        case IndicatorType::Safety: return "Safety";
        case IndicatorType::Communication: return "Communication";
        case IndicatorType::Power: return "Power";
        case IndicatorType::Temperature: return "Temperature";
        case IndicatorType::Pressure: return "Pressure";
        default: return "Unknown";
    }
}

void StatusIndicatorWidget::updateOverallStatus()
{
    // Determine overall status based on individual indicators
    bool hasErrors = false;
    bool hasWarnings = false;
    int totalIndicators = m_indicators.size();
    int okIndicators = 0;

    for (const auto& data : m_indicators) {
        switch (data.currentStatus) {
            case IndicatorStatus::Error:
                hasErrors = true;
                break;
            case IndicatorStatus::Warning:
                hasWarnings = true;
                break;
            case IndicatorStatus::OK:
                okIndicators++;
                break;
            default:
                break;
        }
    }

    IndicatorStatus newOverallStatus;
    QString statusText;
    QString iconColor;
    int healthPercent = 0;

    if (hasErrors) {
        newOverallStatus = IndicatorStatus::Error;
        statusText = "System Error";
        iconColor = "red";
        healthPercent = 25;
    } else if (hasWarnings) {
        newOverallStatus = IndicatorStatus::Warning;
        statusText = "System Warning";
        iconColor = "orange";
        healthPercent = 60;
    } else if (okIndicators == totalIndicators) {
        newOverallStatus = IndicatorStatus::OK;
        statusText = "System Normal";
        iconColor = "green";
        healthPercent = 100;
    } else {
        newOverallStatus = IndicatorStatus::Unknown;
        statusText = "System Status Unknown";
        iconColor = "gray";
        healthPercent = 50;
    }

    // Update displays
    m_overallStatusLabel->setText(QString("System Status: %1").arg(statusText));
    m_overallStatusIcon->setStyleSheet(QString("background-color: %1; border-radius: 16px;").arg(iconColor));
    m_systemHealthBar->setValue(healthPercent);

    // Update counts
    int errorCount = 0;
    int warningCount = 0;
    for (const auto& data : m_indicators) {
        if (data.currentStatus == IndicatorStatus::Error) errorCount++;
        if (data.currentStatus == IndicatorStatus::Warning) warningCount++;
    }

    m_errorCountLabel->setText(QString("Errors: %1").arg(errorCount));
    m_warningCountLabel->setText(QString("Warnings: %1").arg(warningCount));

    // Emit signal if status changed
    if (newOverallStatus != m_overallStatus) {
        m_overallStatus = newOverallStatus;
        emit overallStatusChanged(m_overallStatus);
    }
}

void StatusIndicatorWidget::startBlinkAnimation(IndicatorType type)
{
    if (!m_indicators.contains(type) || !m_animationEnabled) {
        return;
    }

    IndicatorData& data = m_indicators[type];
    if (!data.isBlinking) {
        data.isBlinking = true;
        data.blinkAnimation->start();
    }
}

void StatusIndicatorWidget::stopBlinkAnimation(IndicatorType type)
{
    if (!m_indicators.contains(type)) {
        return;
    }

    IndicatorData& data = m_indicators[type];
    if (data.isBlinking) {
        data.isBlinking = false;
        data.blinkAnimation->stop();
        data.opacityEffect->setOpacity(1.0);
    }
}

// Slot implementations
void StatusIndicatorWidget::onIndicatorClicked()
{
    QWidget* sender = qobject_cast<QWidget*>(QObject::sender());
    if (sender) {
        bool ok;
        int typeInt = sender->property("indicatorType").toInt(&ok);
        if (ok) {
            IndicatorType type = static_cast<IndicatorType>(typeInt);
            emit indicatorClicked(type);

            // Show detailed information
            if (m_indicators.contains(type)) {
                const IndicatorData& data = m_indicators[type];
                QString info = QString("Indicator: %1\nStatus: %2\nMessage: %3")
                              .arg(getIndicatorTypeText(type))
                              .arg(getStatusText(data.currentStatus))
                              .arg(data.currentMessage.isEmpty() ? "None" : data.currentMessage);

                QMessageBox::information(this, "Indicator Details", info);
            }
        }
    }
}

void StatusIndicatorWidget::onBlinkTimer()
{
    // Timer-based blinking is handled by QPropertyAnimation
    // This slot is kept for potential future use
}

void StatusIndicatorWidget::onAcknowledgeClicked()
{
    acknowledgeAlarms();
}

// Configuration methods
void StatusIndicatorWidget::setAnimationEnabled(bool enabled)
{
    if (m_animationEnabled != enabled) {
        m_animationEnabled = enabled;

        if (!enabled) {
            // Stop all animations
            for (auto it = m_indicators.begin(); it != m_indicators.end(); ++it) {
                stopBlinkAnimation(it.key());
            }
        }
    }
}

void StatusIndicatorWidget::setCompactMode(bool compact)
{
    if (m_compactMode != compact) {
        m_compactMode = compact;

        // Adjust layout for compact mode
        if (compact) {
            // Reduce spacing and margins
            m_mainLayout->setSpacing(5);
            m_mainLayout->setContentsMargins(5, 5, 5, 5);

            // Hide some elements
            m_controlGroup->setVisible(false);
        } else {
            // Restore normal spacing
            m_mainLayout->setSpacing(10);
            m_mainLayout->setContentsMargins(10, 10, 10, 10);

            // Show all elements
            m_controlGroup->setVisible(true);
        }
    }
}

// Status access methods
StatusIndicatorWidget::IndicatorStatus StatusIndicatorWidget::getIndicatorStatus(IndicatorType type) const
{
    if (m_indicators.contains(type)) {
        return m_indicators[type].currentStatus;
    }
    return IndicatorStatus::Unknown;
}

QString StatusIndicatorWidget::getIndicatorMessage(IndicatorType type) const
{
    if (m_indicators.contains(type)) {
        return m_indicators[type].currentMessage;
    }
    return QString();
}

StatusIndicatorWidget::IndicatorStatus StatusIndicatorWidget::getOverallStatus() const
{
    return m_overallStatus;
}

QStringList StatusIndicatorWidget::getActiveAlarms() const
{
    return m_activeAlarms;
}

int StatusIndicatorWidget::getErrorCount() const
{
    int count = 0;
    for (const auto& data : m_indicators) {
        if (data.currentStatus == IndicatorStatus::Error) {
            count++;
        }
    }
    return count;
}

int StatusIndicatorWidget::getWarningCount() const
{
    int count = 0;
    for (const auto& data : m_indicators) {
        if (data.currentStatus == IndicatorStatus::Warning) {
            count++;
        }
    }
    return count;
}
