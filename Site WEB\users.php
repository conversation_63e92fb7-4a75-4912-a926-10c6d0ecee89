<?php
session_start();
require_once(__DIR__ . '/lib/user.php');
require_once(__DIR__ . '/lib/password_policy.php');

// Vérifier l'authentification
if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

// Vérifier que l'utilisateur est un contrôleur
if ($_SESSION['user']['role'] !== 'controleur') {
    header('Location: /index.php');
    exit;
}

// Traitement des actions POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                try {
                    if (User::create($_POST['username'], $_POST['password'], $_POST['role'])) {
                        $success = "Utilisateur créé avec succès";
                    } else {
                        $error = "Erreur lors de la création de l'utilisateur";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la création de l'utilisateur : " . $e->getMessage();
                }
                break;
            case 'update':
                try {
                    // Vérifier si le changement de rôle laisserait le système sans contrôleur
                    if (User::wouldLeaveNoControleur($_POST['id'], $_POST['role'])) {
                        $error = "Impossible de rétrograder ce contrôleur : il doit y avoir au moins un contrôleur dans le système";
                    } elseif (User::update($_POST['id'], $_POST['username'], $_POST['role'])) {
                        $success = "Utilisateur mis à jour avec succès";
                    } else {
                        $error = "Erreur lors de la mise à jour de l'utilisateur";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la mise à jour de l'utilisateur : " . $e->getMessage();
                }
                break;
            case 'update_password':
                try {
                    if ($_POST['password'] !== $_POST['confirm_password']) {
                        $error = "Les mots de passe ne correspondent pas";
                    } elseif (User::updatePassword($_POST['id'], $_POST['password'])) {
                        $success = "Mot de passe mis à jour avec succès";
                    } else {
                        $error = "Erreur lors de la mise à jour du mot de passe";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la mise à jour du mot de passe : " . $e->getMessage();
                }
                break;
            case 'delete':
                try {
                    // Empêcher la suppression de son propre compte
                    if ($_POST['id'] == $_SESSION['user']['id']) {
                        $error = "Impossible de supprimer votre propre compte";
                    }
                    // Empêcher la suppression du dernier contrôleur
                    elseif (User::isLastControleur($_POST['id'])) {
                        $error = "Impossible de supprimer le dernier contrôleur : il doit y avoir au moins un contrôleur dans le système";
                    }
                    elseif (User::delete($_POST['id'])) {
                        $success = "Utilisateur supprimé avec succès";
                    } else {
                        $error = "Erreur lors de la suppression de l'utilisateur";
                    }
                } catch (Exception $e) {
                    $error = "Erreur lors de la suppression de l'utilisateur : " . $e->getMessage();
                }
                break;
        }
    }
}

// Récupérer la liste des utilisateurs
try {
    $users = User::getAllUsers();
} catch (Exception $e) {
    if (!isset($error)) {
        $error = 'Erreur lors du chargement des utilisateurs : ' . $e->getMessage();
    }
    $users = [];
}

ob_start();
?>

<div class="mb-4 flex justify-between items-center">
    <h2 class="text-2xl font-bold dark:text-white">Gestion des Utilisateurs</h2>
    <div class="flex space-x-2">
        <span class="text-sm text-gray-500 dark:text-gray-400 self-center">
            <?php echo count($users); ?> utilisateurs (<?php echo User::countControleurs(); ?> contrôleurs)
        </span>
        <button data-modal-target="createUserModal" data-modal-toggle="createUserModal"
                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
            Nouvel Utilisateur
        </button>
    </div>
</div>

<!-- Exigences de mot de passe -->
<div class="flex items-start p-4 mb-4 text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-300" role="alert">
    <svg class="flex-shrink-0 w-4 h-4 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
    </svg>
    <div class="ms-3 text-sm">
        <strong>Exigences pour les mots de passe :</strong>
        <ul class="mt-2 list-disc list-inside">
            <?php foreach (PasswordPolicy::getRequirementsText() as $requirement): ?>
                <li><?php echo htmlspecialchars($requirement); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>

<!-- Information sur les protections -->
<?php if (User::countControleurs() <= 1): ?>
    <div class="flex items-center p-4 mb-4 text-amber-800 rounded-lg bg-amber-50 dark:bg-gray-800 dark:text-amber-300" role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium">
            <strong>Attention :</strong> Il n'y a qu'un seul contrôleur dans le système.
            Pour éviter le verrouillage, ce contrôleur ne peut pas être supprimé ou rétrogradé tant qu'il n'y en a pas d'autres.
        </div>
    </div>
<?php endif; ?>

<?php if (isset($success)): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-success" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
        <button type="button"
                class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
                data-dismiss-target="#alert-error" aria-label="Close">
            <span class="sr-only">Fermer</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>
<?php endif; ?>

<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">ID</th>
            <th scope="col" class="px-6 py-3">Nom d'utilisateur</th>
            <th scope="col" class="px-6 py-3">Rôle</th>
            <th scope="col" class="px-6 py-3">Date de création</th>
            <th scope="col" class="px-6 py-3">Actions</th>
        </tr>
        </thead>
        <tbody>
        <?php if (empty($users)): ?>
            <tr>
                <td colspan="5" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    Aucun utilisateur trouvé
                </td>
            </tr>
        <?php else: ?>
            <?php foreach ($users as $user): ?>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        <?php echo htmlspecialchars($user['id']); ?>
                    </th>
                    <td class="px-6 py-4"><?php echo htmlspecialchars($user['username']); ?></td>
                    <td class="px-6 py-4">
                        <span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-md border
                            <?php
                        if ($user['role'] == 'controleur') {
                            echo 'bg-purple-100 text-purple-800 dark:bg-gray-700 dark:text-purple-400 border-purple-100 dark:border-purple-500';
                        } else {
                            echo 'bg-blue-100 text-blue-800 dark:bg-gray-700 dark:text-blue-400 border-blue-100 dark:border-blue-500';
                        }
                        ?>">
                            <?php echo ucfirst(htmlspecialchars($user['role'])); ?>
                        </span>
                    </td>
                    <td class="px-6 py-4"><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                    <td class="px-6 py-4">
                        <button data-modal-target="editUserModal<?php echo $user['id']; ?>"
                                data-modal-toggle="editUserModal<?php echo $user['id']; ?>"
                                class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Modifier
                        </button>
                        <button data-modal-target="changePasswordModal<?php echo $user['id']; ?>"
                                data-modal-toggle="changePasswordModal<?php echo $user['id']; ?>"
                                class="font-medium text-green-600 dark:text-green-500 hover:underline ms-3">Mot de passe
                        </button>
                        <?php
                        $canDelete = $user['id'] != $_SESSION['user']['id'] && !User::isLastControleur($user['id']);
                        if ($user['id'] != $_SESSION['user']['id']):
                        ?>
                            <?php if ($canDelete): ?>
                                <button data-modal-target="deleteUserModal<?php echo $user['id']; ?>"
                                        data-modal-toggle="deleteUserModal<?php echo $user['id']; ?>"
                                        class="font-medium text-red-600 dark:text-red-500 hover:underline ms-3">Supprimer
                                </button>
                            <?php else: ?>
                                <span class="font-medium text-gray-400 dark:text-gray-500 ms-3"
                                      title="Impossible de supprimer le dernier contrôleur">Supprimer</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if (User::isLastControleur($user['id'])): ?>
                            <span class="text-xs text-amber-600 dark:text-amber-400 ms-3 font-medium"
                                  title="Dernier contrôleur du système">⚠️ Dernier contrôleur</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php endif; ?>
        </tbody>
    </table>
</div>

<div id="createUserModal" tabindex="-1" aria-hidden="true"
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Nouvel Utilisateur
                </h3>
                <button type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="createUserModal">
                    <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                    <span class="sr-only">Fermer</span>
                </button>
            </div>
            <form class="p-4 md:p-5" method="POST">
                <input type="hidden" name="action" value="create">
                <div class="grid gap-4 mb-4">
                    <div>
                        <label for="username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nom d'utilisateur</label>
                        <input type="text" name="username" id="username"
                               class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mot de passe</label>
                        <div class="relative">
                            <input type="password" name="password" id="password"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required oninput="validatePassword('password', 'password-strength', 'password-errors')">
                            <button type="button" onclick="togglePasswordVisibility('password')"
                                    class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </button>
                        </div>
                        <div id="password-strength" class="mt-1 text-xs"></div>
                        <div id="password-errors" class="mt-1 text-xs text-red-600 dark:text-red-400"></div>
                        <button type="button" onclick="generatePassword('password')"
                                class="mt-2 text-xs text-blue-600 dark:text-blue-400 hover:underline">
                            Générer un mot de passe sécurisé
                        </button>
                    </div>
                    <div>
                        <label for="role" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rôle</label>
                        <select name="role" id="role"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="operateur">Opérateur</option>
                            <option value="controleur">Contrôleur</option>
                        </select>
                    </div>
                </div>
                <button type="submit"
                        class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    Créer l'utilisateur
                </button>
            </form>
        </div>
    </div>
</div>

<?php foreach ($users as $user): ?>
    <!-- Modal de modification d'utilisateur -->
    <div id="editUserModal<?php echo $user['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Modifier l'utilisateur
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="editUserModal<?php echo $user['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="username<?php echo $user['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nom d'utilisateur</label>
                            <input type="text" name="username" id="username<?php echo $user['id']; ?>"
                                   value="<?php echo htmlspecialchars($user['username']); ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required>
                        </div>
                        <div>
                            <label for="role<?php echo $user['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rôle</label>
                            <select name="role" id="role<?php echo $user['id']; ?>"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <?php $isLastControleur = User::isLastControleur($user['id']); ?>
                                <option value="operateur"
                                        <?php echo $user['role'] === 'operateur' ? 'selected' : ''; ?>
                                        <?php echo $isLastControleur ? 'disabled' : ''; ?>>
                                    Opérateur <?php echo $isLastControleur ? '(Impossible - dernier contrôleur)' : ''; ?>
                                </option>
                                <option value="controleur" <?php echo $user['role'] === 'controleur' ? 'selected' : ''; ?>>
                                    Contrôleur
                                </option>
                            </select>
                            <?php if ($isLastControleur): ?>
                                <p class="mt-2 text-sm text-amber-600 dark:text-amber-400">
                                    ⚠️ Cet utilisateur est le dernier contrôleur et ne peut pas être rétrogradé.
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        Enregistrer les modifications
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de changement de mot de passe -->
    <div id="changePasswordModal<?php echo $user['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Changer le mot de passe
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="changePasswordModal<?php echo $user['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <form class="p-4 md:p-5" method="POST">
                    <input type="hidden" name="action" value="update_password">
                    <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                    <div class="grid gap-4 mb-4">
                        <div>
                            <label for="password<?php echo $user['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nouveau mot de passe</label>
                            <div class="relative">
                                <input type="password" name="password" id="password<?php echo $user['id']; ?>"
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                       required oninput="validatePassword('password<?php echo $user['id']; ?>', 'password-strength<?php echo $user['id']; ?>', 'password-errors<?php echo $user['id']; ?>')">
                                <button type="button" onclick="togglePasswordVisibility('password<?php echo $user['id']; ?>')"
                                        class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </button>
                            </div>
                            <div id="password-strength<?php echo $user['id']; ?>" class="mt-1 text-xs"></div>
                            <div id="password-errors<?php echo $user['id']; ?>" class="mt-1 text-xs text-red-600 dark:text-red-400"></div>
                            <button type="button" onclick="generatePassword('password<?php echo $user['id']; ?>')"
                                    class="mt-2 text-xs text-blue-600 dark:text-blue-400 hover:underline">
                                Générer un mot de passe sécurisé
                            </button>
                        </div>
                        <div>
                            <label for="confirm_password<?php echo $user['id']; ?>"
                                   class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirmer le mot de passe</label>
                            <input type="password" name="confirm_password" id="confirm_password<?php echo $user['id']; ?>"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                   required oninput="checkPasswordMatch('password<?php echo $user['id']; ?>', 'confirm_password<?php echo $user['id']; ?>', 'password-match<?php echo $user['id']; ?>')">
                            <div id="password-match<?php echo $user['id']; ?>" class="mt-1 text-xs"></div>
                        </div>
                    </div>
                    <button type="submit"
                            class="text-white inline-flex items-center bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                        Changer le mot de passe
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal de suppression d'utilisateur -->
    <div id="deleteUserModal<?php echo $user['id']; ?>" tabindex="-1" aria-hidden="true"
         class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-md max-h-full">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Confirmer la suppression
                    </h3>
                    <button type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteUserModal<?php echo $user['id']; ?>">
                        <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Fermer</span>
                    </button>
                </div>
                <div class="p-4 md:p-5">
                    <?php $isLastControleur = User::isLastControleur($user['id']); ?>
                    <?php if ($isLastControleur): ?>
                        <div class="flex items-center p-4 mb-4 text-amber-800 rounded-lg bg-amber-50 dark:bg-gray-800 dark:text-amber-300" role="alert">
                            <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                            </svg>
                            <div class="ms-3 text-sm font-medium">
                                <strong>Suppression impossible !</strong><br>
                                Cet utilisateur est le dernier contrôleur du système. Il doit y avoir au moins un contrôleur pour éviter le verrouillage du système.
                            </div>
                        </div>
                        <button type="button" data-modal-hide="deleteUserModal<?php echo $user['id']; ?>"
                                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Fermer
                        </button>
                    <?php else: ?>
                        <p class="text-gray-500 dark:text-gray-300">Êtes-vous sûr de vouloir supprimer l'utilisateur <strong><?php echo htmlspecialchars($user['username']); ?></strong> ? Cette action est irréversible.</p>
                        <form method="POST" class="mt-5">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                            <button type="submit"
                                    class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                                Oui, supprimer
                            </button>
                            <button type="button" data-modal-hide="deleteUserModal<?php echo $user['id']; ?>"
                                    class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                                Non, annuler
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>



<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>

<script>
// Configuration des exigences de mot de passe (côté client)
const passwordRequirements = <?php echo json_encode(PasswordPolicy::getRequirements()); ?>;

// Fonction de validation de mot de passe en temps réel
function validatePassword(inputId, strengthId, errorsId) {
    const password = document.getElementById(inputId).value;
    const strengthElement = document.getElementById(strengthId);
    const errorsElement = document.getElementById(errorsId);

    if (!password) {
        strengthElement.innerHTML = '';
        errorsElement.innerHTML = '';
        return;
    }

    const validation = validatePasswordClient(password);

    // Afficher la force du mot de passe
    const strengthColor = getStrengthColor(validation.strength);
    const strengthText = getStrengthText(validation.strength);
    strengthElement.innerHTML = `<span class="${strengthColor}">Force: ${strengthText} (${validation.score}/100)</span>`;

    // Afficher les erreurs
    if (validation.errors.length > 0) {
        errorsElement.innerHTML = validation.errors.map(error => `• ${error}`).join('<br>');
    } else {
        errorsElement.innerHTML = '<span class="text-green-600 dark:text-green-400">✓ Mot de passe conforme</span>';
    }
}

// Validation côté client (réplique de la logique PHP)
function validatePasswordClient(password) {
    const result = {
        valid: true,
        errors: [],
        score: 0,
        strength: 'weak'
    };

    // Longueur minimale
    if (password.length < passwordRequirements.min_length) {
        result.valid = false;
        result.errors.push(`Au moins ${passwordRequirements.min_length} caractères`);
    } else {
        result.score += 20;
    }

    // Majuscules
    if (passwordRequirements.require_uppercase && !/[A-Z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre majuscule');
    } else if (passwordRequirements.require_uppercase) {
        result.score += 15;
    }

    // Minuscules
    if (passwordRequirements.require_lowercase && !/[a-z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre minuscule');
    } else if (passwordRequirements.require_lowercase) {
        result.score += 15;
    }

    // Chiffres
    if (passwordRequirements.require_numbers && !/[0-9]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins un chiffre');
    } else if (passwordRequirements.require_numbers) {
        result.score += 15;
    }

    // Caractères spéciaux
    if (passwordRequirements.require_special_chars) {
        const specialChars = (password.match(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g) || []).length;
        if (specialChars < passwordRequirements.min_special_chars) {
            result.valid = false;
            result.errors.push(`Au moins ${passwordRequirements.min_special_chars} caractère(s) spécial(aux)`);
        } else {
            result.score += 20;
        }
    }

    // Motifs interdits
    for (const pattern of passwordRequirements.forbidden_patterns) {
        if (password.toLowerCase().includes(pattern.toLowerCase())) {
            result.valid = false;
            result.errors.push(`Ne doit pas contenir "${pattern}"`);
            break;
        }
    }

    // Caractères consécutifs
    if (hasConsecutiveChars(password, passwordRequirements.max_consecutive_chars)) {
        result.valid = false;
        result.errors.push(`Pas plus de ${passwordRequirements.max_consecutive_chars} caractères identiques consécutifs`);
    }

    // Bonus longueur
    if (password.length >= 12) result.score += 10;
    if (password.length >= 16) result.score += 5;

    // Déterminer la force
    if (result.score >= 80) result.strength = 'very_strong';
    else if (result.score >= 60) result.strength = 'strong';
    else if (result.score >= 40) result.strength = 'medium';
    else if (result.score >= 20) result.strength = 'weak';
    else result.strength = 'very_weak';

    return result;
}

// Vérifier les caractères consécutifs
function hasConsecutiveChars(password, maxConsecutive) {
    let count = 1;
    for (let i = 1; i < password.length; i++) {
        if (password[i] === password[i - 1]) {
            count++;
            if (count > maxConsecutive) return true;
        } else {
            count = 1;
        }
    }
    return false;
}

// Obtenir la couleur selon la force
function getStrengthColor(strength) {
    switch (strength) {
        case 'very_strong': return 'text-green-600 dark:text-green-400';
        case 'strong': return 'text-green-500 dark:text-green-300';
        case 'medium': return 'text-yellow-500 dark:text-yellow-400';
        case 'weak': return 'text-orange-500 dark:text-orange-400';
        default: return 'text-red-600 dark:text-red-400';
    }
}

// Obtenir le texte de force
function getStrengthText(strength) {
    switch (strength) {
        case 'very_strong': return 'Très fort';
        case 'strong': return 'Fort';
        case 'medium': return 'Moyen';
        case 'weak': return 'Faible';
        default: return 'Très faible';
    }
}

// Vérifier la correspondance des mots de passe
function checkPasswordMatch(passwordId, confirmId, matchId) {
    const password = document.getElementById(passwordId).value;
    const confirm = document.getElementById(confirmId).value;
    const matchElement = document.getElementById(matchId);

    if (!confirm) {
        matchElement.innerHTML = '';
        return;
    }

    if (password === confirm) {
        matchElement.innerHTML = '<span class="text-green-600 dark:text-green-400">✓ Les mots de passe correspondent</span>';
    } else {
        matchElement.innerHTML = '<span class="text-red-600 dark:text-red-400">✗ Les mots de passe ne correspondent pas</span>';
    }
}

// Basculer la visibilité du mot de passe
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

// Générer un mot de passe sécurisé
function generatePassword(inputId) {
    // Générer un mot de passe selon les exigences
    let chars = '';
    if (passwordRequirements.require_lowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
    if (passwordRequirements.require_uppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (passwordRequirements.require_numbers) chars += '0123456789';
    if (passwordRequirements.require_special_chars) chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (!chars) chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    let password = '';
    const length = Math.max(passwordRequirements.min_length, 12);

    // S'assurer qu'on a au moins un caractère de chaque type requis
    if (passwordRequirements.require_lowercase) password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_uppercase) password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_numbers) password += '0123456789'[Math.floor(Math.random() * 10)];
    if (passwordRequirements.require_special_chars) password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

    // Compléter avec des caractères aléatoires
    for (let i = password.length; i < length; i++) {
        password += chars[Math.floor(Math.random() * chars.length)];
    }

    // Mélanger le mot de passe
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    // Mettre à jour le champ
    document.getElementById(inputId).value = password;

    // Déclencher la validation
    const strengthId = inputId.replace('password', 'password-strength');
    const errorsId = inputId.replace('password', 'password-errors');
    validatePassword(inputId, strengthId, errorsId);

    // Copier dans le presse-papiers si possible
    if (navigator.clipboard) {
        navigator.clipboard.writeText(password).then(() => {
            alert('Mot de passe généré et copié dans le presse-papiers !');
        });
    } else {
        alert('Mot de passe généré ! Pensez à le copier.');
    }
}
</script>
