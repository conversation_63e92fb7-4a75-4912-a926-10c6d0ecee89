<?php
session_start();
require_once(__DIR__ . '/lib/user.php');
require_once(__DIR__ . '/lib/password_policy.php');

// Vérifier l'authentification
if (!isset($_SESSION['user'])) {
    header('Location: /auth/login.php');
    exit;
}

// Vérifier que l'utilisateur est un contrôleur
if ($_SESSION['user']['role'] !== 'controleur') {
    header('Location: /index.php');
    exit;
}

// Traitement des actions POST (pour une future implémentation)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // TODO: Implémenter la sauvegarde des paramètres en base de données
    $success = "Fonctionnalité en cours de développement";
}

// Récupérer les exigences actuelles
$requirements = PasswordPolicy::getRequirements();
$requirementsText = PasswordPolicy::getRequirementsText();

ob_start();
?>

<div class="mb-4 flex justify-between items-center">
    <h2 class="text-2xl font-bold dark:text-white">Politique de Mots de Passe</h2>
    <div class="flex space-x-2">
        <a href="/users.php" 
           class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
            Retour aux Utilisateurs
        </a>
    </div>
</div>

<?php if (isset($success)): ?>
    <div id="alert-success"
         class="flex items-center p-4 mb-4 text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($success); ?></div>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div id="alert-error"
         class="flex items-center p-4 mb-4 text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
         role="alert">
        <svg class="flex-shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <div class="ms-3 text-sm font-medium"><?php echo htmlspecialchars($error); ?></div>
    </div>
<?php endif; ?>

<!-- Exigences actuelles -->
<div class="bg-white dark:bg-gray-800 shadow-md sm:rounded-lg p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Exigences Actuelles</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Paramètres de base -->
        <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Paramètres de Base</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Longueur minimale :</span>
                    <span class="font-medium"><?php echo $requirements['min_length']; ?> caractères</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Majuscules requises :</span>
                    <span class="font-medium"><?php echo $requirements['require_uppercase'] ? 'Oui' : 'Non'; ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Minuscules requises :</span>
                    <span class="font-medium"><?php echo $requirements['require_lowercase'] ? 'Oui' : 'Non'; ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Chiffres requis :</span>
                    <span class="font-medium"><?php echo $requirements['require_numbers'] ? 'Oui' : 'Non'; ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Caractères spéciaux :</span>
                    <span class="font-medium"><?php echo $requirements['require_special_chars'] ? 'Oui (' . $requirements['min_special_chars'] . ' min)' : 'Non'; ?></span>
                </div>
            </div>
        </div>
        
        <!-- Paramètres avancés -->
        <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Paramètres Avancés</h4>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Caractères consécutifs max :</span>
                    <span class="font-medium"><?php echo $requirements['max_consecutive_chars']; ?></span>
                </div>
                <div>
                    <span class="text-gray-600 dark:text-gray-400">Motifs interdits :</span>
                    <div class="mt-1">
                        <?php foreach ($requirements['forbidden_patterns'] as $pattern): ?>
                            <span class="inline-block bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">
                                <?php echo htmlspecialchars($pattern); ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Résumé des exigences -->
<div class="bg-blue-50 dark:bg-gray-800 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-6">
    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-4">
        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
        </svg>
        Exigences pour les Utilisateurs
    </h3>
    <ul class="space-y-1 text-sm text-blue-800 dark:text-blue-300">
        <?php foreach ($requirementsText as $requirement): ?>
            <li class="flex items-start">
                <svg class="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                <?php echo htmlspecialchars($requirement); ?>
            </li>
        <?php endforeach; ?>
    </ul>
</div>

<!-- Testeur de mot de passe -->
<div class="bg-white dark:bg-gray-800 shadow-md sm:rounded-lg p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Testeur de Mot de Passe</h3>
    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Testez un mot de passe pour voir s'il respecte les exigences actuelles.
    </p>
    
    <div class="max-w-md">
        <label for="test-password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
            Mot de passe à tester
        </label>
        <div class="relative">
            <input type="password" id="test-password"
                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pr-10 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                   placeholder="Saisissez un mot de passe..."
                   oninput="testPassword()">
            <button type="button" onclick="toggleTestPasswordVisibility()" 
                    class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
            </button>
        </div>
        
        <div id="test-result" class="mt-3"></div>
        
        <button type="button" onclick="generateTestPassword()" 
                class="mt-3 text-sm text-blue-600 dark:text-blue-400 hover:underline">
            Générer un exemple de mot de passe conforme
        </button>
    </div>
</div>

<!-- Configuration future -->
<div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
        </svg>
        Configuration Avancée
    </h3>
    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
        La modification des paramètres de politique de mot de passe sera disponible dans une future version.
        Actuellement, les paramètres sont définis dans le code source.
    </p>
    
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex">
            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            <div>
                <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-300">Fonctionnalité en développement</h4>
                <p class="text-sm text-yellow-700 dark:text-yellow-400 mt-1">
                    Pour modifier les paramètres actuellement, contactez l'administrateur système.
                    Une interface de configuration sera ajoutée prochainement.
                </p>
            </div>
        </div>
    </div>
</div>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>

<script>
// Configuration des exigences (côté client)
const passwordRequirements = <?php echo json_encode($requirements); ?>;

// Fonction de test de mot de passe
function testPassword() {
    const password = document.getElementById('test-password').value;
    const resultElement = document.getElementById('test-result');
    
    if (!password) {
        resultElement.innerHTML = '';
        return;
    }
    
    const validation = validatePasswordClient(password);
    
    let html = '<div class="space-y-2">';
    
    // Force du mot de passe
    const strengthColor = getStrengthColor(validation.strength);
    const strengthText = getStrengthText(validation.strength);
    html += `<div class="flex justify-between items-center">`;
    html += `<span class="text-sm font-medium">Force du mot de passe :</span>`;
    html += `<span class="${strengthColor} text-sm font-medium">${strengthText} (${validation.score}/100)</span>`;
    html += `</div>`;
    
    // Statut global
    if (validation.valid) {
        html += `<div class="flex items-center text-green-600 dark:text-green-400 text-sm">`;
        html += `<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">`;
        html += `<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>`;
        html += `</svg>Mot de passe conforme</div>`;
    } else {
        html += `<div class="flex items-center text-red-600 dark:text-red-400 text-sm">`;
        html += `<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">`;
        html += `<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>`;
        html += `</svg>Mot de passe non conforme</div>`;
    }
    
    // Erreurs
    if (validation.errors.length > 0) {
        html += `<div class="text-sm text-red-600 dark:text-red-400">`;
        html += `<div class="font-medium mb-1">Problèmes détectés :</div>`;
        html += `<ul class="list-disc list-inside space-y-1">`;
        validation.errors.forEach(error => {
            html += `<li>${error}</li>`;
        });
        html += `</ul></div>`;
    }
    
    html += '</div>';
    resultElement.innerHTML = html;
}

// Basculer la visibilité du mot de passe de test
function toggleTestPasswordVisibility() {
    const input = document.getElementById('test-password');
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

// Générer un mot de passe de test
function generateTestPassword() {
    const password = generateSecurePassword();
    document.getElementById('test-password').value = password;
    testPassword();
}

// Fonctions de validation (répliques de users.php)
function validatePasswordClient(password) {
    const result = { valid: true, errors: [], score: 0, strength: 'weak' };

    if (password.length < passwordRequirements.min_length) {
        result.valid = false;
        result.errors.push(`Au moins ${passwordRequirements.min_length} caractères`);
    } else { result.score += 20; }

    if (passwordRequirements.require_uppercase && !/[A-Z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre majuscule');
    } else if (passwordRequirements.require_uppercase) { result.score += 15; }

    if (passwordRequirements.require_lowercase && !/[a-z]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins une lettre minuscule');
    } else if (passwordRequirements.require_lowercase) { result.score += 15; }

    if (passwordRequirements.require_numbers && !/[0-9]/.test(password)) {
        result.valid = false;
        result.errors.push('Au moins un chiffre');
    } else if (passwordRequirements.require_numbers) { result.score += 15; }

    if (passwordRequirements.require_special_chars) {
        const specialChars = (password.match(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g) || []).length;
        if (specialChars < passwordRequirements.min_special_chars) {
            result.valid = false;
            result.errors.push(`Au moins ${passwordRequirements.min_special_chars} caractère(s) spécial(aux)`);
        } else { result.score += 20; }
    }

    for (const pattern of passwordRequirements.forbidden_patterns) {
        if (password.toLowerCase().includes(pattern.toLowerCase())) {
            result.valid = false;
            result.errors.push(`Ne doit pas contenir "${pattern}"`);
            break;
        }
    }

    if (hasConsecutiveChars(password, passwordRequirements.max_consecutive_chars)) {
        result.valid = false;
        result.errors.push(`Pas plus de ${passwordRequirements.max_consecutive_chars} caractères identiques consécutifs`);
    }

    if (password.length >= 12) result.score += 10;
    if (password.length >= 16) result.score += 5;

    if (result.score >= 80) result.strength = 'very_strong';
    else if (result.score >= 60) result.strength = 'strong';
    else if (result.score >= 40) result.strength = 'medium';
    else if (result.score >= 20) result.strength = 'weak';
    else result.strength = 'very_weak';

    return result;
}

function hasConsecutiveChars(password, maxConsecutive) {
    let count = 1;
    for (let i = 1; i < password.length; i++) {
        if (password[i] === password[i - 1]) {
            count++;
            if (count > maxConsecutive) return true;
        } else { count = 1; }
    }
    return false;
}

function getStrengthColor(strength) {
    switch (strength) {
        case 'very_strong': return 'text-green-600 dark:text-green-400';
        case 'strong': return 'text-green-500 dark:text-green-300';
        case 'medium': return 'text-yellow-500 dark:text-yellow-400';
        case 'weak': return 'text-orange-500 dark:text-orange-400';
        default: return 'text-red-600 dark:text-red-400';
    }
}

function getStrengthText(strength) {
    switch (strength) {
        case 'very_strong': return 'Très fort';
        case 'strong': return 'Fort';
        case 'medium': return 'Moyen';
        case 'weak': return 'Faible';
        default: return 'Très faible';
    }
}

function generateSecurePassword() {
    let chars = '';
    if (passwordRequirements.require_lowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
    if (passwordRequirements.require_uppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (passwordRequirements.require_numbers) chars += '0123456789';
    if (passwordRequirements.require_special_chars) chars += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (!chars) chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    let password = '';
    const length = Math.max(passwordRequirements.min_length, 12);

    if (passwordRequirements.require_lowercase) password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_uppercase) password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
    if (passwordRequirements.require_numbers) password += '0123456789'[Math.floor(Math.random() * 10)];
    if (passwordRequirements.require_special_chars) password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

    for (let i = password.length; i < length; i++) {
        password += chars[Math.floor(Math.random() * chars.length)];
    }

    return password.split('').sort(() => Math.random() - 0.5).join('');
}
</script>
