#ifndef APIENDPOINTS_H
#define APIENDPOINTS_H

#include <QString>

/**
 * @brief Centralized API endpoint definitions
 * 
 * This class contains all the REST API endpoints used to communicate
 * with the PHP web server backend.
 */
class ApiEndpoints
{
public:
    // Base configuration
    static const QString BASE_URL;
    static const QString API_BASE;
    
    // Authentication endpoints
    static const QString AUTH_LOGIN;
    
    // User management endpoints
    static const QString USERS;
    static const QString USER_BY_ID;
    
    // Business case (affaires) endpoints
    static const QString AFFAIRES;
    static const QString AFFAIRE_BY_ID;
    static const QString AFFAIRES_BY_STATUS;
    
    // Test case (essais) endpoints
    static const QString ESSAIS;
    static const QString ESSAI_BY_ID;
    static const QString ESSAIS_BY_AFFAIRE;
    static const QString ESSAIS_BY_STATUS;
    
    // Curve data endpoints
    static const QString COURBES;
    static const QString COURBE_BY_ID;
    static const QString COURBES_BY_ESSAI;
    static const QString COURBES_BY_TYPE;
    
    // Process verbal (PV) endpoints
    static const QString PV;
    static const QString PV_BY_ID;
    static const QString PV_BY_AFFAIRE;
    
    // Performance/efficiency endpoints
    static const QString RENDEMENT;
    static const QString RENDEMENT_BY_ESSAI;
    
    // Backup endpoints
    static const QString BACKUP;
    static const QString BACKUP_CREATE;
    
    // PDF generation endpoints
    static const QString PDF;
    static const QString PDF_GENERATE;
    
    // Utility methods
    static QString buildUrl(const QString& endpoint);
    static QString buildUrlWithId(const QString& endpoint, int id);
    static QString buildUrlWithParam(const QString& endpoint, const QString& param, const QString& value);
    
private:
    ApiEndpoints() = default; // Static class, no instantiation
};

#endif // APIENDPOINTS_H
