#ifndef TESTDATA_H
#define TESTDATA_H

#include <QObject>
#include <QDateTime>
#include <QVariant>
#include <QJsonObject>
#include <QJsonArray>

/**
 * @brief Real-time test data container
 * 
 * This class represents a single data point collected during hydraulic testing,
 * including all sensor readings and metadata.
 */
class TestData
{
public:
    TestData();
    TestData(const QDateTime& timestamp);
    TestData(const TestData& other);
    TestData& operator=(const TestData& other);
    
    // Timestamp
    QDateTime timestamp() const { return m_timestamp; }
    void setTimestamp(const QDateTime& timestamp) { m_timestamp = timestamp; }
    
    // Pressure readings (in bar)
    double pressureCPA() const { return m_pressureCPA; }
    void setPressureCPA(double pressure) { m_pressureCPA = pressure; }
    
    double pressureCPB() const { return m_pressureCPB; }
    void setPressureCPB(double pressure) { m_pressureCPB = pressure; }
    
    double pressureSupply() const { return m_pressureSupply; }
    void setPressureSupply(double pressure) { m_pressureSupply = pressure; }
    
    double pressureReturn() const { return m_pressureReturn; }
    void setPressureReturn(double pressure) { m_pressureReturn = pressure; }
    
    // Flow readings (in L/min)
    double flowRate() const { return m_flowRate; }
    void setFlowRate(double flow) { m_flowRate = flow; }
    
    // Temperature readings (in °C)
    double temperatureFluid() const { return m_temperatureFluid; }
    void setTemperatureFluid(double temp) { m_temperatureFluid = temp; }
    
    double temperatureAmbient() const { return m_temperatureAmbient; }
    void setTemperatureAmbient(double temp) { m_temperatureAmbient = temp; }
    
    // Actuator data
    double actuatorPosition() const { return m_actuatorPosition; }
    void setActuatorPosition(double position) { m_actuatorPosition = position; }
    
    double actuatorVelocity() const { return m_actuatorVelocity; }
    void setActuatorVelocity(double velocity) { m_actuatorVelocity = velocity; }
    
    double actuatorForce() const { return m_actuatorForce; }
    void setActuatorForce(double force) { m_actuatorForce = force; }
    
    // Calculated values
    double powerHydraulic() const { return m_powerHydraulic; }
    void setPowerHydraulic(double power) { m_powerHydraulic = power; }
    
    double efficiency() const { return m_efficiency; }
    void setEfficiency(double eff) { m_efficiency = eff; }
    
    // Generic parameter access
    QVariant parameter(const QString& name) const;
    void setParameter(const QString& name, const QVariant& value);
    QStringList parameterNames() const;
    
    // Data validation
    bool isValid() const;
    QStringList validate() const;
    
    // Serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
    // Operators
    bool operator==(const TestData& other) const;
    bool operator!=(const TestData& other) const { return !(*this == other); }
    
    // Static factory methods
    static TestData fromSensorReadings(const QMap<QString, QVariant>& readings);
    static TestData interpolate(const TestData& data1, const TestData& data2, double factor);
    
private:
    QDateTime m_timestamp;
    
    // Pressure readings
    double m_pressureCPA;
    double m_pressureCPB;
    double m_pressureSupply;
    double m_pressureReturn;
    
    // Flow readings
    double m_flowRate;
    
    // Temperature readings
    double m_temperatureFluid;
    double m_temperatureAmbient;
    
    // Actuator data
    double m_actuatorPosition;
    double m_actuatorVelocity;
    double m_actuatorForce;
    
    // Calculated values
    double m_powerHydraulic;
    double m_efficiency;
    
    // Additional parameters
    QMap<QString, QVariant> m_additionalParameters;
    
    void initializeDefaults();
};

/**
 * @brief Collection of test data points
 */
class TestDataSet
{
public:
    TestDataSet();
    TestDataSet(const QString& testName);
    
    // Basic operations
    void addDataPoint(const TestData& data);
    void removeDataPoint(int index);
    void clear();
    
    // Access
    int size() const { return m_dataPoints.size(); }
    bool isEmpty() const { return m_dataPoints.isEmpty(); }
    const TestData& at(int index) const { return m_dataPoints.at(index); }
    TestData& operator[](int index) { return m_dataPoints[index]; }
    const TestData& operator[](int index) const { return m_dataPoints[index]; }
    
    // Metadata
    QString testName() const { return m_testName; }
    void setTestName(const QString& name) { m_testName = name; }
    
    QDateTime startTime() const { return m_startTime; }
    void setStartTime(const QDateTime& time) { m_startTime = time; }
    
    QDateTime endTime() const { return m_endTime; }
    void setEndTime(const QDateTime& time) { m_endTime = time; }
    
    // Statistics
    TestData averageValues() const;
    TestData minimumValues() const;
    TestData maximumValues() const;
    double duration() const; // in seconds
    
    // Data filtering
    TestDataSet filterByTimeRange(const QDateTime& start, const QDateTime& end) const;
    TestDataSet filterByParameter(const QString& parameter, double minValue, double maxValue) const;
    TestDataSet downsample(int factor) const;
    
    // Serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
private:
    QString m_testName;
    QDateTime m_startTime;
    QDateTime m_endTime;
    QList<TestData> m_dataPoints;
};

#endif // TESTDATA_H
