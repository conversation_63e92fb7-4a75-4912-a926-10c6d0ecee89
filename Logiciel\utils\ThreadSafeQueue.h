#ifndef THREADSAFEQUEUE_H
#define THREADSAFEQUEUE_H

#include <QQueue>
#include <QMutex>
#include <QWaitCondition>
#include <QMutexLocker>

/**
 * @brief Thread-safe queue for data exchange between threads
 * 
 * This template class provides a thread-safe queue implementation
 * for passing data between the data acquisition thread and the main thread.
 */
template<typename T>
class ThreadSafeQueue
{
public:
    ThreadSafeQueue() = default;
    ~ThreadSafeQueue() = default;
    
    // Non-copyable
    ThreadSafeQueue(const ThreadSafeQueue&) = delete;
    ThreadSafeQueue& operator=(const ThreadSafeQueue&) = delete;
    
    /**
     * @brief Add an item to the queue
     * @param item The item to add
     */
    void enqueue(const T& item)
    {
        QMutexLocker locker(&m_mutex);
        m_queue.enqueue(item);
        m_condition.wakeOne();
    }
    
    /**
     * @brief Remove and return an item from the queue
     * @param item Reference to store the dequeued item
     * @return true if an item was dequeued, false if queue is empty
     */
    bool dequeue(T& item)
    {
        QMutexLocker locker(&m_mutex);
        if (m_queue.isEmpty()) {
            return false;
        }
        item = m_queue.dequeue();
        return true;
    }
    
    /**
     * @brief Wait for an item to become available and dequeue it
     * @param item Reference to store the dequeued item
     * @param timeoutMs Maximum time to wait in milliseconds (0 = wait forever)
     * @return true if an item was dequeued, false if timeout occurred
     */
    bool waitDequeue(T& item, unsigned long timeoutMs = 0)
    {
        QMutexLocker locker(&m_mutex);
        
        while (m_queue.isEmpty()) {
            if (timeoutMs == 0) {
                m_condition.wait(&m_mutex);
            } else {
                if (!m_condition.wait(&m_mutex, timeoutMs)) {
                    return false; // Timeout
                }
            }
        }
        
        item = m_queue.dequeue();
        return true;
    }
    
    /**
     * @brief Check if the queue is empty
     * @return true if empty, false otherwise
     */
    bool isEmpty() const
    {
        QMutexLocker locker(&m_mutex);
        return m_queue.isEmpty();
    }
    
    /**
     * @brief Get the number of items in the queue
     * @return Number of items
     */
    int size() const
    {
        QMutexLocker locker(&m_mutex);
        return m_queue.size();
    }
    
    /**
     * @brief Clear all items from the queue
     */
    void clear()
    {
        QMutexLocker locker(&m_mutex);
        m_queue.clear();
    }
    
    /**
     * @brief Get a copy of all items without removing them
     * @return List of all items in the queue
     */
    QList<T> toList() const
    {
        QMutexLocker locker(&m_mutex);
        QList<T> result;
        for (const T& item : m_queue) {
            result.append(item);
        }
        return result;
    }
    
    /**
     * @brief Set maximum queue size (0 = unlimited)
     * @param maxSize Maximum number of items to store
     */
    void setMaxSize(int maxSize)
    {
        QMutexLocker locker(&m_mutex);
        m_maxSize = maxSize;
        
        // Remove excess items if necessary
        while (m_maxSize > 0 && m_queue.size() > m_maxSize) {
            m_queue.dequeue();
        }
    }
    
    /**
     * @brief Get the maximum queue size
     * @return Maximum size (0 = unlimited)
     */
    int maxSize() const
    {
        QMutexLocker locker(&m_mutex);
        return m_maxSize;
    }

private:
    mutable QMutex m_mutex;
    QWaitCondition m_condition;
    QQueue<T> m_queue;
    int m_maxSize = 0; // 0 = unlimited
};

#endif // THREADSAFEQUEUE_H
