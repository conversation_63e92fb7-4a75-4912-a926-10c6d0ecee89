#include "AuthManager.h"
#include <QSettings>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

// Settings keys
const QString AuthManager::SETTINGS_TOKEN_KEY = "auth/token";
const QString AuthManager::SETTINGS_USER_ID_KEY = "auth/user_id";
const QString AuthManager::SETTINGS_USERNAME_KEY = "auth/username";
const QString AuthManager::SETTINGS_USER_ROLE_KEY = "auth/user_role";
const QString AuthManager::SETTINGS_TOKEN_EXPIRY_KEY = "auth/token_expiry";

AuthManager::AuthManager(QObject *parent)
    : QObject(parent)
    , m_refreshTimer(new QTimer(this))
    , m_expiryCheckTimer(new QTimer(this))
{
    setupTimers();
    loadTokenFromSettings();
}

AuthManager::~AuthManager()
{
    stopTimers();
}

bool AuthManager::isTokenExpired() const
{
    if (m_tokenExpiry.isNull()) {
        return true;
    }
    return QDateTime::currentDateTime() >= m_tokenExpiry;
}

QString AuthManager::authorizationHeader() const
{
    if (m_token.isEmpty()) {
        return QString();
    }
    return QString("Bearer %1").arg(m_token);
}

void AuthManager::login(const QString& username, const QString& password)
{
    // This method should be called by ApiClient
    // For now, we just emit a signal that login was requested
    // The actual HTTP request will be handled by ApiClient
    Q_UNUSED(username)
    Q_UNUSED(password)
    
    qDebug() << "AuthManager::login() - This should be implemented by ApiClient";
}

void AuthManager::logout()
{
    clearSession();
    clearStoredToken();
    emit loggedOut();
}

void AuthManager::refreshToken()
{
    if (m_token.isEmpty()) {
        emit authenticationRequired();
        return;
    }
    
    // This should trigger an API call to refresh the token
    // For now, we just extend the current token expiry
    m_tokenExpiry = calculateTokenExpiry();
    saveTokenToSettings();
    emit tokenRefreshed();
}

void AuthManager::validateSession()
{
    bool isValid = isAuthenticated();
    
    if (!isValid && hasValidToken()) {
        // Token exists but might be expired, try to refresh
        refreshToken();
        isValid = isAuthenticated();
    }
    
    emit sessionValidated(isValid);
    
    if (!isValid) {
        emit authenticationRequired();
    }
}

void AuthManager::saveTokenToSettings()
{
    QSettings settings;
    settings.setValue(SETTINGS_TOKEN_KEY, m_token);
    settings.setValue(SETTINGS_USER_ID_KEY, m_userInfo.id);
    settings.setValue(SETTINGS_USERNAME_KEY, m_userInfo.username);
    settings.setValue(SETTINGS_USER_ROLE_KEY, m_userInfo.role);
    settings.setValue(SETTINGS_TOKEN_EXPIRY_KEY, m_tokenExpiry);
}

void AuthManager::loadTokenFromSettings()
{
    QSettings settings;
    
    m_token = settings.value(SETTINGS_TOKEN_KEY).toString();
    m_userInfo.id = settings.value(SETTINGS_USER_ID_KEY).toInt();
    m_userInfo.username = settings.value(SETTINGS_USERNAME_KEY).toString();
    m_userInfo.role = settings.value(SETTINGS_USER_ROLE_KEY).toString();
    m_tokenExpiry = settings.value(SETTINGS_TOKEN_EXPIRY_KEY).toDateTime();
    
    if (!m_token.isEmpty() && m_userInfo.isValid()) {
        if (isTokenExpired()) {
            qDebug() << "Loaded token is expired, clearing session";
            clearSession();
        } else {
            qDebug() << "Loaded valid session for user:" << m_userInfo.username;
            startRefreshTimer();
        }
    }
}

void AuthManager::clearStoredToken()
{
    QSettings settings;
    settings.remove(SETTINGS_TOKEN_KEY);
    settings.remove(SETTINGS_USER_ID_KEY);
    settings.remove(SETTINGS_USERNAME_KEY);
    settings.remove(SETTINGS_USER_ROLE_KEY);
    settings.remove(SETTINGS_TOKEN_EXPIRY_KEY);
}

void AuthManager::onTokenRefreshTimer()
{
    qDebug() << "Token refresh timer triggered";
    refreshToken();
}

void AuthManager::checkTokenExpiry()
{
    if (hasValidToken() && isTokenExpired()) {
        qDebug() << "Token expired, clearing session";
        clearSession();
        emit tokenExpired();
        emit authenticationRequired();
    }
}

void AuthManager::setToken(const QString& token)
{
    m_token = token;
    m_tokenExpiry = calculateTokenExpiry();
    
    if (!token.isEmpty()) {
        startRefreshTimer();
        saveTokenToSettings();
    } else {
        stopTimers();
    }
}

void AuthManager::setUserInfo(const QJsonObject& userJson)
{
    m_userInfo.id = userJson.value("id").toInt();
    m_userInfo.username = userJson.value("username").toString();
    m_userInfo.role = userJson.value("role").toString();
}

void AuthManager::setupTimers()
{
    // Setup token refresh timer
    m_refreshTimer->setSingleShot(false);
    m_refreshTimer->setInterval(TOKEN_REFRESH_INTERVAL_MINUTES * 60 * 1000);
    connect(m_refreshTimer, &QTimer::timeout, this, &AuthManager::onTokenRefreshTimer);
    
    // Setup token expiry check timer
    m_expiryCheckTimer->setSingleShot(false);
    m_expiryCheckTimer->setInterval(TOKEN_EXPIRY_CHECK_INTERVAL_SECONDS * 1000);
    connect(m_expiryCheckTimer, &QTimer::timeout, this, &AuthManager::checkTokenExpiry);
    m_expiryCheckTimer->start();
}

void AuthManager::startRefreshTimer()
{
    if (!m_refreshTimer->isActive()) {
        m_refreshTimer->start();
    }
}

void AuthManager::stopTimers()
{
    m_refreshTimer->stop();
    // Keep expiry check timer running
}

void AuthManager::clearSession()
{
    m_token.clear();
    m_userInfo = UserInfo{};
    m_tokenExpiry = QDateTime();
    stopTimers();
}

QDateTime AuthManager::calculateTokenExpiry() const
{
    return QDateTime::currentDateTime().addSecs(TOKEN_VALIDITY_HOURS * 3600);
}
