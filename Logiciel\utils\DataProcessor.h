#ifndef DATAPROCESSOR_H
#define DATAPROCESSOR_H

#include <QObject>
#include <QMutex>
#include <QLoggingCategory>
#include <memory>

#include "models/TestData.h"
#include "models/TestConfiguration.h"
#include "HydraulicCalculations.h"

Q_DECLARE_LOGGING_CATEGORY(dataProcessorLog)

/**
 * @brief Core data processing engine for hydraulic test bench system
 * 
 * This class provides thread-safe data processing capabilities including
 * filtering, validation, calculation of derived values, and real-time
 * analysis of hydraulic test data.
 */
class DataProcessor : public QObject
{
    Q_OBJECT
    
public:
    /**
     * @brief Data processing configuration
     */
    struct ProcessingConfig {
        bool enableFiltering = true;        ///< Enable data filtering
        bool enableValidation = true;       ///< Enable data validation
        bool enableCalculations = true;     ///< Enable derived calculations
        bool enableStatistics = true;       ///< Enable statistical analysis
        double filterCutoff = 10.0;         ///< Low-pass filter cutoff (Hz)
        int filterOrder = 2;                ///< Filter order
        double validationTolerance = 0.05;  ///< Validation tolerance (5%)
        int statisticsWindow = 100;         ///< Statistics calculation window
        bool enableRealTimeProcessing = true; ///< Enable real-time processing
    };
    
    /**
     * @brief Data quality metrics
     */
    struct QualityMetrics {
        double signalToNoiseRatio = 0.0;    ///< Signal-to-noise ratio
        double dataCompleteness = 0.0;      ///< Percentage of valid data points
        int outlierCount = 0;               ///< Number of outliers detected
        int validationErrors = 0;           ///< Number of validation errors
        double averageUpdateRate = 0.0;     ///< Average data update rate (Hz)
        QDateTime lastUpdate;               ///< Last processing timestamp
    };
    
    explicit DataProcessor(QObject *parent = nullptr);
    virtual ~DataProcessor() = default;
    
    // Configuration
    void setProcessingConfig(const ProcessingConfig& config);
    ProcessingConfig processingConfig() const;
    
    void setTestConfiguration(std::shared_ptr<TestConfiguration> config);
    std::shared_ptr<TestConfiguration> testConfiguration() const;
    
    void setHydraulicParameters(const HydraulicSystemParameters& params);
    HydraulicSystemParameters hydraulicParameters() const;
    
    // Main processing methods
    TestData processRawData(const TestData& rawData);
    bool processRawDataInPlace(TestData& data);
    QList<TestData> processBatch(const QList<TestData>& rawDataList);
    
    // Individual processing steps
    bool applyFilters(TestData& data);
    bool validateDataIntegrity(const TestData& data, QStringList& errors);
    bool calculateDerivedValues(TestData& data);
    bool updateStatistics(const TestData& data);
    
    // Data validation
    bool isDataValid(const TestData& data) const;
    QStringList validateSensorReading(const QString& sensor, double value) const;
    bool checkSafetyLimits(const TestData& data, QStringList& violations) const;
    bool detectOutliers(const TestData& data, QStringList& outliers) const;
    
    // Filtering methods
    double applyLowPassFilter(double newValue, double previousValue, double cutoffFreq, double sampleRate);
    double applyMovingAverage(const QList<double>& values, int windowSize);
    double applyMedianFilter(const QList<double>& values, int windowSize);
    QList<double> applySavitzkyGolayFilter(const QList<double>& values, int windowSize, int polynomialOrder);
    
    // Statistical analysis
    void resetStatistics();
    QualityMetrics getQualityMetrics() const;
    double calculateSignalToNoiseRatio(const QList<double>& signal) const;
    QPair<double, double> calculateConfidenceInterval(const QList<double>& values, double confidence = 0.95) const;
    
    // Real-time processing
    void enableRealTimeProcessing(bool enabled);
    bool isRealTimeProcessingEnabled() const;
    void setProcessingRate(int rateHz);
    int processingRate() const;
    
    // Thread safety
    void lockProcessor();
    void unlockProcessor();
    bool tryLockProcessor(int timeoutMs = 1000);
    
    // Performance monitoring
    int getProcessedDataCount() const;
    double getAverageProcessingTime() const; // in milliseconds
    double getProcessingThroughput() const; // data points per second
    void resetPerformanceCounters();
    
signals:
    void dataProcessed(const TestData& processedData);
    void processingError(const QString& error);
    void validationFailed(const TestData& data, const QStringList& errors);
    void safetyViolation(const TestData& data, const QStringList& violations);
    void outlierDetected(const TestData& data, const QStringList& outliers);
    void qualityMetricsUpdated(const QualityMetrics& metrics);
    void configurationChanged();
    
private slots:
    void onTestConfigurationChanged();
    
private:
    mutable QMutex m_mutex;
    ProcessingConfig m_config;
    std::shared_ptr<TestConfiguration> m_testConfig;
    HydraulicSystemParameters m_hydraulicParams;
    std::unique_ptr<HydraulicTestAnalyzer> m_analyzer;
    
    // Filter state variables
    QMap<QString, double> m_filterStates;
    QMap<QString, QList<double>> m_filterBuffers;
    
    // Statistics
    QualityMetrics m_qualityMetrics;
    QList<TestData> m_recentData;
    QMap<QString, QList<double>> m_statisticsBuffers;
    
    // Performance monitoring
    int m_processedDataCount;
    QList<double> m_processingTimes;
    QDateTime m_lastProcessingTime;
    
    // Processing methods
    bool initializeFilters();
    void updateFilterBuffers(const TestData& data);
    void updateQualityMetrics(const TestData& data);
    void updatePerformanceMetrics(double processingTimeMs);
    
    // Validation helpers
    bool isValueInRange(double value, double min, double max) const;
    bool isRateOfChangeValid(double currentValue, double previousValue, double maxRate, double deltaTime) const;
    bool isSensorCorrelationValid(const TestData& data) const;
    
    // Filter implementations
    double butterworth2ndOrder(double input, double& x1, double& x2, double& y1, double& y2, double cutoff, double sampleRate);
    void calculateFilterCoefficients(double cutoff, double sampleRate, double& a1, double& a2, double& b0, double& b1, double& b2);
    
    // Statistical calculations
    double calculateMean(const QList<double>& values) const;
    double calculateStandardDeviation(const QList<double>& values) const;
    double calculateVariance(const QList<double>& values) const;
    double calculateMedian(QList<double> values) const;
    QPair<double, double> calculateMinMax(const QList<double>& values) const;
    
    // Constants
    static const int MAX_FILTER_BUFFER_SIZE = 1000;
    static const int MAX_STATISTICS_BUFFER_SIZE = 10000;
    static constexpr double DEFAULT_SAMPLE_RATE = 100.0; // Hz
    static constexpr double OUTLIER_THRESHOLD = 3.0; // Standard deviations
    static const int MIN_STATISTICS_SAMPLES = 10;
};

#endif // DATAPROCESSOR_H
