#include "ApiRequestThread.h"
#include <QDebug>
#include <QMutexLocker>
#include <QElapsedTimer>
#include <algorithm>

Q_LOGGING_CATEGORY(apiRequestThreadLog, "hydraulic.utils.apirequest")

ApiRequestThread::ApiRequestThread(QObject *parent)
    : QThread(parent)
    , m_isProcessing(false)
    , m_isPaused(false)
    , m_stopRequested(false)
    , m_nextRequestId(1)
    , m_maxConcurrentRequests(5)
    , m_defaultTimeoutMs(30000)
    , m_defaultMaxRetries(3)
    , m_totalRequestsProcessed(0)
    , m_successfulRequests(0)
    , m_failedRequests(0)
{
    qCDebug(apiRequestThreadLog) << "ApiRequestThread created";
}

ApiRequestThread::~ApiRequestThread()
{
    stopProcessing();
    wait(); // Wait for thread to finish
}

void ApiRequestThread::startProcessing()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isProcessing) {
        return; // Already processing
    }
    
    if (!m_apiClient) {
        qCWarning(apiRequestThreadLog) << "Cannot start processing: no API client set";
        return;
    }
    
    m_isProcessing = true;
    m_isPaused = false;
    m_stopRequested = false;
    
    start();
    
    emit processingStarted();
    qCDebug(apiRequestThreadLog) << "API request processing started";
}

void ApiRequestThread::stopProcessing()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isProcessing) {
        return; // Not processing
    }
    
    m_stopRequested = true;
    m_requestCondition.wakeAll();
    m_pauseCondition.wakeAll();
    
    locker.unlock();
    
    // Wait for thread to finish
    if (!wait(5000)) { // 5 second timeout
        terminate(); // Force termination if needed
        wait(1000);
    }
    
    locker.relock();
    m_isProcessing = false;
    m_isPaused = false;
    
    emit processingStopped();
    qCDebug(apiRequestThreadLog) << "API request processing stopped";
}

void ApiRequestThread::pauseProcessing()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isProcessing || m_isPaused) {
        return;
    }
    
    m_isPaused = true;
    emit processingPaused();
    qCDebug(apiRequestThreadLog) << "API request processing paused";
}

void ApiRequestThread::resumeProcessing()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isProcessing || !m_isPaused) {
        return;
    }
    
    m_isPaused = false;
    m_pauseCondition.wakeAll();
    emit processingResumed();
    qCDebug(apiRequestThreadLog) << "API request processing resumed";
}

void ApiRequestThread::setApiClient(std::shared_ptr<ApiClient> client)
{
    QMutexLocker locker(&m_mutex);
    m_apiClient = client;
    
    qCDebug(apiRequestThreadLog) << "API client set";
}

std::shared_ptr<ApiClient> ApiRequestThread::apiClient() const
{
    QMutexLocker locker(&m_mutex);
    return m_apiClient;
}

int ApiRequestThread::queueRequest(const QString& method, const QString& endpoint, 
                                  const QJsonObject& data, int priority, const QString& description)
{
    QMutexLocker locker(&m_mutex);
    
    ApiRequest request = createRequest(method, endpoint, data, priority, description);
    m_pendingRequests.enqueue(request);
    updateRequestStatus(request.requestId, RequestStatus::Pending);
    
    // Sort by priority
    sortRequestsByPriority();
    
    // Wake up processing thread
    m_requestCondition.wakeOne();
    
    emit requestQueued(request.requestId, description);
    qCDebug(apiRequestThreadLog) << "Request queued:" << request.requestId << method << endpoint;
    
    return request.requestId;
}

int ApiRequestThread::queueGetRequest(const QString& endpoint, int priority)
{
    return queueRequest("GET", endpoint, QJsonObject(), priority, QString("GET %1").arg(endpoint));
}

int ApiRequestThread::queuePostRequest(const QString& endpoint, const QJsonObject& data, int priority)
{
    return queueRequest("POST", endpoint, data, priority, QString("POST %1").arg(endpoint));
}

int ApiRequestThread::queuePutRequest(const QString& endpoint, const QJsonObject& data, int priority)
{
    return queueRequest("PUT", endpoint, data, priority, QString("PUT %1").arg(endpoint));
}

int ApiRequestThread::queueDeleteRequest(const QString& endpoint, int priority)
{
    return queueRequest("DELETE", endpoint, QJsonObject(), priority, QString("DELETE %1").arg(endpoint));
}

bool ApiRequestThread::cancelRequest(int requestId)
{
    QMutexLocker locker(&m_mutex);
    
    // Check if request is pending
    for (int i = 0; i < m_pendingRequests.size(); ++i) {
        if (m_pendingRequests[i].requestId == requestId) {
            m_pendingRequests.removeAt(i);
            updateRequestStatus(requestId, RequestStatus::Cancelled);
            emit requestCancelled(requestId);
            qCDebug(apiRequestThreadLog) << "Request cancelled:" << requestId;
            return true;
        }
    }
    
    // Check if request is processing (cannot cancel)
    if (m_processingRequests.contains(requestId)) {
        qCWarning(apiRequestThreadLog) << "Cannot cancel request in progress:" << requestId;
        return false;
    }
    
    return false;
}

void ApiRequestThread::cancelAllRequests()
{
    QMutexLocker locker(&m_mutex);
    
    // Cancel all pending requests
    while (!m_pendingRequests.isEmpty()) {
        ApiRequest request = m_pendingRequests.dequeue();
        updateRequestStatus(request.requestId, RequestStatus::Cancelled);
        emit requestCancelled(request.requestId);
    }
    
    qCDebug(apiRequestThreadLog) << "All pending requests cancelled";
}

void ApiRequestThread::clearCompletedRequests()
{
    QMutexLocker locker(&m_mutex);
    
    // Remove completed requests
    auto it = m_completedRequests.begin();
    while (it != m_completedRequests.end()) {
        RequestStatus status = m_requestStatuses.value(it.key());
        if (status == RequestStatus::Completed || status == RequestStatus::Failed || 
            status == RequestStatus::Cancelled || status == RequestStatus::Timeout) {
            m_requestStatuses.remove(it.key());
            it = m_completedRequests.erase(it);
        } else {
            ++it;
        }
    }
    
    qCDebug(apiRequestThreadLog) << "Completed requests cleared";
}

ApiRequestThread::RequestStatus ApiRequestThread::getRequestStatus(int requestId) const
{
    QMutexLocker locker(&m_mutex);
    return m_requestStatuses.value(requestId, RequestStatus::Pending);
}

ApiRequestThread::ApiRequest ApiRequestThread::getRequest(int requestId) const
{
    QMutexLocker locker(&m_mutex);
    
    // Check pending requests
    for (const ApiRequest& request : m_pendingRequests) {
        if (request.requestId == requestId) {
            return request;
        }
    }
    
    // Check processing requests
    if (m_processingRequests.contains(requestId)) {
        return m_processingRequests.value(requestId);
    }
    
    // Check completed requests
    if (m_completedRequests.contains(requestId)) {
        return m_completedRequests.value(requestId);
    }
    
    return ApiRequest(); // Return empty request if not found
}

QList<ApiRequestThread::ApiRequest> ApiRequestThread::getPendingRequests() const
{
    QMutexLocker locker(&m_mutex);
    QList<ApiRequest> requests;
    for (const ApiRequest& request : m_pendingRequests) {
        requests.append(request);
    }
    return requests;
}

int ApiRequestThread::pendingRequestCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_pendingRequests.size();
}

void ApiRequestThread::run()
{
    qCDebug(apiRequestThreadLog) << "API request thread started";
    
    while (!m_stopRequested) {
        // Check if paused
        {
            QMutexLocker locker(&m_mutex);
            while (m_isPaused && !m_stopRequested) {
                m_pauseCondition.wait(&m_mutex);
            }
        }
        
        if (m_stopRequested) {
            break;
        }
        
        // Process next request
        processNextRequest();
        
        // Wait for next request if queue is empty
        {
            QMutexLocker locker(&m_mutex);
            if (m_pendingRequests.isEmpty() && !m_stopRequested) {
                emit queueEmpty();
                m_requestCondition.wait(&m_mutex, 1000); // Wait up to 1 second
            }
        }
    }
    
    qCDebug(apiRequestThreadLog) << "API request thread finished";
}

void ApiRequestThread::processNextRequest()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_pendingRequests.isEmpty() || !m_apiClient) {
        return;
    }
    
    // Check if we've reached the concurrent request limit
    if (m_processingRequests.size() >= m_maxConcurrentRequests) {
        return;
    }
    
    // Get next request
    ApiRequest request = m_pendingRequests.dequeue();
    m_processingRequests[request.requestId] = request;
    updateRequestStatus(request.requestId, RequestStatus::Processing);
    
    locker.unlock();
    
    emit requestStarted(request.requestId);
    
    // Execute request
    executeRequest(request);
}

void ApiRequestThread::executeRequest(const ApiRequest& request)
{
    QElapsedTimer timer;
    timer.start();

    try {
        // Store request for completion handling
        m_activeRequests[request.requestId] = request;
        m_requestTimers[request.requestId] = timer;

        // Connect to API client signals for this request
        connect(m_apiClient.get(), &ApiClient::responseReceived,
                this, &ApiRequestThread::onApiResponse, Qt::UniqueConnection);
        connect(m_apiClient.get(), &ApiClient::errorOccurred,
                this, &ApiRequestThread::onApiError, Qt::UniqueConnection);

        // Execute the appropriate API method
        if (request.method == "GET") {
            m_apiClient->get(request.endpoint);
        } else if (request.method == "POST") {
            m_apiClient->post(request.endpoint, request.data);
        } else if (request.method == "PUT") {
            m_apiClient->put(request.endpoint, request.data);
        } else if (request.method == "DELETE") {
            m_apiClient->deleteResource(request.endpoint);
        } else {
            throw std::runtime_error(QString("Unsupported HTTP method: %1").arg(request.method).toStdString());
        }

    } catch (const std::exception& e) {
        double requestTime = timer.nsecsElapsed() / 1000000.0;
        QString error = QString("Request execution error: %1").arg(e.what());

        handleRequestCompletion(request.requestId, false, error);
        updateStatistics(requestTime, false);
        emit requestFailed(request.requestId, error);

        qCWarning(apiRequestThreadLog) << "Request execution failed:" << request.requestId << error;
    }
}

void ApiRequestThread::handleRequestCompletion(int requestId, bool success, const QString& error)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_processingRequests.contains(requestId)) {
        return; // Request not found
    }
    
    ApiRequest request = m_processingRequests.take(requestId);
    m_completedRequests[requestId] = request;
    
    if (success) {
        updateRequestStatus(requestId, RequestStatus::Completed);
        m_successfulRequests++;
    } else {
        updateRequestStatus(requestId, RequestStatus::Failed);
        m_failedRequests++;
    }
    
    m_totalRequestsProcessed++;
    
    // Cleanup old requests periodically
    if (m_completedRequests.size() > MAX_COMPLETED_REQUESTS) {
        cleanupOldRequests();
    }
    
    emit statisticsUpdated();
}

void ApiRequestThread::retryRequest(const ApiRequest& request)
{
    QMutexLocker locker(&m_mutex);
    
    // Add request back to the front of the queue for immediate retry
    m_pendingRequests.prepend(request);
    updateRequestStatus(request.requestId, RequestStatus::Pending);
    
    qCDebug(apiRequestThreadLog) << "Retrying request:" << request.requestId << "attempt" << request.retryCount;
}

ApiRequestThread::ApiRequest ApiRequestThread::createRequest(const QString& method, const QString& endpoint, 
                                                           const QJsonObject& data, int priority, const QString& description)
{
    ApiRequest request;
    request.requestId = m_nextRequestId++;
    request.method = method.toUpper();
    request.endpoint = endpoint;
    request.data = data;
    request.priority = priority;
    request.timeoutMs = m_defaultTimeoutMs;
    request.retryCount = 0;
    request.maxRetries = m_defaultMaxRetries;
    request.description = description.isEmpty() ? QString("%1 %2").arg(method, endpoint) : description;
    request.createdAt = QDateTime::currentDateTime();
    
    return request;
}

void ApiRequestThread::updateRequestStatus(int requestId, RequestStatus status)
{
    m_requestStatuses[requestId] = status;
}

void ApiRequestThread::sortRequestsByPriority()
{
    // Convert queue to list, sort, and convert back
    QList<ApiRequest> requests;
    while (!m_pendingRequests.isEmpty()) {
        requests.append(m_pendingRequests.dequeue());
    }
    
    std::sort(requests.begin(), requests.end(), 
              [](const ApiRequest& a, const ApiRequest& b) {
                  return a.priority > b.priority; // Higher priority first
              });
    
    for (const ApiRequest& request : requests) {
        m_pendingRequests.enqueue(request);
    }
}

void ApiRequestThread::updateStatistics(double requestTimeMs, bool success)
{
    QMutexLocker locker(&m_mutex);
    
    m_requestTimes.append(requestTimeMs);
    
    // Limit request time history
    while (m_requestTimes.size() > MAX_REQUEST_TIME_HISTORY) {
        m_requestTimes.removeFirst();
    }
}

void ApiRequestThread::cleanupOldRequests()
{
    // Remove oldest completed requests
    QDateTime cutoffTime = QDateTime::currentDateTime().addSecs(-3600); // 1 hour ago

    auto it = m_completedRequests.begin();
    while (it != m_completedRequests.end()) {
        if (it.value().createdAt < cutoffTime) {
            m_requestStatuses.remove(it.key());
            it = m_completedRequests.erase(it);
        } else {
            ++it;
        }
    }
}

void ApiRequestThread::onApiResponse(const QString& endpoint, const JsonResponse& response)
{
    // Find the request that corresponds to this response by endpoint
    QMutexLocker locker(&m_mutex);

    // Find matching request by endpoint
    int matchingRequestId = -1;
    for (auto it = m_activeRequests.begin(); it != m_activeRequests.end(); ++it) {
        if (it.value().endpoint == endpoint) {
            matchingRequestId = it.key();
            break;
        }
    }

    if (matchingRequestId != -1) {
        ApiRequest request = m_activeRequests.take(matchingRequestId);
        QElapsedTimer timer = m_requestTimers.take(matchingRequestId);

        double requestTime = timer.nsecsElapsed() / 1000000.0; // Convert to milliseconds

        handleRequestCompletion(matchingRequestId, true);
        updateStatistics(requestTime, true);

        // Convert JsonResponse to ApiResponse<QJsonObject> for signal compatibility
        ApiResponse<QJsonObject> apiResponse(response.data());
        emit requestCompleted(matchingRequestId, apiResponse);
    }
}

void ApiRequestThread::onApiError(const QString& endpoint, const NetworkError& error)
{
    QMutexLocker locker(&m_mutex);

    // Find matching request by endpoint
    int matchingRequestId = -1;
    for (auto it = m_activeRequests.begin(); it != m_activeRequests.end(); ++it) {
        if (it.value().endpoint == endpoint) {
            matchingRequestId = it.key();
            break;
        }
    }

    if (matchingRequestId != -1) {
        ApiRequest request = m_activeRequests.take(matchingRequestId);
        QElapsedTimer timer = m_requestTimers.take(matchingRequestId);

        double requestTime = timer.nsecsElapsed() / 1000000.0;

        handleRequestCompletion(matchingRequestId, false, error.message());
        updateStatistics(requestTime, false);

        // Retry if configured
        if (request.retryCount < request.maxRetries) {
            ApiRequest retryRequest = request;
            retryRequest.retryCount++;
            this->retryRequest(retryRequest);
        } else {
            emit requestFailed(matchingRequestId, error.message());
        }
    }
}

int ApiRequestThread::totalRequestsProcessed() const
{
    QMutexLocker locker(&m_mutex);
    return m_totalRequestsProcessed;
}

int ApiRequestThread::successfulRequests() const
{
    QMutexLocker locker(&m_mutex);
    return m_successfulRequests;
}

int ApiRequestThread::failedRequests() const
{
    QMutexLocker locker(&m_mutex);
    return m_failedRequests;
}

double ApiRequestThread::averageRequestTime() const
{
    QMutexLocker locker(&m_mutex);
    
    if (m_requestTimes.isEmpty()) {
        return 0.0;
    }
    
    double sum = 0.0;
    for (double time : m_requestTimes) {
        sum += time;
    }
    
    return sum / m_requestTimes.size();
}

void ApiRequestThread::resetStatistics()
{
    QMutexLocker locker(&m_mutex);
    
    m_totalRequestsProcessed = 0;
    m_successfulRequests = 0;
    m_failedRequests = 0;
    m_requestTimes.clear();
    
    emit statisticsUpdated();
    qCDebug(apiRequestThreadLog) << "Statistics reset";
}
