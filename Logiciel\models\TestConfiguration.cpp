#include "TestConfiguration.h"
#include <QJsonDocument>
#include <QFile>
#include <QDebug>

Q_LOGGING_CATEGORY(testConfigLog, "hydraulic.models.testconfig")

// Static constants
const double TestConfiguration::DEFAULT_PRESSURE_MIN = 0.0;
const double TestConfiguration::DEFAULT_PRESSURE_MAX = 200.0;
const double TestConfiguration::DEFAULT_FLOW_MIN = 0.0;
const double TestConfiguration::DEFAULT_FLOW_MAX = 100.0;
const double TestConfiguration::DEFAULT_TEMPERATURE_MAX = 80.0;
const int TestConfiguration::DEFAULT_ACQUISITION_RATE = 100;
const int TestConfiguration::DEFAULT_TEST_DURATION = 60000; // 1 minute
const int TestConfiguration::MIN_ACQUISITION_RATE = 1;
const int TestConfiguration::MAX_ACQUISITION_RATE = 10000;
const int TestConfiguration::MIN_TEST_DURATION = 1000; // 1 second
const int TestConfiguration::MAX_TEST_DURATION = 3600000; // 1 hour

TestConfiguration::TestConfiguration(QObject *parent)
    : QObject(parent)
    , m_name("Default Configuration")
    , m_description("Default hydraulic test configuration")
    , m_pressureMin(DEFAULT_PRESSURE_MIN)
    , m_pressureMax(DEFAULT_PRESSURE_MAX)
    , m_flowMin(DEFAULT_FLOW_MIN)
    , m_flowMax(DEFAULT_FLOW_MAX)
    , m_temperatureMax(DEFAULT_TEMPERATURE_MAX)
    , m_acquisitionRate(DEFAULT_ACQUISITION_RATE)
    , m_testDuration(DEFAULT_TEST_DURATION)
{
    connectSignals();
}

TestConfiguration::TestConfiguration(const TestConfiguration& other)
    : QObject(other.parent())
    , m_name(other.m_name)
    , m_description(other.m_description)
    , m_pressureMin(other.m_pressureMin)
    , m_pressureMax(other.m_pressureMax)
    , m_flowMin(other.m_flowMin)
    , m_flowMax(other.m_flowMax)
    , m_temperatureMax(other.m_temperatureMax)
    , m_acquisitionRate(other.m_acquisitionRate)
    , m_testDuration(other.m_testDuration)
    , m_safetyThresholds(other.m_safetyThresholds)
    , m_acquisitionConfig(other.m_acquisitionConfig)
    , m_executionConfig(other.m_executionConfig)
{
    connectSignals();
}

TestConfiguration& TestConfiguration::operator=(const TestConfiguration& other)
{
    if (this != &other) {
        m_name = other.m_name;
        m_description = other.m_description;
        m_pressureMin = other.m_pressureMin;
        m_pressureMax = other.m_pressureMax;
        m_flowMin = other.m_flowMin;
        m_flowMax = other.m_flowMax;
        m_temperatureMax = other.m_temperatureMax;
        m_acquisitionRate = other.m_acquisitionRate;
        m_testDuration = other.m_testDuration;
        m_safetyThresholds = other.m_safetyThresholds;
        m_acquisitionConfig = other.m_acquisitionConfig;
        m_executionConfig = other.m_executionConfig;
        
        emit configurationChanged();
        validateAndEmitSignals();
    }
    return *this;
}

void TestConfiguration::setName(const QString& name)
{
    if (m_name != name) {
        m_name = name;
        emit nameChanged(name);
        emit configurationChanged();
    }
}

void TestConfiguration::setDescription(const QString& description)
{
    if (m_description != description) {
        m_description = description;
        emit descriptionChanged(description);
        emit configurationChanged();
    }
}

void TestConfiguration::setPressureMin(double pressureMin)
{
    if (m_pressureMin != pressureMin) {
        m_pressureMin = pressureMin;
        emit pressureMinChanged(pressureMin);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setPressureMax(double pressureMax)
{
    if (m_pressureMax != pressureMax) {
        m_pressureMax = pressureMax;
        emit pressureMaxChanged(pressureMax);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setPressureRange(double min, double max)
{
    setPressureMin(min);
    setPressureMax(max);
}

QPair<double, double> TestConfiguration::pressureRange() const
{
    return QPair<double, double>(m_pressureMin, m_pressureMax);
}

void TestConfiguration::setFlowMin(double flowMin)
{
    if (m_flowMin != flowMin) {
        m_flowMin = flowMin;
        emit flowMinChanged(flowMin);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setFlowMax(double flowMax)
{
    if (m_flowMax != flowMax) {
        m_flowMax = flowMax;
        emit flowMaxChanged(flowMax);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setFlowRange(double min, double max)
{
    setFlowMin(min);
    setFlowMax(max);
}

QPair<double, double> TestConfiguration::flowRange() const
{
    return QPair<double, double>(m_flowMin, m_flowMax);
}

void TestConfiguration::setTemperatureMax(double temperatureMax)
{
    if (m_temperatureMax != temperatureMax) {
        m_temperatureMax = temperatureMax;
        emit temperatureMaxChanged(temperatureMax);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setAcquisitionRate(int acquisitionRate)
{
    if (m_acquisitionRate != acquisitionRate) {
        m_acquisitionRate = acquisitionRate;
        emit acquisitionRateChanged(acquisitionRate);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setTestDuration(int testDuration)
{
    if (m_testDuration != testDuration) {
        m_testDuration = testDuration;
        emit testDurationChanged(testDuration);
        emit configurationChanged();
        validateAndEmitSignals();
    }
}

void TestConfiguration::setSafetyThresholds(const SafetyThresholds& thresholds)
{
    m_safetyThresholds = thresholds;
    emit safetyThresholdsChanged();
    emit configurationChanged();
    validateAndEmitSignals();
}

void TestConfiguration::setAcquisitionConfig(const AcquisitionConfig& config)
{
    m_acquisitionConfig = config;
    emit acquisitionConfigChanged();
    emit configurationChanged();
}

void TestConfiguration::setExecutionConfig(const ExecutionConfig& config)
{
    m_executionConfig = config;
    emit executionConfigChanged();
    emit configurationChanged();
}

bool TestConfiguration::isValid() const
{
    return validate().isEmpty();
}

QStringList TestConfiguration::validate() const
{
    QStringList errors;
    
    if (m_name.isEmpty()) {
        errors << "Configuration name cannot be empty";
    }
    
    if (!isPressureRangeValid()) {
        errors << "Invalid pressure range: minimum must be less than maximum";
    }
    
    if (!isFlowRangeValid()) {
        errors << "Invalid flow range: minimum must be less than maximum";
    }
    
    if (!isAcquisitionRateValid()) {
        errors << QString("Acquisition rate must be between %1 and %2 Hz")
                     .arg(MIN_ACQUISITION_RATE).arg(MAX_ACQUISITION_RATE);
    }
    
    if (!isTestDurationValid()) {
        errors << QString("Test duration must be between %1 and %2 ms")
                     .arg(MIN_TEST_DURATION).arg(MAX_TEST_DURATION);
    }
    
    if (!areSafetyThresholdsValid()) {
        errors << "Invalid safety thresholds";
    }
    
    return errors;
}

bool TestConfiguration::isPressureRangeValid() const
{
    return m_pressureMin >= 0.0 && m_pressureMax > m_pressureMin && m_pressureMax <= 500.0;
}

bool TestConfiguration::isFlowRangeValid() const
{
    return m_flowMin >= -200.0 && m_flowMax > m_flowMin && m_flowMax <= 200.0;
}

bool TestConfiguration::isAcquisitionRateValid() const
{
    return m_acquisitionRate >= MIN_ACQUISITION_RATE && m_acquisitionRate <= MAX_ACQUISITION_RATE;
}

bool TestConfiguration::isTestDurationValid() const
{
    return m_testDuration >= MIN_TEST_DURATION && m_testDuration <= MAX_TEST_DURATION;
}

bool TestConfiguration::areSafetyThresholdsValid() const
{
    return m_safetyThresholds.maxPressure > 0.0 &&
           m_safetyThresholds.maxTemperature > 0.0 &&
           m_safetyThresholds.maxFlow > 0.0 &&
           m_safetyThresholds.maxVelocity > 0.0 &&
           m_safetyThresholds.maxForce > 0.0 &&
           m_safetyThresholds.leakageThreshold >= 0.0 &&
           m_safetyThresholds.emergencyStopDelay > 0;
}

bool TestConfiguration::isPressureSafe(double pressure) const
{
    return pressure >= m_safetyThresholds.minPressure && 
           pressure <= m_safetyThresholds.maxPressure;
}

bool TestConfiguration::isTemperatureSafe(double temperature) const
{
    return temperature <= m_safetyThresholds.maxTemperature;
}

bool TestConfiguration::isFlowSafe(double flow) const
{
    return qAbs(flow) <= m_safetyThresholds.maxFlow;
}

bool TestConfiguration::isVelocitySafe(double velocity) const
{
    return qAbs(velocity) <= m_safetyThresholds.maxVelocity;
}

bool TestConfiguration::isForceSafe(double force) const
{
    return qAbs(force) <= m_safetyThresholds.maxForce;
}

bool TestConfiguration::isWithinOperatingLimits(double pressure, double flow, double temperature) const
{
    return isPressureSafe(pressure) && isFlowSafe(flow) && isTemperatureSafe(temperature);
}

QJsonObject TestConfiguration::toJson() const
{
    QJsonObject json;
    json["name"] = m_name;
    json["description"] = m_description;
    json["pressure_min"] = m_pressureMin;
    json["pressure_max"] = m_pressureMax;
    json["flow_min"] = m_flowMin;
    json["flow_max"] = m_flowMax;
    json["temperature_max"] = m_temperatureMax;
    json["acquisition_rate"] = m_acquisitionRate;
    json["test_duration"] = m_testDuration;
    json["safety_thresholds"] = safetyThresholdsToJson();
    json["acquisition_config"] = acquisitionConfigToJson();
    json["execution_config"] = executionConfigToJson();
    json["display_name"] = displayName();
    json["estimated_test_time"] = estimatedTestTime();
    json["is_valid"] = isValid();
    return json;
}

void TestConfiguration::fromJson(const QJsonObject& json)
{
    setName(json["name"].toString());
    setDescription(json["description"].toString());
    setPressureMin(json["pressure_min"].toDouble());
    setPressureMax(json["pressure_max"].toDouble());
    setFlowMin(json["flow_min"].toDouble());
    setFlowMax(json["flow_max"].toDouble());
    setTemperatureMax(json["temperature_max"].toDouble());
    setAcquisitionRate(json["acquisition_rate"].toInt());
    setTestDuration(json["test_duration"].toInt());
    
    safetyThresholdsFromJson(json["safety_thresholds"].toObject());
    acquisitionConfigFromJson(json["acquisition_config"].toObject());
    executionConfigFromJson(json["execution_config"].toObject());
}

QJsonObject TestConfiguration::toJsonPartial() const
{
    QJsonObject json;
    if (!m_name.isEmpty()) json["name"] = m_name;
    if (!m_description.isEmpty()) json["description"] = m_description;
    json["pressure_min"] = m_pressureMin;
    json["pressure_max"] = m_pressureMax;
    json["flow_min"] = m_flowMin;
    json["flow_max"] = m_flowMax;
    json["temperature_max"] = m_temperatureMax;
    json["acquisition_rate"] = m_acquisitionRate;
    json["test_duration"] = m_testDuration;
    return json;
}

bool TestConfiguration::saveToFile(const QString& filename) const
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        qCWarning(testConfigLog) << "Cannot open file for writing:" << filename;
        return false;
    }
    
    QJsonDocument doc(toJson());
    file.write(doc.toJson());
    return true;
}

bool TestConfiguration::loadFromFile(const QString& filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        qCWarning(testConfigLog) << "Cannot open file for reading:" << filename;
        return false;
    }
    
    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    
    if (doc.isNull()) {
        qCWarning(testConfigLog) << "Invalid JSON in file:" << filename;
        return false;
    }
    
    fromJson(doc.object());
    return true;
}

QString TestConfiguration::displayName() const
{
    return m_name.isEmpty() ? "Unnamed Configuration" : m_name;
}

QString TestConfiguration::summary() const
{
    return QString("Pressure: %1-%2 bar, Flow: %3-%4 L/min, Duration: %5 min")
               .arg(m_pressureMin).arg(m_pressureMax)
               .arg(m_flowMin).arg(m_flowMax)
               .arg(estimatedTestTime());
}

double TestConfiguration::estimatedTestTime() const
{
    return m_testDuration / 60000.0; // Convert ms to minutes
}

bool TestConfiguration::operator==(const TestConfiguration& other) const
{
    return m_name == other.m_name &&
           m_pressureMin == other.m_pressureMin &&
           m_pressureMax == other.m_pressureMax &&
           m_flowMin == other.m_flowMin &&
           m_flowMax == other.m_flowMax &&
           m_acquisitionRate == other.m_acquisitionRate &&
           m_testDuration == other.m_testDuration;
}

TestConfiguration TestConfiguration::createDefault()
{
    return TestConfiguration();
}

TestConfiguration TestConfiguration::createPressureTest()
{
    TestConfiguration config;
    config.setName("Pressure Test Configuration");
    config.setDescription("Configuration for hydraulic pressure testing");
    config.setPressureRange(0.0, 250.0);
    config.setFlowRange(0.0, 50.0);
    config.setTestDuration(30000); // 30 seconds
    return config;
}

TestConfiguration TestConfiguration::createFlowTest()
{
    TestConfiguration config;
    config.setName("Flow Test Configuration");
    config.setDescription("Configuration for hydraulic flow testing");
    config.setPressureRange(50.0, 200.0);
    config.setFlowRange(0.0, 100.0);
    config.setTestDuration(60000); // 1 minute
    return config;
}

TestConfiguration TestConfiguration::createLeakageTest()
{
    TestConfiguration config;
    config.setName("Leakage Test Configuration");
    config.setDescription("Configuration for hydraulic leakage testing");
    config.setPressureRange(100.0, 150.0);
    config.setFlowRange(-5.0, 5.0);
    config.setTestDuration(300000); // 5 minutes
    config.setAcquisitionRate(10); // Lower rate for long test
    return config;
}

TestConfiguration TestConfiguration::createCycleTest()
{
    TestConfiguration config;
    config.setName("Cycle Test Configuration");
    config.setDescription("Configuration for hydraulic cycle testing");
    config.setPressureRange(0.0, 200.0);
    config.setFlowRange(-100.0, 100.0);
    config.setTestDuration(600000); // 10 minutes
    return config;
}

TestConfiguration TestConfiguration::createFromJson(const QJsonObject& json)
{
    TestConfiguration config;
    config.fromJson(json);
    return config;
}

void TestConfiguration::connectSignals()
{
    // Connect property change signals to validation
    connect(this, &TestConfiguration::pressureMinChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &TestConfiguration::pressureMaxChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &TestConfiguration::flowMinChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &TestConfiguration::flowMaxChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &TestConfiguration::acquisitionRateChanged, this, [this]() { validateAndEmitSignals(); });
    connect(this, &TestConfiguration::testDurationChanged, this, [this]() { validateAndEmitSignals(); });
}

void TestConfiguration::validateAndEmitSignals()
{
    bool valid = isValid();
    emit validationChanged(valid);
    
    if (!valid) {
        QStringList errors = validate();
        qCWarning(testConfigLog) << "TestConfiguration validation failed:" << errors;
    }
}

QJsonObject TestConfiguration::safetyThresholdsToJson() const
{
    QJsonObject json;
    json["max_pressure"] = m_safetyThresholds.maxPressure;
    json["max_temperature"] = m_safetyThresholds.maxTemperature;
    json["max_flow"] = m_safetyThresholds.maxFlow;
    json["max_velocity"] = m_safetyThresholds.maxVelocity;
    json["max_force"] = m_safetyThresholds.maxForce;
    json["min_pressure"] = m_safetyThresholds.minPressure;
    json["leakage_threshold"] = m_safetyThresholds.leakageThreshold;
    json["emergency_stop_delay"] = m_safetyThresholds.emergencyStopDelay;
    return json;
}

void TestConfiguration::safetyThresholdsFromJson(const QJsonObject& json)
{
    m_safetyThresholds.maxPressure = json["max_pressure"].toDouble(250.0);
    m_safetyThresholds.maxTemperature = json["max_temperature"].toDouble(80.0);
    m_safetyThresholds.maxFlow = json["max_flow"].toDouble(100.0);
    m_safetyThresholds.maxVelocity = json["max_velocity"].toDouble(200.0);
    m_safetyThresholds.maxForce = json["max_force"].toDouble(50000.0);
    m_safetyThresholds.minPressure = json["min_pressure"].toDouble(0.0);
    m_safetyThresholds.leakageThreshold = json["leakage_threshold"].toDouble(2.0);
    m_safetyThresholds.emergencyStopDelay = json["emergency_stop_delay"].toInt(1000);
}

QJsonObject TestConfiguration::acquisitionConfigToJson() const
{
    QJsonObject json;
    json["sample_rate"] = m_acquisitionConfig.sampleRate;
    json["buffer_size"] = m_acquisitionConfig.bufferSize;
    json["enable_filtering"] = m_acquisitionConfig.enableFiltering;
    json["filter_cutoff"] = m_acquisitionConfig.filterCutoff;
    json["enable_validation"] = m_acquisitionConfig.enableValidation;
    json["enable_logging"] = m_acquisitionConfig.enableLogging;
    json["log_format"] = m_acquisitionConfig.logFormat;
    return json;
}

void TestConfiguration::acquisitionConfigFromJson(const QJsonObject& json)
{
    m_acquisitionConfig.sampleRate = json["sample_rate"].toInt(100);
    m_acquisitionConfig.bufferSize = json["buffer_size"].toInt(10000);
    m_acquisitionConfig.enableFiltering = json["enable_filtering"].toBool(true);
    m_acquisitionConfig.filterCutoff = json["filter_cutoff"].toDouble(10.0);
    m_acquisitionConfig.enableValidation = json["enable_validation"].toBool(true);
    m_acquisitionConfig.enableLogging = json["enable_logging"].toBool(true);
    m_acquisitionConfig.logFormat = json["log_format"].toString("csv");
}

QJsonObject TestConfiguration::executionConfigToJson() const
{
    QJsonObject json;
    json["auto_start"] = m_executionConfig.autoStart;
    json["auto_stop"] = m_executionConfig.autoStop;
    json["pause_on_error"] = m_executionConfig.pauseOnError;
    json["save_results"] = m_executionConfig.saveResults;
    json["generate_report"] = m_executionConfig.generateReport;
    json["report_format"] = m_executionConfig.reportFormat;
    json["max_retries"] = m_executionConfig.maxRetries;
    json["retry_delay"] = m_executionConfig.retryDelay;
    return json;
}

void TestConfiguration::executionConfigFromJson(const QJsonObject& json)
{
    m_executionConfig.autoStart = json["auto_start"].toBool(false);
    m_executionConfig.autoStop = json["auto_stop"].toBool(true);
    m_executionConfig.pauseOnError = json["pause_on_error"].toBool(true);
    m_executionConfig.saveResults = json["save_results"].toBool(true);
    m_executionConfig.generateReport = json["generate_report"].toBool(true);
    m_executionConfig.reportFormat = json["report_format"].toString("pdf");
    m_executionConfig.maxRetries = json["max_retries"].toInt(3);
    m_executionConfig.retryDelay = json["retry_delay"].toInt(5000);
}
