#ifndef DATAVALIDATOR_H
#define DATAVALIDATOR_H

#include <QObject>
#include <QMutex>
#include <QSettings>
#include <QLoggingCategory>
#include <memory>

#include "models/TestData.h"
#include "models/TestConfiguration.h"

Q_DECLARE_LOGGING_CATEGORY(dataValidatorLog)

/**
 * @brief Data validation engine for hydraulic test bench system
 * 
 * This class provides comprehensive data validation capabilities including
 * sensor reading validation, safety limit checking, configurable validation
 * rules, and real-time validation feedback for the UI components.
 */
class DataValidator : public QObject
{
    Q_OBJECT
    
public:
    /**
     * @brief Validation rule configuration
     */
    struct ValidationRule {
        QString parameterName;          ///< Parameter to validate
        double minValue = -1e6;         ///< Minimum allowed value
        double maxValue = 1e6;          ///< Maximum allowed value
        double maxRateOfChange = 1e6;   ///< Maximum rate of change per second
        bool enableRangeCheck = true;   ///< Enable range validation
        bool enableRateCheck = true;    ///< Enable rate of change validation
        bool enableCorrelationCheck = false; ///< Enable correlation validation
        QString correlatedParameter;    ///< Parameter to correlate with
        double correlationFactor = 1.0; ///< Expected correlation factor
        double correlationTolerance = 0.1; ///< Correlation tolerance
        bool isCritical = false;        ///< Critical validation (stops test on failure)
        QString description;            ///< Human-readable description
    };
    
    /**
     * @brief Validation result
     */
    struct ValidationResult {
        bool isValid = true;            ///< Overall validation result
        QStringList errors;             ///< List of validation errors
        QStringList warnings;           ///< List of validation warnings
        QStringList criticalErrors;    ///< List of critical errors
        int errorCount = 0;             ///< Total number of errors
        int warningCount = 0;           ///< Total number of warnings
        QDateTime validationTime;       ///< When validation was performed
        QString summary;                ///< Summary of validation results
    };
    
    /**
     * @brief Validation statistics
     */
    struct ValidationStatistics {
        int totalValidations = 0;       ///< Total validations performed
        int successfulValidations = 0;  ///< Successful validations
        int failedValidations = 0;      ///< Failed validations
        int criticalFailures = 0;       ///< Critical validation failures
        double successRate = 0.0;       ///< Success rate percentage
        QDateTime lastValidation;       ///< Last validation timestamp
        QMap<QString, int> errorCounts; ///< Error counts by type
    };
    
    explicit DataValidator(QObject *parent = nullptr);
    virtual ~DataValidator() = default;
    
    // Configuration management
    void addValidationRule(const ValidationRule& rule);
    void removeValidationRule(const QString& parameterName);
    void clearValidationRules();
    QList<ValidationRule> validationRules() const;
    ValidationRule getValidationRule(const QString& parameterName) const;
    bool hasValidationRule(const QString& parameterName) const;
    
    void setTestConfiguration(std::shared_ptr<TestConfiguration> config);
    std::shared_ptr<TestConfiguration> testConfiguration() const;
    
    // Main validation methods
    ValidationResult validateSensorReading(const QString& sensor, double value);
    ValidationResult validateTestData(const TestData& data);
    ValidationResult validateTestParameters(const TestConfiguration& config);
    ValidationResult validateBatch(const QList<TestData>& dataList);
    
    // Individual validation checks
    bool checkSafetyLimits(const TestData& data, QStringList& violations);
    bool checkDataIntegrity(const TestData& data, QStringList& errors);
    bool checkSensorCorrelation(const TestData& data, QStringList& correlationErrors);
    bool checkRateOfChange(const QString& parameter, double currentValue, double previousValue, 
                          double deltaTime, QStringList& rateErrors);
    
    // Validation rule management
    void loadValidationRules(const QString& filename);
    void saveValidationRules(const QString& filename) const;
    void loadDefaultRules();
    void createHydraulicValidationRules();
    
    // Statistics and monitoring
    ValidationStatistics getValidationStatistics() const;
    void resetValidationStatistics();
    double getValidationSuccessRate() const;
    QMap<QString, int> getErrorFrequency() const;
    
    // Configuration
    void setValidationEnabled(bool enabled);
    bool isValidationEnabled() const;
    void setStrictMode(bool strict);
    bool isStrictMode() const;
    void setValidationTolerance(double tolerance);
    double validationTolerance() const;
    
    // Thread safety
    void lockValidator();
    void unlockValidator();
    bool tryLockValidator(int timeoutMs = 1000);
    
signals:
    void validationCompleted(const ValidationResult& result);
    void validationFailed(const TestData& data, const QStringList& errors);
    void criticalValidationFailure(const TestData& data, const QStringList& criticalErrors);
    void safetyLimitViolation(const TestData& data, const QStringList& violations);
    void validationRulesChanged();
    void validationStatisticsUpdated(const ValidationStatistics& stats);
    
private slots:
    void onTestConfigurationChanged();
    
private:
    mutable QMutex m_mutex;
    QMap<QString, ValidationRule> m_validationRules;
    std::shared_ptr<TestConfiguration> m_testConfig;
    ValidationStatistics m_statistics;
    QMap<QString, double> m_previousValues;
    QMap<QString, QDateTime> m_previousTimestamps;
    
    // Configuration
    bool m_validationEnabled;
    bool m_strictMode;
    double m_validationTolerance;
    
    // Validation methods
    bool validateRange(const QString& parameter, double value, const ValidationRule& rule, QStringList& errors);
    bool validateRateOfChange(const QString& parameter, double value, const ValidationRule& rule, QStringList& errors);
    bool validateCorrelation(const TestData& data, const ValidationRule& rule, QStringList& errors);
    bool validateSafetyThresholds(const TestData& data, QStringList& violations);
    
    // Helper methods
    void updateValidationStatistics(const ValidationResult& result);
    void updatePreviousValues(const TestData& data);
    QString formatValidationError(const QString& parameter, const QString& error, double value) const;
    QString formatValidationSummary(const ValidationResult& result) const;
    
    // Default validation rules
    ValidationRule createPressureValidationRule(const QString& parameterName, double minPressure, double maxPressure);
    ValidationRule createFlowValidationRule(const QString& parameterName, double minFlow, double maxFlow);
    ValidationRule createTemperatureValidationRule(const QString& parameterName, double maxTemperature);
    ValidationRule createPositionValidationRule(const QString& parameterName, double minPosition, double maxPosition);
    ValidationRule createVelocityValidationRule(const QString& parameterName, double maxVelocity);
    ValidationRule createForceValidationRule(const QString& parameterName, double maxForce);
    
    // Serialization helpers
    QJsonObject validationRuleToJson(const ValidationRule& rule) const;
    ValidationRule validationRuleFromJson(const QJsonObject& json) const;
    
    // Constants
    static const double DEFAULT_VALIDATION_TOLERANCE;
    static const double DEFAULT_MAX_RATE_OF_CHANGE;
    static const int VALIDATION_HISTORY_SIZE;
};

#endif // DATAVALIDATOR_H
