cmake_minimum_required(VERSION 3.16)

project(Logiciel VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets Network Charts SerialPort Concurrent)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets Network Charts SerialPort Concurrent)

set(PROJECT_SOURCES
        main.cpp
        mainwindow.cpp
        mainwindow.h
        mainwindow.ui

        # Network layer
        network/ApiClient.cpp
        network/ApiClient.h
        network/AuthManager.cpp
        network/AuthManager.h
        network/ApiEndpoints.cpp
        network/ApiEndpoints.h

        # Data models
        models/ApiResponse.cpp
        models/ApiResponse.h
        models/NetworkError.cpp
        models/NetworkError.h
        models/User.cpp
        models/User.h
        models/Affaire.cpp
        models/Affaire.h
        models/Essai.cpp
        models/Essai.h
        models/Courbe.cpp
        models/Courbe.h
        models/TestData.cpp
        models/TestData.h
        models/TestConfiguration.cpp
        models/TestConfiguration.h

        # Hardware simulation
        models/HardwareInterface.cpp
        models/HardwareInterface.h
        models/SimulatedSensor.cpp
        models/SimulatedSensor.h
        models/PressureSensor.cpp
        models/PressureSensor.h
        models/FlowMeter.cpp
        models/FlowMeter.h
        models/TemperatureSensor.cpp
        models/TemperatureSensor.h
        models/ActuatorControl.cpp
        models/ActuatorControl.h

        # Utilities
        utils/DataProcessor.cpp
        utils/DataProcessor.h
        utils/DataValidator.cpp
        utils/DataValidator.h
        utils/DataBuffer.cpp
        utils/DataBuffer.h
        utils/HydraulicCalculations.cpp
        utils/HydraulicCalculations.h
        utils/TestSessionManager.cpp
        utils/TestSessionManager.h
        utils/DataAcquisitionThread.cpp
        utils/DataAcquisitionThread.h
        utils/ApiRequestThread.cpp
        utils/ApiRequestThread.h
        utils/ThreadSafeQueue.h

        # Widgets
        widgets/SimulationControlWidget.cpp
        widgets/SimulationControlWidget.h
        widgets/TestControlPanel.cpp
        widgets/TestControlPanel.h
        widgets/RealTimeChart.cpp
        widgets/RealTimeChart.h
        widgets/TestConfigurationWidget.cpp
        widgets/TestConfigurationWidget.h
        widgets/DataDisplayWidget.cpp
        widgets/DataDisplayWidget.h
        widgets/StatusIndicatorWidget.cpp
        widgets/StatusIndicatorWidget.h
)

if (${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(Logiciel
            MANUAL_FINALIZATION
            ${PROJECT_SOURCES}
    )
    # Define target properties for Android with Qt 6 as:
    #    set_property(TARGET Logiciel APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
    #                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
    # For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else ()
    if (ANDROID)
        add_library(Logiciel SHARED
                ${PROJECT_SOURCES}
        )
        # Define properties for Android with Qt 5 after find_package() calls as:
        #    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else ()
        add_executable(Logiciel
                ${PROJECT_SOURCES}
        )
    endif ()
endif ()

target_link_libraries(Logiciel PRIVATE
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
    Qt${QT_VERSION_MAJOR}::Charts
    Qt${QT_VERSION_MAJOR}::SerialPort
    Qt${QT_VERSION_MAJOR}::Concurrent
)

# Add include directories for relative includes
target_include_directories(Logiciel PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if (${QT_VERSION} VERSION_LESS 6.1.0)
    set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.Logiciel)
endif ()
set_target_properties(Logiciel PROPERTIES
        ${BUNDLE_ID_OPTION}
        MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
        MACOSX_BUNDLE TRUE
        WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS Logiciel
        BUNDLE DESTINATION .
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if (QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(Logiciel)
endif ()
