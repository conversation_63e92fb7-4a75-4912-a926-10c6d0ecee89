#ifndef ESSAI_H
#define ESSAI_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(essaiModelLog)

/**
 * @brief Test case model for hydraulic test bench system
 * 
 * This class represents a single test case (essai) within a business case (affaire).
 * Each essai contains test parameters, execution results, and status information
 * for hydraulic cylinder testing procedures.
 */
class Essai : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(int affaireId READ affaireId WRITE setAffaireId NOTIFY affaireIdChanged)
    Q_PROPERTY(QString typeEssai READ typeEssai WRITE setTypeEssai NOTIFY typeEssaiChanged)
    Q_PROPERTY(QJsonObject parametres READ parametres WRITE setParametres NOTIFY parametresChanged)
    Q_PROPERTY(QJsonObject resultats READ resultats WRITE setResultats NOTIFY resultatsChanged)
    Q_PROPERTY(QString statut READ statut WRITE setStatut NOTIFY statutChanged)
    Q_PROPERTY(QDateTime dateExecution READ dateExecution WRITE setDateExecution NOTIFY dateExecutionChanged)
    
public:
    /**
     * @brief Test types for hydraulic cylinder testing
     */
    enum class TestType {
        PressureTest,       ///< Static pressure test
        FlowTest,           ///< Flow rate measurement test
        LeakageTest,        ///< Internal and external leakage test
        CycleTest,          ///< Endurance cycling test
        PerformanceTest,    ///< Overall performance evaluation
        CalibrationTest,    ///< Sensor calibration verification
        SafetyTest          ///< Safety system verification
    };
    Q_ENUM(TestType)
    
    /**
     * @brief Test execution status
     */
    enum class Status {
        Pending,        ///< Test configured but not started
        Running,        ///< Test currently executing
        Paused,         ///< Test temporarily paused
        Completed,      ///< Test completed successfully
        Failed,         ///< Test failed due to error
        Cancelled,      ///< Test cancelled by user
        Archived        ///< Test completed and archived
    };
    Q_ENUM(Status)
    
    explicit Essai(QObject *parent = nullptr);
    Essai(const Essai& other);
    Essai& operator=(const Essai& other);
    virtual ~Essai() = default;
    
    // Property accessors
    int id() const { return m_id; }
    void setId(int id);
    
    int affaireId() const { return m_affaireId; }
    void setAffaireId(int affaireId);
    
    QString typeEssai() const { return m_typeEssai; }
    void setTypeEssai(const QString& typeEssai);
    
    QJsonObject parametres() const { return m_parametres; }
    void setParametres(const QJsonObject& parametres);
    
    QJsonObject resultats() const { return m_resultats; }
    void setResultats(const QJsonObject& resultats);
    
    QString statut() const { return m_statut; }
    void setStatut(const QString& statut);
    
    QDateTime dateExecution() const { return m_dateExecution; }
    void setDateExecution(const QDateTime& dateExecution);
    
    // Type and status management
    TestType testType() const;
    void setTestType(TestType type);
    QString testTypeDisplayName() const;
    static QString testTypeToString(TestType type);
    static TestType testTypeFromString(const QString& typeStr);
    
    Status essaiStatus() const;
    void setEssaiStatus(Status status);
    QString statusDisplayName() const;
    static QString statusToString(Status status);
    static Status statusFromString(const QString& statusStr);
    
    // Parameter management
    void setParameter(const QString& key, const QVariant& value);
    QVariant parameter(const QString& key, const QVariant& defaultValue = QVariant()) const;
    QStringList parameterKeys() const;
    bool hasParameter(const QString& key) const;
    void removeParameter(const QString& key);
    
    // Result management
    void setResult(const QString& key, const QVariant& value);
    QVariant result(const QString& key, const QVariant& defaultValue = QVariant()) const;
    QStringList resultKeys() const;
    bool hasResult(const QString& key) const;
    void removeResult(const QString& key);
    void clearResults();
    
    // Test execution state
    bool canBeStarted() const;
    bool canBePaused() const;
    bool canBeResumed() const;
    bool canBeStopped() const;
    bool canBeModified() const;
    bool canBeDeleted() const;
    bool isRunning() const;
    bool isCompleted() const;
    bool hasFailed() const;
    
    // Validation
    bool isValid() const;
    QStringList validate() const;
    bool areParametersValid() const;
    bool isTypeValid() const;
    bool isStatusValid() const;
    
    // JSON serialization
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    QJsonObject toJsonPartial() const; // For API updates
    
    // Utility methods
    QString displayName() const;
    QString shortDescription() const;
    int durationMinutes() const;
    QString formattedExecutionDate() const;
    double progressPercentage() const;
    
    // Operators
    bool operator==(const Essai& other) const;
    bool operator!=(const Essai& other) const { return !(*this == other); }
    
    // Static factory methods
    static Essai createFromJson(const QJsonObject& json);
    static Essai createNew(int affaireId, TestType type);
    static QStringList getAllTestTypes();
    static QStringList getAllStatuses();
    static QJsonObject getDefaultParameters(TestType type);
    
signals:
    void idChanged(int id);
    void affaireIdChanged(int affaireId);
    void typeEssaiChanged(const QString& typeEssai);
    void parametresChanged(const QJsonObject& parametres);
    void resultatsChanged(const QJsonObject& resultats);
    void statutChanged(const QString& statut);
    void dateExecutionChanged(const QDateTime& dateExecution);
    void essaiDataChanged();
    void validationChanged(bool isValid);
    void statusChanged(Status oldStatus, Status newStatus);
    void parameterChanged(const QString& key, const QVariant& value);
    void resultChanged(const QString& key, const QVariant& value);
    
private:
    int m_id;
    int m_affaireId;
    QString m_typeEssai;
    QJsonObject m_parametres;
    QJsonObject m_resultats;
    QString m_statut;
    QDateTime m_dateExecution;
    
    void connectSignals();
    void validateAndEmitSignals();
    bool isValidTestType(const QString& type) const;
    bool isValidStatus(const QString& status) const;
    QJsonObject validateParameters(const QJsonObject& params) const;
    
    // Constants
    static const QStringList REQUIRED_PRESSURE_PARAMS;
    static const QStringList REQUIRED_FLOW_PARAMS;
    static const QStringList REQUIRED_LEAKAGE_PARAMS;
};

#endif // ESSAI_H
