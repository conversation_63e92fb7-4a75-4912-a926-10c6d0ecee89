#ifndef DATADISPLAYWIDGET_H
#define DATADISPLAYWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLCDNumber>
#include <QProgressBar>
#include <QPushButton>
#include <QComboBox>
#include <QCheckBox>
#include <QTimer>
#include <QTextEdit>
#include <QFrame>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QSlider>
#include <QDial>
#include <QLoggingCategory>
#include <QMutex>
#include <QDateTime>
#include <QFileDialog>
#include <QStandardPaths>

#include "models/TestData.h"
#include "utils/HydraulicCalculations.h"

Q_DECLARE_LOGGING_CATEGORY(dataDisplayLog)

/**
 * @brief Real-time display widget for hydraulic system data
 * 
 * This widget provides a comprehensive real-time display of all hydraulic
 * system parameters including pressures, flows, temperatures, actuator
 * status, and system diagnostics. Features configurable units, data
 * logging, and professional visual indicators.
 */
class DataDisplayWidget : public QWidget
{
    Q_OBJECT
    
public:
    enum class DisplayUnits {
        Metric,     // bar, L/min, °C, mm, N
        Imperial,   // PSI, GPM, °F, inch, lbf
        Mixed       // User configurable
    };
    
    enum class DataQuality {
        Excellent,  // < 1% data loss
        Good,       // 1-5% data loss
        Fair,       // 5-10% data loss
        Poor        // > 10% data loss
    };
    
    explicit DataDisplayWidget(QWidget *parent = nullptr);
    ~DataDisplayWidget();
    
    // Configuration
    void setDisplayUnits(DisplayUnits units);
    DisplayUnits displayUnits() const { return m_displayUnits; }
    
    void setUpdateInterval(int intervalMs);
    int updateInterval() const { return m_updateInterval; }
    
    void setDataLoggingEnabled(bool enabled);
    bool isDataLoggingEnabled() const { return m_dataLoggingEnabled; }
    
    void setAlarmThresholds(const QMap<QString, QPair<double, double>>& thresholds);
    QMap<QString, QPair<double, double>> alarmThresholds() const { return m_alarmThresholds; }
    
    // Data access
    TestData currentData() const;
    QList<TestData> dataHistory(int maxSamples = 1000) const;
    
    // Statistics
    DataQuality currentDataQuality() const { return m_dataQuality; }
    double actualUpdateRate() const { return m_actualUpdateRate; }
    int totalSamplesReceived() const { return m_totalSamples; }
    int droppedSamples() const { return m_droppedSamples; }
    
public slots:
    void updateData(const TestData& data);
    void clearData();
    void resetStatistics();
    void exportData();
    void saveConfiguration();
    void loadConfiguration();
    
signals:
    void dataUpdated(const TestData& data);
    void alarmTriggered(const QString& parameter, double value, const QString& condition);
    void dataQualityChanged(DataQuality quality);
    void configurationChanged();
    void exportCompleted(const QString& filename);
    
private slots:
    void onUpdateTimer();
    void onUnitsChanged();
    void onThresholdChanged();
    void onLoggingToggled(bool enabled);
    void onExportClicked();
    void onResetStatsClicked();
    void onConfigureAlarmsClicked();
    
private:
    // UI setup
    void setupUI();
    void setupPressureDisplay();
    void setupFlowDisplay();
    void setupTemperatureDisplay();
    void setupActuatorDisplay();
    void setupSystemStatusDisplay();
    void setupControlPanel();
    void setupDataLogging();
    
    // Display groups
    QGroupBox* createPressureGroup();
    QGroupBox* createFlowGroup();
    QGroupBox* createTemperatureGroup();
    QGroupBox* createActuatorGroup();
    QGroupBox* createSystemStatusGroup();
    QGroupBox* createStatisticsGroup();
    
    // Value display widgets
    QWidget* createValueDisplay(const QString& label, const QString& units,
                               QLCDNumber*& lcdDisplay, QProgressBar*& progressBar,
                               QLabel** statusLabel = nullptr);
    
    // Update methods
    void updatePressureDisplays(const TestData& data);
    void updateFlowDisplays(const TestData& data);
    void updateTemperatureDisplays(const TestData& data);
    void updateActuatorDisplays(const TestData& data);
    void updateSystemStatus(const TestData& data);
    void updateStatistics();
    
    // Unit conversion
    double convertPressure(double pressure) const;
    double convertFlow(double flow) const;
    double convertTemperature(double temperature) const;
    double convertPosition(double position) const;
    double convertVelocity(double velocity) const;
    double convertForce(double force) const;
    
    QString getPressureUnit() const;
    QString getFlowUnit() const;
    QString getTemperatureUnit() const;
    QString getPositionUnit() const;
    QString getVelocityUnit() const;
    QString getForceUnit() const;
    
    // Alarm checking
    void checkAlarms(const TestData& data);
    void setAlarmStatus(QLabel* statusLabel, bool alarm, const QString& message = QString());
    
    // Data validation
    bool validateData(const TestData& data) const;
    void updateDataQuality();
    
    // Styling
    void applyProfessionalStyling();
    void updateDisplayColors();
    QString getStatusColor(double value, double min, double max) const;
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    
    // Display groups
    QGroupBox* m_pressureGroup;
    QGroupBox* m_flowGroup;
    QGroupBox* m_temperatureGroup;
    QGroupBox* m_actuatorGroup;
    QGroupBox* m_systemStatusGroup;
    QGroupBox* m_statisticsGroup;
    
    // Pressure displays
    QLCDNumber* m_pressureCPALCD;
    QLCDNumber* m_pressureCPBLCD;
    QLCDNumber* m_pressureSupplyLCD;
    QLCDNumber* m_pressureReturnLCD;
    QProgressBar* m_pressureCPABar;
    QProgressBar* m_pressureCPBBar;
    QProgressBar* m_pressureSupplyBar;
    QProgressBar* m_pressureReturnBar;
    QLabel* m_pressureCPAStatus;
    QLabel* m_pressureCPBStatus;
    QLabel* m_pressureSupplyStatus;
    QLabel* m_pressureReturnStatus;
    
    // Flow displays
    QLCDNumber* m_flowRateLCD;
    QProgressBar* m_flowRateBar;
    QLabel* m_flowDirectionLabel;
    QLabel* m_flowStatusLabel;
    QDial* m_flowDirectionDial;
    
    // Temperature displays
    QLCDNumber* m_tempFluidLCD;
    QLCDNumber* m_tempAmbientLCD;
    QProgressBar* m_tempFluidBar;
    QProgressBar* m_tempAmbientBar;
    QLabel* m_tempFluidStatus;
    QLabel* m_tempAmbientStatus;
    QLabel* m_tempTrendLabel;
    
    // Actuator displays
    QLCDNumber* m_actuatorPositionLCD;
    QLCDNumber* m_actuatorVelocityLCD;
    QLCDNumber* m_actuatorForceLCD;
    QProgressBar* m_actuatorPositionBar;
    QProgressBar* m_actuatorVelocityBar;
    QProgressBar* m_actuatorForceBar;
    QLabel* m_actuatorStatusLabel;
    QSlider* m_actuatorPositionSlider;
    
    // System status displays
    QLabel* m_systemStatusLabel;
    QLabel* m_dataQualityLabel;
    QLabel* m_updateRateLabel;
    QLabel* m_totalSamplesLabel;
    QLabel* m_droppedSamplesLabel;
    QLabel* m_lastUpdateLabel;
    QProgressBar* m_dataQualityBar;
    
    // Control panel
    QComboBox* m_unitsComboBox;
    QCheckBox* m_loggingCheckBox;
    QPushButton* m_exportButton;
    QPushButton* m_resetStatsButton;
    QPushButton* m_configureAlarmsButton;
    QSpinBox* m_updateIntervalSpinBox;
    
    // Data logging
    QTextEdit* m_logTextEdit;
    QPushButton* m_clearLogButton;
    QPushButton* m_saveLogButton;
    
    // Configuration and state
    DisplayUnits m_displayUnits;
    int m_updateInterval;
    bool m_dataLoggingEnabled;
    QMap<QString, QPair<double, double>> m_alarmThresholds;
    
    // Data management
    mutable QMutex m_dataMutex;
    TestData m_currentData;
    QList<TestData> m_dataHistory;
    static const int MAX_HISTORY_SIZE = 10000;
    
    // Statistics
    DataQuality m_dataQuality;
    double m_actualUpdateRate;
    int m_totalSamples;
    int m_droppedSamples;
    QDateTime m_lastUpdateTime;
    QList<qint64> m_updateTimes;
    static const int RATE_CALCULATION_SAMPLES = 50;
    
    // Timers
    QTimer* m_updateTimer;
    QTimer* m_statisticsTimer;
    
    // Constants
    static const int DEFAULT_UPDATE_INTERVAL_MS = 100;
    static const int STATISTICS_UPDATE_INTERVAL_MS = 1000;
    static const double PRESSURE_MIN;
    static const double PRESSURE_MAX;
    static const double FLOW_MIN;
    static const double FLOW_MAX;
    static const double TEMPERATURE_MIN;
    static const double TEMPERATURE_MAX;
    static const double POSITION_MIN;
    static const double POSITION_MAX;
    static const double VELOCITY_MIN;
    static const double VELOCITY_MAX;
    static const double FORCE_MIN;
    static const double FORCE_MAX;
};

#endif // DATADISPLAYWIDGET_H
