#ifndef SIMULATIONCONTROLWIDGET_H
#define SIMULATIONCONTROLWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QSlider>
#include <QProgressBar>
#include <QTextEdit>
#include <QTimer>
#include <memory>

#include "models/PressureSensor.h"
#include "models/FlowMeter.h"
#include "models/TemperatureSensor.h"
#include "models/ActuatorControl.h"

/**
 * @brief Widget for controlling hardware simulation
 * 
 * This widget provides a comprehensive interface for controlling
 * all simulated hardware devices, including manual data input,
 * automatic data generation, and test scenario execution.
 */
class SimulationControlWidget : public QWidget
{
    Q_OBJECT
    
public:
    explicit SimulationControlWidget(QWidget *parent = nullptr);
    ~SimulationControlWidget();
    
    // Hardware device management
    void addPressureSensor(std::shared_ptr<PressureSensor> sensor);
    void addFlowMeter(std::shared_ptr<FlowMeter> flowMeter);
    void addTemperatureSensor(std::shared_ptr<TemperatureSensor> tempSensor);
    void setActuatorControl(std::shared_ptr<ActuatorControl> actuator);
    
    void removeAllDevices();
    
    // Simulation control
    void startSimulation();
    void stopSimulation();
    void pauseSimulation();
    void resumeSimulation();
    
    bool isSimulationRunning() const { return m_simulationRunning; }
    
    // Manual control mode
    void enableManualControl(bool enabled);
    bool isManualControlEnabled() const { return m_manualControlEnabled; }
    
signals:
    void simulationStarted();
    void simulationStopped();
    void simulationPaused();
    void simulationResumed();
    void manualControlToggled(bool enabled);
    void testScenarioStarted(const QString& scenarioName);
    void testScenarioCompleted(const QString& scenarioName);
    
private slots:
    // UI control slots
    void onStartStopClicked();
    void onPauseResumeClicked();
    void onManualControlToggled(bool enabled);
    void onResetSimulationClicked();
    
    // Manual control slots
    void onPressureValueChanged();
    void onFlowValueChanged();
    void onTemperatureValueChanged();
    void onActuatorControlChanged();
    
    // Test scenario slots
    void onRunPressureTestClicked();
    void onRunFlowTestClicked();
    void onRunTemperatureTestClicked();
    void onRunCycleTestClicked();
    void onRunLeakTestClicked();
    
    // Device status updates
    void onDeviceStatusChanged();
    void updateDeviceStatus();
    
private:
    // UI setup
    void setupUI();
    void setupSimulationControls();
    void setupManualControls();
    void setupTestScenarios();
    void setupDeviceStatus();
    
    // Manual control groups
    QGroupBox* createPressureControlGroup();
    QGroupBox* createFlowControlGroup();
    QGroupBox* createTemperatureControlGroup();
    QGroupBox* createActuatorControlGroup();
    
    // Test scenario groups
    QGroupBox* createPressureTestGroup();
    QGroupBox* createFlowTestGroup();
    QGroupBox* createTemperatureTestGroup();
    QGroupBox* createSystemTestGroup();
    
    // Utility methods
    void updateUIState();
    void connectDeviceSignals();
    void disconnectDeviceSignals();
    void applyManualValues();
    void resetToDefaults();
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    
    // Simulation control widgets
    QGroupBox* m_simulationControlGroup;
    QPushButton* m_startStopButton;
    QPushButton* m_pauseResumeButton;
    QPushButton* m_resetButton;
    QCheckBox* m_manualControlCheckBox;
    QLabel* m_simulationStatusLabel;
    QProgressBar* m_simulationProgressBar;
    
    // Manual control widgets
    QGroupBox* m_manualControlGroup;
    
    // Pressure controls
    QDoubleSpinBox* m_pressureCPASpinBox;
    QDoubleSpinBox* m_pressureCPBSpinBox;
    QDoubleSpinBox* m_pressureSupplySpinBox;
    QDoubleSpinBox* m_pressureReturnSpinBox;
    QSlider* m_pressureCPASlider;
    QSlider* m_pressureCPBSlider;
    
    // Flow controls
    QDoubleSpinBox* m_flowRateSpinBox;
    QSlider* m_flowRateSlider;
    QComboBox* m_flowDirectionComboBox;
    
    // Temperature controls
    QDoubleSpinBox* m_temperatureFluidSpinBox;
    QDoubleSpinBox* m_temperatureAmbientSpinBox;
    QSlider* m_temperatureFluidSlider;
    
    // Actuator controls
    QDoubleSpinBox* m_actuatorPositionSpinBox;
    QDoubleSpinBox* m_actuatorVelocitySpinBox;
    QDoubleSpinBox* m_actuatorForceSpinBox;
    QComboBox* m_actuatorModeComboBox;
    QPushButton* m_actuatorExtendButton;
    QPushButton* m_actuatorRetractButton;
    QPushButton* m_actuatorStopButton;
    QPushButton* m_actuatorEmergencyStopButton;
    
    // Test scenario widgets
    QGroupBox* m_testScenariosGroup;
    
    // Pressure test controls
    QDoubleSpinBox* m_pressureTestTargetSpinBox;
    QSpinBox* m_pressureTestDurationSpinBox;
    QPushButton* m_runPressureTestButton;
    
    // Flow test controls
    QDoubleSpinBox* m_flowTestTargetSpinBox;
    QDoubleSpinBox* m_flowTestDistanceSpinBox;
    QPushButton* m_runFlowTestButton;
    
    // Temperature test controls
    QDoubleSpinBox* m_tempTestTargetSpinBox;
    QSpinBox* m_tempTestDurationSpinBox;
    QPushButton* m_runTempTestButton;
    
    // Cycle test controls
    QDoubleSpinBox* m_cycleTestMinSpinBox;
    QDoubleSpinBox* m_cycleTestMaxSpinBox;
    QSpinBox* m_cycleTestCountSpinBox;
    QDoubleSpinBox* m_cycleTestVelocitySpinBox;
    QPushButton* m_runCycleTestButton;
    
    // Leak test controls
    QDoubleSpinBox* m_leakTestPressureSpinBox;
    QDoubleSpinBox* m_leakTestRateSpinBox;
    QPushButton* m_runLeakTestButton;
    
    // Device status widgets
    QGroupBox* m_deviceStatusGroup;
    QTextEdit* m_statusTextEdit;
    QTimer* m_statusUpdateTimer;
    
    // Hardware devices
    QList<std::shared_ptr<PressureSensor>> m_pressureSensors;
    std::shared_ptr<FlowMeter> m_flowMeter;
    QList<std::shared_ptr<TemperatureSensor>> m_temperatureSensors;
    std::shared_ptr<ActuatorControl> m_actuatorControl;
    
    // State
    bool m_simulationRunning;
    bool m_simulationPaused;
    bool m_manualControlEnabled;
    
    // Constants
    static const double PRESSURE_MIN;
    static const double PRESSURE_MAX;
    static const double FLOW_MIN;
    static const double FLOW_MAX;
    static const double TEMPERATURE_MIN;
    static const double TEMPERATURE_MAX;
    static const double POSITION_MIN;
    static const double POSITION_MAX;
    static const double VELOCITY_MIN;
    static const double VELOCITY_MAX;
    static const double FORCE_MIN;
    static const double FORCE_MAX;
};

#endif // SIMULATIONCONTROLWIDGET_H
