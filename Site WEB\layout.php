<!DOCTYPE html>
<html lang="fr" class="h-full">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord - FluidMotion Labs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css" rel="stylesheet"/>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/themes/light.css"/>
    <link rel="stylesheet" href="/assets/css/print.css" media="print"/>
    <script src="/assets/js/print.js" defer></script>
    <style>
        .back-to-top {
            display: none;
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 99;
            transition: opacity 0.3s;
        }

        .tippy-box[data-theme~='custom'] {
            background-color: #1e293b;
            color: #fff;
        }

        .tippy-box[data-theme~='custom'] .tippy-arrow {
            color: #1e293b;
        }

        /* Animation pour le bouton de changement de thème */
        .theme-toggle-icon {
            transition: transform 0.5s ease;
        }

        .theme-toggle:hover .theme-toggle-icon {
            transform: rotate(45deg);
        }
    </style>
    <script>
        // Vérifie le thème préféré de l'utilisateur au chargement
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 min-h-full flex flex-col">

<nav class="fixed top-0 z-50 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
    <div class="px-3 py-3 lg:px-5 lg:pl-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center justify-start rtl:justify-end">
                <a href="/index.php" class="flex ms-2 md:me-24">
                    <span class="self-center text-xl font-semibold sm:text-2xl whitespace-nowrap dark:text-white">FluidMotion Labs</span>
                </a>
            </div>

            <div class="hidden sm:flex w-full justify-between px-4">
                <?php $navItemClass = isset($_SESSION['user']) && $_SESSION['user']['role'] === 'controleur' ? 'w-1/5' : 'w-1/4'; ?>
                <a href="/index.php"
                   class="flex items-center justify-center text-base font-medium rounded-md py-3 px-5 text-gray-700 bg-gray-100 hover:bg-blue-500 hover:text-white transition-all duration-200 <?php echo $navItemClass; ?> mx-1 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-blue-600">
                    <svg class="w-5 h-5 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                         viewBox="0 0 24 24">
                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                    </svg>
                    Accueil
                </a>
                <a href="/affaires.php"
                   class="flex items-center justify-center text-base font-medium rounded-md py-3 px-5 text-gray-700 bg-gray-100 hover:bg-blue-500 hover:text-white transition-all duration-200 <?php echo $navItemClass; ?> mx-1 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-blue-600">
                    <svg class="w-5 h-5 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                         viewBox="0 0 24 24">
                        <path fill-rule="evenodd"
                              d="M6 2a2 2 0 0 0-2 2v15a3 3 0 0 0 3 3h12a1 1 0 1 0 0-2h-2v-2h2a1 1 0 0 0 1-1V4a2 2 0 0 0-2-2h-8v16h5v2H7a1 1 0 1 1 0-2h1V2H6Z"
                              clip-rule="evenodd"/>
                    </svg>
                    Affaires
                </a>
                <a href="/essais.php"
                   class="flex items-center justify-center text-base font-medium rounded-md py-3 px-5 text-gray-700 bg-gray-100 hover:bg-blue-500 hover:text-white transition-all duration-200 <?php echo $navItemClass; ?> mx-1 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-blue-600">
                    <svg class="w-5 h-5 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                         height="24" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 4v15a1 1 0 0 0 1 1h15M8 16l2.5-5.5 3 3L17.273 7 20 9.667"/>
                    </svg>
                    Essais
                </a>
                <a href="/pv.php"
                   class="flex items-center justify-center text-base font-medium rounded-md py-3 px-5 text-gray-700 bg-gray-100 hover:bg-blue-500 hover:text-white transition-all duration-200 <?php echo $navItemClass; ?> mx-1 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-blue-600">
                    <svg class="w-5 h-5 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                         height="24" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2.038 5.61A2.01 2.01 0 0 0 2 6v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6c0-.12-.01-.238-.03-.352l-.866.65-7.89 6.032a2 2 0 0 1-2.429 0L2.884 6.288l-.846-.677Z"/>
                        <path d="M20.677 4.117A1.996 1.996 0 0 0 20 4H4c-.225 0-.44.037-.642.105l.758.607L12 10.742 19.9 4.7l.777-.583Z"/>
                    </svg>
                    PV
                </a>

            </div>

            <div class="flex items-center">
                <!-- Bouton de basculement du thème -->
                <button id="theme-toggle" type="button"
                        class="theme-toggle text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 me-2">
                    <svg id="theme-toggle-dark-icon" class="theme-toggle-icon hidden w-5 h-5" fill="currentColor"
                         viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <svg id="theme-toggle-light-icon" class="theme-toggle-icon hidden w-5 h-5" fill="currentColor"
                         viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                              fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <div class="flex items-center ms-3">
                    <!-- Logo supprimé -->
                    <div>
                        <button type="button"
                                class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
                                aria-expanded="false" data-dropdown-toggle="dropdown-user">
                            <span class="sr-only">Menu utilisateur</span>
                            <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                      clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
                         id="dropdown-user">
                        <div class="px-4 py-3" role="none">
                            <p class="text-sm text-gray-900 dark:text-white" role="none">
                                <?php echo isset($_SESSION['user']) ? htmlspecialchars($_SESSION['user']['username']) : ''; ?>
                            </p>
                            <?php if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'controleur'): ?>
                                <p class="text-xs text-blue-600 dark:text-blue-400" role="none">
                                    Contrôleur • Outils avancés disponibles
                                </p>
                            <?php endif; ?>
                        </div>
                        <ul class="py-1" role="none">
                            <?php if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'controleur'): ?>
                                <li>
                                    <a href="/backup.php"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                       role="menuitem">
                                        <svg class="w-4 h-4 me-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                             height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd"
                                                  d="M12 3a1 1 0 0 1 .78.375l4 5A1 1 0 0 1 16 10h-3v9a1 1 0 1 1-2 0v-9H8a1 1 0 0 1-.78-1.625l4-5A1 1 0 0 1 12 3Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        Sauvegarde & Restauration
                                    </a>
                                </li>
                                <li>
                                    <a href="/users.php"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                       role="menuitem">
                                        <svg class="w-4 h-4 me-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                             height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd"
                                                  d="M12 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8ZM6 8a6 6 0 1 1 12 0A6 6 0 0 1 6 8ZM14 15a4 4 0 0 0-8 0v3h8v-3Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        Gestion des Utilisateurs
                                    </a>
                                </li>
                                <li>
                                    <a href="/password-policy.php"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                       role="menuitem">
                                        <svg class="w-4 h-4 me-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24"
                                             height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd"
                                                  d="M8 10V7a4 4 0 1 1 8 0v3h1a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h1Zm2-3a2 2 0 1 1 4 0v3h-4V7Zm2 6a1 1 0 0 1 1 1v3a1 1 0 1 1-2 0v-3a1 1 0 0 1 1-1Z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        Politique de Mots de Passe
                                    </a>
                                </li>
                                <li>
                                    <a href="/tests.php"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                       role="menuitem">
                                        <svg class="w-4 h-4 me-2 inline" aria-hidden="true"
                                             xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                             viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                  stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                        </svg>
                                        Tests & Diagnostic
                                    </a>
                                </li>
                                <li>
                                    <a href="/data-generator.php"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                       role="menuitem">
                                        <svg class="w-4 h-4 me-2 inline" aria-hidden="true"
                                             xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             fill="currentColor"
                                             viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                                        </svg>
                                        Générateur de Données
                                    </a>
                                </li>
                                <li>
                                    <hr class="my-1 border-gray-200 dark:border-gray-600">
                                </li>
                            <?php endif; ?>
                            <li>
                                <a href="/auth/logout.php"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                                   role="menuitem">Déconnexion</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<div class="flex-grow p-4">
    <div class="p-4 mt-14">
        <?php if (isset($pageContent)) echo $pageContent; ?>
    </div>
</div>

<!-- Bouton retour en haut -->
<button id="backToTop"
        class="fixed bottom-4 right-4 z-50 hidden p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
    <span class="sr-only">Retour en haut</span>
</button>

<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
<script src="/js/forms.js"></script>
<script>
    // Gestion du bouton retour en haut
    const backToTopButton = document.getElementById('backToTop');

    window.onscroll = function () {
        if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
            backToTopButton.style.display = "block";
        } else {
            backToTopButton.style.display = "none";
        }
    };

    backToTopButton.addEventListener('click', function () {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Raccourcis clavier
    document.addEventListener('keydown', function (e) {
        // Alt + H = Accueil
        if (e.altKey && e.key === 'h') {
            window.location.href = '/index.php';
        }
        // Alt + A = Affaires
        if (e.altKey && e.key === 'a') {
            window.location.href = '/affaires.php';
        }
        // Alt + E = Essais
        if (e.altKey && e.key === 'e') {
            window.location.href = '/essais.php';
        }
        // Alt + P = PV
        if (e.altKey && e.key === 'p') {
            window.location.href = '/pv.php';
        }
        // Raccourcis pour les contrôleurs uniquement
        <?php if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'controleur'): ?>
        // Alt + S = Sauvegarde (contrôleur uniquement)
        if (e.altKey && e.key === 's') {
            window.location.href = '/backup.php';
        }
        // Alt + U = Utilisateurs (contrôleur uniquement)
        if (e.altKey && e.key === 'u') {
            window.location.href = '/users.php';
        }
        // Alt + P = Politique de mots de passe (contrôleur uniquement)
        if (e.altKey && e.key === 'p') {
            window.location.href = '/password-policy.php';
        }
        // Alt + T = Tests (contrôleur uniquement)
        if (e.altKey && e.key === 't') {
            window.location.href = '/tests.php';
        }
        // Alt + G = Générateur de données (contrôleur uniquement)
        if (e.altKey && e.key === 'g') {
            window.location.href = '/data-generator.php';
        }
        <?php endif; ?>
    });

    // Gestion du changement de thème (clair/sombre)
    document.addEventListener('DOMContentLoaded', function () {
        var themeToggleBtn = document.getElementById('theme-toggle');
        var themeToggleDarkIcon = document.getElementById('theme-toggle-dark-icon');
        var themeToggleLightIcon = document.getElementById('theme-toggle-light-icon');

        // Affiche l'icône appropriée en fonction du thème actuel
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && document.documentElement.classList.contains('dark'))) {
            themeToggleLightIcon.classList.remove('hidden');
        } else {
            themeToggleDarkIcon.classList.remove('hidden');
        }

        // Change le thème lorsque l'utilisateur clique sur le bouton
        themeToggleBtn.addEventListener('click', function () {
            // Basculement de l'icône
            themeToggleDarkIcon.classList.toggle('hidden');
            themeToggleLightIcon.classList.toggle('hidden');

            // Si le thème est actuellement "light"
            if (localStorage.getItem('color-theme') === 'light' || (!('color-theme' in localStorage) && !document.documentElement.classList.contains('dark'))) {
                // Passage au thème sombre
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
            } else {
                // Passage au thème clair
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
            }
        });
    });
</script>
</body>

</html>